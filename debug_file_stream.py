#!/usr/bin/env python3
"""
调试文件流状态
"""

import requests
import json

def debug_file_stream():
    """调试文件流"""
    
    print("🔄 检查后端服务...")
    try:
        response = requests.get('http://localhost:8000/api/chat/health', timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接后端: {e}")
        return
    
    print("\n🔄 创建对话...")
    try:
        response = requests.post(
            'http://localhost:8000/api/chat/conversations',
            json={'title': '调试测试'},
            timeout=10
        )
        
        if response.status_code == 200:
            conversation = response.json()
            conversation_id = conversation['id']
            print(f"✅ 对话创建成功: ID={conversation_id}")
        else:
            print(f"❌ 创建对话失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 创建对话异常: {e}")
        return
    
    print("\n🔄 测试最小文件...")
    tiny_content = "Hello"
    
    try:
        files = {
            'files': ('tiny.txt', tiny_content.encode('utf-8'), 'text/plain')
        }
        
        data = {
            'message': '测试',
            'conversation_id': conversation_id
        }
        
        print(f"📤 发送请求...")
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            stream=True,
            timeout=30
        )
        
        print(f"📡 HTTP状态: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 请求成功，开始读取响应...")
            
            response_lines = []
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    response_lines.append(line_str)
                    print(f"📄 响应行: {line_str}")
                    
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]
                        if data_str == '[DONE]':
                            print("✅ 响应完成")
                            break
                        
                        try:
                            chunk = json.loads(data_str)
                            print(f"📊 解析的数据: {chunk}")
                        except Exception as parse_error:
                            print(f"⚠️ 解析失败: {parse_error}")
                    
                    # 限制输出行数
                    if len(response_lines) > 20:
                        print("... (输出截断)")
                        break
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 开始调试文件流状态")
    print("="*40)
    debug_file_stream()
    print("\n" + "="*40)
    print("🔍 调试完成")
