#!/usr/bin/env python3
"""
文件上传功能测试脚本

用于测试后端文件上传API的功能
"""

import asyncio
import aiohttp
import json
from pathlib import Path

async def test_file_upload():
    """测试文件上传功能"""
    
    # 创建测试文件
    test_file_path = Path("test_document.txt")
    test_content = """这是一个测试文档。

# 测试标题

这个文档用于测试文件上传功能。

## 功能列表
1. 文件上传
2. 文本提取
3. AI分析

```python
def hello_world():
    print("Hello, World!")
```

测试完成。
"""
    
    # 写入测试文件
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"✅ 创建测试文件: {test_file_path}")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 1. 创建对话
            print("\n🔄 创建新对话...")
            async with session.post(
                'http://localhost:8000/api/chat/conversations',
                json={'title': '文件上传测试'}
            ) as response:
                if response.status == 200:
                    conversation = await response.json()
                    conversation_id = conversation['id']
                    print(f"✅ 对话创建成功: ID={conversation_id}")
                else:
                    print(f"❌ 创建对话失败: {response.status}")
                    return
            
            # 2. 上传文件
            print("\n🔄 上传文件...")
            
            # 准备表单数据
            data = aiohttp.FormData()
            data.add_field('message', '请分析这个文档的内容')
            data.add_field('conversation_id', str(conversation_id))
            
            # 添加文件
            with open(test_file_path, 'rb') as f:
                data.add_field('files', f, filename=test_file_path.name, content_type='text/plain')
                
                async with session.post(
                    'http://localhost:8000/api/chat/stream-with-files',
                    data=data
                ) as response:
                    if response.status == 200:
                        print("✅ 文件上传成功，开始接收流式响应...")
                        
                        # 读取流式响应
                        async for line in response.content:
                            line = line.decode('utf-8').strip()
                            if line.startswith('data: '):
                                data_str = line[6:]  # 移除 'data: ' 前缀
                                
                                if data_str == '[DONE]':
                                    print("\n✅ 流式响应完成")
                                    break
                                
                                try:
                                    chunk = json.loads(data_str)
                                    if chunk.get('type') == 'content':
                                        print(chunk.get('content', ''), end='', flush=True)
                                    elif chunk.get('type') == 'complete':
                                        print(f"\n✅ AI响应完成，处理时间: {chunk.get('processing_time', 0):.2f}s")
                                    elif chunk.get('type') == 'error':
                                        print(f"\n❌ AI响应错误: {chunk.get('error')}")
                                except json.JSONDecodeError:
                                    print(f"\n⚠️ 无法解析数据: {data_str}")
                    else:
                        error_text = await response.text()
                        print(f"❌ 文件上传失败: {response.status}")
                        print(f"错误信息: {error_text}")
            
            # 3. 获取对话消息
            print("\n\n🔄 获取对话消息...")
            async with session.get(
                f'http://localhost:8000/api/chat/conversations/{conversation_id}/messages'
            ) as response:
                if response.status == 200:
                    messages = await response.json()
                    print(f"✅ 获取到 {len(messages)} 条消息")
                    
                    for msg in messages:
                        print(f"\n📝 消息 {msg['id']} ({msg['role']}):")
                        print(f"内容: {msg['content'][:100]}...")
                        
                        if msg.get('attachments'):
                            print(f"📎 附件: {len(msg['attachments'])} 个")
                            for att in msg['attachments']:
                                print(f"  - {att['filename']} ({att['file_size']} bytes)")
                else:
                    print(f"❌ 获取消息失败: {response.status}")
    
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    
    finally:
        # 清理测试文件
        if test_file_path.exists():
            test_file_path.unlink()
            print(f"\n🗑️ 已清理测试文件: {test_file_path}")

async def test_file_validation():
    """测试文件验证功能"""
    print("\n" + "="*50)
    print("测试文件验证功能")
    print("="*50)
    
    # 创建一个大文件来测试大小限制
    large_file_path = Path("large_test_file.txt")
    large_content = "A" * (11 * 1024 * 1024)  # 11MB，超过10MB限制
    
    with open(large_file_path, 'w') as f:
        f.write(large_content)
    
    print(f"✅ 创建大文件: {large_file_path} ({len(large_content)} bytes)")
    
    try:
        async with aiohttp.ClientSession() as session:
            # 创建对话
            async with session.post(
                'http://localhost:8000/api/chat/conversations',
                json={'title': '文件验证测试'}
            ) as response:
                conversation = await response.json()
                conversation_id = conversation['id']
            
            # 尝试上传大文件
            print("\n🔄 尝试上传超大文件...")
            
            data = aiohttp.FormData()
            data.add_field('message', '测试大文件上传')
            data.add_field('conversation_id', str(conversation_id))
            
            with open(large_file_path, 'rb') as f:
                data.add_field('files', f, filename=large_file_path.name, content_type='text/plain')
                
                async with session.post(
                    'http://localhost:8000/api/chat/stream-with-files',
                    data=data
                ) as response:
                    if response.status == 413:
                        print("✅ 大文件被正确拒绝 (413 Payload Too Large)")
                    else:
                        print(f"⚠️ 预期413错误，但得到: {response.status}")
                        error_text = await response.text()
                        print(f"响应: {error_text}")
    
    except Exception as e:
        print(f"❌ 验证测试中出现错误: {e}")
    
    finally:
        # 清理大文件
        if large_file_path.exists():
            large_file_path.unlink()
            print(f"\n🗑️ 已清理大文件: {large_file_path}")

async def main():
    """主测试函数"""
    print("🚀 开始文件上传功能测试")
    print("="*50)
    
    # 检查后端是否运行
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/health') as response:
                if response.status == 200:
                    health = await response.json()
                    print(f"✅ 后端服务正常: {health}")
                else:
                    print(f"❌ 后端服务异常: {response.status}")
                    return
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        print("请确保后端服务已启动 (uvicorn main:app --reload)")
        return
    
    # 运行测试
    await test_file_upload()
    await test_file_validation()
    
    print("\n" + "="*50)
    print("✅ 所有测试完成")

if __name__ == "__main__":
    asyncio.run(main())
