#!/usr/bin/env python3
"""
简单的文件上传测试
"""

import requests
import json

def test_simple_upload():
    """简单的文件上传测试"""
    
    # 1. 创建对话
    print("🔄 创建对话...")
    response = requests.post(
        'http://localhost:8000/api/chat/conversations',
        json={'title': '简单测试'}
    )
    
    if response.status_code != 200:
        print(f"❌ 创建对话失败: {response.status_code}")
        print(response.text)
        return
    
    conversation = response.json()
    conversation_id = conversation['id']
    print(f"✅ 对话创建成功: ID={conversation_id}")
    
    # 2. 创建测试文件
    test_content = "这是一个简单的测试文件。\n包含一些中文内容。"
    
    # 3. 上传文件
    print("🔄 上传文件...")
    
    files = {
        'files': ('test.txt', test_content.encode('utf-8'), 'text/plain')
    }
    
    data = {
        'message': '请分析这个文件',
        'conversation_id': conversation_id
    }
    
    response = requests.post(
        'http://localhost:8000/api/chat/stream-with-files',
        files=files,
        data=data,
        stream=True
    )
    
    if response.status_code == 200:
        print("✅ 文件上传成功")
        
        # 读取流式响应
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data_str = line[6:]
                    if data_str == '[DONE]':
                        print("\n✅ 响应完成")
                        break
                    try:
                        chunk = json.loads(data_str)
                        if chunk.get('type') == 'content':
                            print(chunk.get('content', ''), end='', flush=True)
                        elif chunk.get('type') == 'error':
                            print(f"\n❌ 错误: {chunk.get('error')}")
                    except:
                        pass
    else:
        print(f"❌ 上传失败: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    test_simple_upload()
