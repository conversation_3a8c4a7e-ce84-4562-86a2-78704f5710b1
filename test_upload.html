<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .file-list {
            margin: 20px 0;
        }
        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 5px 0;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>文件上传测试</h1>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>点击或拖拽文件到这里上传</p>
        <p>支持图片、文档、代码文件等</p>
    </div>
    
    <input type="file" id="fileInput" multiple style="display: none;" accept="image/*,.txt,.md,.pdf,.py,.js,.ts,.html,.css,.json,.csv">
    
    <div class="file-list" id="fileList"></div>
    
    <div>
        <label for="messageInput">消息内容:</label><br>
        <textarea id="messageInput" rows="4" cols="50" placeholder="输入您的消息...">请分析这些文件</textarea>
    </div>
    
    <div>
        <label for="conversationId">对话ID (可选):</label><br>
        <input type="number" id="conversationId" placeholder="留空将创建新对话">
    </div>
    
    <button class="btn" onclick="uploadFiles()">发送消息和文件</button>
    
    <div id="messages"></div>

    <script>
        let selectedFiles = [];
        
        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });
        
        // 拖拽处理
        const uploadArea = document.querySelector('.upload-area');
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            handleFiles(e.dataTransfer.files);
        });
        
        function handleFiles(files) {
            selectedFiles = Array.from(files);
            displayFiles();
        }
        
        function displayFiles() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span>${file.name} (${(file.size / 1024).toFixed(1)} KB)</span>
                    <button onclick="removeFile(${index})" style="margin-left: auto;">删除</button>
                `;
                fileList.appendChild(fileItem);
            });
        }
        
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            displayFiles();
        }
        
        async function uploadFiles() {
            const message = document.getElementById('messageInput').value;
            const conversationId = document.getElementById('conversationId').value;
            
            if (!message.trim() && selectedFiles.length === 0) {
                showMessage('请输入消息或选择文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('message', message);
            
            if (conversationId) {
                formData.append('conversation_id', conversationId);
            } else {
                // 如果没有对话ID，先创建对话
                try {
                    const response = await fetch('http://localhost:8000/api/chat/conversations', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ title: '文件上传测试' })
                    });
                    const conversation = await response.json();
                    formData.append('conversation_id', conversation.id);
                    document.getElementById('conversationId').value = conversation.id;
                } catch (error) {
                    showMessage('创建对话失败: ' + error.message, 'error');
                    return;
                }
            }
            
            selectedFiles.forEach(file => {
                formData.append('files', file);
            });
            
            try {
                showMessage('正在上传文件和发送消息...', 'success');
                
                const response = await fetch('http://localhost:8000/api/chat/stream-with-files', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                // 处理流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                showMessage('开始接收AI响应...', 'success');
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6).trim();
                            if (data === '[DONE]') {
                                showMessage('响应完成!', 'success');
                                return;
                            }
                            
                            try {
                                const chunk = JSON.parse(data);
                                if (chunk.type === 'content') {
                                    appendToResponse(chunk.content);
                                } else if (chunk.type === 'complete') {
                                    showMessage('AI响应完成!', 'success');
                                } else if (chunk.type === 'error') {
                                    showMessage('AI响应错误: ' + chunk.error, 'error');
                                }
                            } catch (e) {
                                console.warn('解析SSE数据失败:', data);
                            }
                        }
                    }
                }
                
            } catch (error) {
                showMessage('上传失败: ' + error.message, 'error');
            }
        }
        
        function showMessage(text, type) {
            const messages = document.getElementById('messages');
            const message = document.createElement('div');
            message.className = `message ${type}`;
            message.textContent = text;
            messages.appendChild(message);
            
            // 自动滚动到底部
            message.scrollIntoView();
        }
        
        let responseDiv = null;
        function appendToResponse(content) {
            if (!responseDiv) {
                responseDiv = document.createElement('div');
                responseDiv.className = 'message success';
                responseDiv.innerHTML = '<strong>AI响应:</strong><br>';
                document.getElementById('messages').appendChild(responseDiv);
            }
            
            responseDiv.innerHTML += content;
            responseDiv.scrollIntoView();
        }
    </script>
</body>
</html>
