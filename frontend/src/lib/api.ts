import { ChatRequest, Conversation, Message, StreamChunk } from '@/types/chat'

const API_BASE = process.env.NODE_ENV === 'production' 
  ? 'https://your-api-domain.com/api' 
  : 'http://localhost:8000/api'

class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string = API_BASE) {
    this.baseUrl = baseUrl
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`API请求失败: ${response.status} ${error}`)
    }

    return response.json()
  }

  // 对话管理
  async createConversation(title: string = '新对话'): Promise<Conversation> {
    return this.request<Conversation>('/chat/conversations', {
      method: 'POST',
      body: JSON.stringify({ title }),
    })
  }

  async getConversations(): Promise<Conversation[]> {
    return this.request<Conversation[]>('/chat/conversations')
  }

  async getConversationMessages(conversationId: number): Promise<Message[]> {
    return this.request<Message[]>(`/chat/conversations/${conversationId}/messages`)
  }

  async deleteConversation(conversationId: number): Promise<void> {
    await this.request(`/chat/conversations/${conversationId}`, {
      method: 'DELETE',
    })
  }

  // 聊天功能
  async sendMessage(request: ChatRequest): Promise<any> {
    return this.request('/chat/send', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  }

  // SSE流式聊天
  async *streamChat(conversationId: number, message: string, files?: File[]): AsyncGenerator<StreamChunk> {
    let url: string
    let response: Response

    if (files && files.length > 0) {
      // 如果有文件，使用POST请求发送multipart/form-data
      const formData = new FormData()
      formData.append('message', message)
      formData.append('conversation_id', conversationId.toString())

      files.forEach((file, index) => {
        formData.append(`files`, file)
      })

      response = await fetch(`${this.baseUrl}/chat/stream-with-files`, {
        method: 'POST',
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
        body: formData,
      })
    } else {
      // 没有文件时使用原来的GET请求
      url = `${this.baseUrl}/chat/stream/${conversationId}?message=${encodeURIComponent(message)}`

      response = await fetch(url, {
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
      })
    }

    if (!response.ok) {
      throw new Error(`流式请求失败: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法获取响应流')
    }

    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim()
            
            if (data === '[DONE]') {
              return
            }

            try {
              const chunk: StreamChunk = JSON.parse(data)
              yield chunk
            } catch (e) {
              console.warn('解析SSE数据失败:', data)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
  }

  // 健康检查
  async healthCheck(): Promise<any> {
    return this.request('/chat/health')
  }
}

export const apiClient = new ApiClient()

// 工具函数
export const formatTime = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN')
}

export const truncateText = (text: string, maxLength: number = 50): string => {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}
