'use client'

import React from 'react'
import { Plus, MessageSquare, Trash2, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Conversation } from '@/types/chat'
import { formatTime, truncateText } from '@/lib/api'
import { cn } from '@/lib/utils'

interface SidebarProps {
  conversations: Conversation[]
  currentConversationId?: number
  onSelectConversation: (id: number) => void
  onNewConversation: () => void
  onDeleteConversation: (id: number) => void
  isLoading?: boolean
}

export function Sidebar({
  conversations,
  currentConversationId,
  onSelectConversation,
  onNewConversation,
  onDeleteConversation,
  isLoading = false
}: SidebarProps) {
  return (
    <div className="w-80 h-full gemini-sidebar flex flex-col">
      {/* 头部 */}
      <div className="p-6 border-b border-gray-200/60">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-semibold gradient-text">Liangxi AI</h1>
          <Button variant="ghost" size="icon" className="hover:bg-gray-100/80">
            <Settings className="h-5 w-5 text-gray-600" />
          </Button>
        </div>

        <Button
          onClick={onNewConversation}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white rounded-full py-3 shadow-sm"
          disabled={isLoading}
        >
          <Plus className="h-4 w-4 mr-2" />
          新建对话
        </Button>
      </div>

      {/* 对话列表 */}
      <ScrollArea className="flex-1">
        <div className="p-3">
          {conversations.length === 0 ? (
            <div className="text-center text-gray-500 py-12">
              <MessageSquare className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p className="text-sm font-medium">暂无对话</p>
              <p className="text-xs mt-1 text-gray-400">点击"新建对话"开始聊天</p>
            </div>
          ) : (
            <div className="space-y-2">
              {conversations.map((conversation) => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  isActive={conversation.id === currentConversationId}
                  onSelect={() => onSelectConversation(conversation.id)}
                  onDelete={() => onDeleteConversation(conversation.id)}
                />
              ))}
            </div>
          )}
        </div>
      </ScrollArea>

      {/* 底部信息 */}
      <div className="p-4 border-t border-gray-200/60 text-xs text-gray-500">
        <div className="flex items-center justify-between">
          <span>共 {conversations.length} 个对话</span>
          <span className="text-gray-400">v1.0.0</span>
        </div>
      </div>
    </div>
  )
}

interface ConversationItemProps {
  conversation: Conversation
  isActive: boolean
  onSelect: () => void
  onDelete: () => void
}

function ConversationItem({
  conversation,
  isActive,
  onSelect,
  onDelete
}: ConversationItemProps) {
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    // 直接删除，不显示确认弹窗
    onDelete()
  }

  return (
    <div
      className={cn(
        "group relative p-3 rounded-xl cursor-pointer transition-all duration-200",
        "hover:bg-white/60 hover:shadow-sm",
        isActive && "bg-white/80 shadow-sm border border-blue-200/50"
      )}
      onClick={onSelect}
    >
      <div className="flex items-start justify-between gap-2">
        <div className="flex-1 min-w-0">
          <h3 className={cn(
            "font-medium text-sm truncate",
            isActive ? "text-gray-900" : "text-gray-700"
          )}>
            {truncateText(conversation.title, 30)}
          </h3>

          <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
            <span>{formatTime(conversation.updated_at)}</span>
            {conversation.message_count > 0 && (
              <>
                <span>•</span>
                <span>{conversation.message_count} 条消息</span>
              </>
            )}
          </div>
        </div>

        <Button
          variant="ghost"
          size="sm"
          className="opacity-0 group-hover:opacity-100 transition-all duration-200 h-7 w-7 p-0 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg flex-shrink-0"
          onClick={handleDelete}
          title="删除对话"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
