'use client'

import React from 'react'
import { <PERSON><PERSON>, User, <PERSON><PERSON>, Clock, FileText, Image, Download } from 'lucide-react'
import { Message as MessageType } from '@/types/chat'
import { Button } from '@/components/ui/button'
import { GeminiMarkdown } from '@/components/ui/gemini-markdown'
import { formatTime } from '@/lib/api'
import { copyToClipboard } from '@/lib/utils'
import { cn } from '@/lib/utils'

interface MessageProps {
  message: MessageType
  isStreaming?: boolean
}

export function Message({ message, isStreaming = false }: MessageProps) {
  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'

  const handleCopy = async () => {
    try {
      await copyToClipboard(message.content)
      // 这里可以添加一个toast通知
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  return (
    <div className={cn(
      "group flex gap-4 p-6 chat-message max-w-5xl mx-auto",
      isUser ? "flex-row-reverse" : "flex-row"
    )}>
      {/* 头像 */}
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        isUser
          ? "bg-blue-600 text-white"
          : "bg-gradient-to-br from-blue-500 to-purple-600 text-white"
      )}>
        {isUser ? <User size={16} /> : <Bot size={16} />}
      </div>

      {/* 消息内容区域 */}
      <div className={cn(
        "flex-1 min-w-0",
        isUser ? "text-right" : "text-left"
      )}>
        {/* 文件附件 */}
        {message.files && message.files.length > 0 && (
          <div className={cn(
            "mb-3 flex flex-wrap gap-2",
            isUser ? "justify-end" : "justify-start"
          )}>
            {message.files.map((file, index) => (
              <div
                key={index}
                className={cn(
                  "flex items-center gap-2 p-2 rounded-lg border max-w-xs",
                  isUser
                    ? "bg-blue-50 border-blue-200 text-blue-900"
                    : "bg-gray-50 border-gray-200 text-gray-900"
                )}
              >
                {/* 文件图标 */}
                {file.type.startsWith('image/') ? (
                  <Image className="w-4 h-4 flex-shrink-0" />
                ) : (
                  <FileText className="w-4 h-4 flex-shrink-0" />
                )}

                {/* 文件信息 */}
                <div className="flex-1 min-w-0">
                  <div className="text-xs font-medium truncate">{file.name}</div>
                  <div className="text-xs opacity-70">
                    {(file.size / 1024).toFixed(1)} KB
                  </div>
                </div>

                {/* 下载按钮 */}
                {file.url && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => window.open(file.url, '_blank')}
                  >
                    <Download className="w-3 h-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        )}

        {/* 消息内容 */}
        <div className={cn(
          isUser
            ? "bg-blue-600 text-white p-4 rounded-2xl ml-auto max-w-2xl shadow-sm inline-block"
            : "bg-transparent text-gray-900 w-full",
          isStreaming && "animate-pulse"
        )}>
          {isAssistant ? (
            <GeminiMarkdown
              content={message.content}
              className="leading-relaxed"
            />
          ) : (
            <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
          )}

          {/* 流式输入指示器 */}
          {isStreaming && (
            <div className="flex items-center gap-1 mt-3 text-xs opacity-70">
              <div className="flex space-x-1">
                <div className="w-1 h-1 bg-current rounded-full animate-bounce"></div>
                <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="ml-2">正在输入...</span>
            </div>
          )}
        </div>

        {/* 消息元信息 */}
        <div className={cn(
          "flex items-center gap-2 mt-2 text-xs text-gray-500",
          isUser ? "justify-end" : "justify-start"
        )}>
          <span>{formatTime(message.created_at)}</span>

          {message.processing_time && (
            <>
              <span>•</span>
              <div className="flex items-center gap-1">
                <Clock size={12} />
                <span>{message.processing_time.toFixed(2)}s</span>
              </div>
            </>
          )}

          {message.model_name && (
            <>
              <span>•</span>
              <span className="text-gray-400">{message.model_name}</span>
            </>
          )}

          {/* 复制按钮 */}
          <Button
            variant="ghost"
            size="sm"
            className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 hover:bg-gray-100"
            onClick={handleCopy}
          >
            <Copy size={12} />
          </Button>


        </div>

        {/* 错误状态 */}
        {message.status === 'failed' && (
          <div className="mt-2 text-xs text-red-400 flex items-center gap-1">
            <span>⚠️ 消息发送失败</span>
          </div>
        )}
      </div>

    </div>
  )
}
