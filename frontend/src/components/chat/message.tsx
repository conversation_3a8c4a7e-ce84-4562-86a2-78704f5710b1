'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, User, Bo<PERSON>, Clock, FileText, Image, Download, Eye, X } from 'lucide-react'
import { Message as MessageType } from '@/types/chat'
import { Button } from '@/components/ui/button'
import { GeminiMarkdown } from '@/components/ui/gemini-markdown'
import { formatTime } from '@/lib/api'
import { copyToClipboard } from '@/lib/utils'
import { cn } from '@/lib/utils'

interface MessageProps {
  message: MessageType
  isStreaming?: boolean
}

export function Message({ message, isStreaming = false }: MessageProps) {
  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'
  const [viewingFile, setViewingFile] = useState<any>(null)
  const [loadingFile, setLoadingFile] = useState<number | null>(null)

  const handleCopy = async () => {
    try {
      await copyToClipboard(message.content)
      // 这里可以添加一个toast通知
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  const handleViewFile = async (attachmentId: number) => {
    setLoadingFile(attachmentId)
    try {
      const response = await fetch(`/api/chat/files/${attachmentId}/content`)
      if (response.ok) {
        const fileData = await response.json()
        setViewingFile(fileData)
      } else {
        console.error('获取文件内容失败:', response.statusText)
      }
    } catch (error) {
      console.error('获取文件内容失败:', error)
    } finally {
      setLoadingFile(null)
    }
  }

  return (
    <>
    <div className={cn(
      "group flex gap-4 p-6 chat-message max-w-5xl mx-auto",
      isUser ? "flex-row-reverse" : "flex-row"
    )}>
      {/* 头像 */}
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        isUser
          ? "bg-blue-600 text-white"
          : "bg-gradient-to-br from-blue-500 to-purple-600 text-white"
      )}>
        {isUser ? <User size={16} /> : <Bot size={16} />}
      </div>

      {/* 消息内容区域 */}
      <div className={cn(
        "flex-1 min-w-0",
        isUser ? "text-right" : "text-left"
      )}>
        {/* 文件附件 */}
        {message.files && message.files.length > 0 && (
          <div className={cn(
            "mb-3 flex flex-wrap gap-2",
            isUser ? "justify-end" : "justify-start"
          )}>
            {message.files.map((file, index) => (
              <div
                key={index}
                className={cn(
                  "flex items-center gap-2 p-2 rounded-lg border max-w-xs",
                  isUser
                    ? "bg-blue-50 border-blue-200 text-blue-900"
                    : "bg-gray-50 border-gray-200 text-gray-900"
                )}
              >
                {/* 文件图标 */}
                {file.type.startsWith('image/') ? (
                  <Image className="w-4 h-4 flex-shrink-0" />
                ) : (
                  <FileText className="w-4 h-4 flex-shrink-0" />
                )}

                {/* 文件信息 */}
                <div className="flex-1 min-w-0">
                  <div className="text-xs font-medium truncate">{file.name}</div>
                  <div className="text-xs opacity-70">
                    {(file.size / 1024).toFixed(1)} KB
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex gap-1">
                  {/* 查看按钮 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => handleViewFile(file.id)}
                    disabled={loadingFile === file.id}
                    title="查看文件内容"
                  >
                    {loadingFile === file.id ? (
                      <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Eye className="w-3 h-3" />
                    )}
                  </Button>

                  {/* 下载按钮 */}
                  {file.url && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => window.open(file.url, '_blank')}
                      title="下载文件"
                    >
                      <Download className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 消息内容 */}
        <div className={cn(
          isUser
            ? "bg-blue-600 text-white p-4 rounded-2xl ml-auto max-w-2xl shadow-sm inline-block"
            : "bg-transparent text-gray-900 w-full",
          isStreaming && "animate-pulse"
        )}>
          {isAssistant ? (
            <GeminiMarkdown
              content={message.content}
              className="leading-relaxed"
            />
          ) : (
            <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
          )}

          {/* 流式输入指示器 */}
          {isStreaming && (
            <div className="flex items-center gap-1 mt-3 text-xs opacity-70">
              <div className="flex space-x-1">
                <div className="w-1 h-1 bg-current rounded-full animate-bounce"></div>
                <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="ml-2">正在输入...</span>
            </div>
          )}
        </div>

        {/* 消息元信息 */}
        <div className={cn(
          "flex items-center gap-2 mt-2 text-xs text-gray-500",
          isUser ? "justify-end" : "justify-start"
        )}>
          <span>{formatTime(message.created_at)}</span>

          {message.processing_time && (
            <>
              <span>•</span>
              <div className="flex items-center gap-1">
                <Clock size={12} />
                <span>{message.processing_time.toFixed(2)}s</span>
              </div>
            </>
          )}

          {message.model_name && (
            <>
              <span>•</span>
              <span className="text-gray-400">{message.model_name}</span>
            </>
          )}

          {/* 复制按钮 */}
          <Button
            variant="ghost"
            size="sm"
            className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 hover:bg-gray-100"
            onClick={handleCopy}
          >
            <Copy size={12} />
          </Button>


        </div>

        {/* 错误状态 */}
        {message.status === 'failed' && (
          <div className="mt-2 text-xs text-red-400 flex items-center gap-1">
            <span>⚠️ 消息发送失败</span>
          </div>
        )}
      </div>

    </div>

    {/* 文件内容查看模态框 */}
    {viewingFile && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-4xl max-h-[80vh] w-full flex flex-col">
          {/* 模态框头部 */}
          <div className="flex items-center justify-between p-4 border-b">
            <div>
              <h3 className="text-lg font-semibold">{viewingFile.filename}</h3>
              <p className="text-sm text-gray-500">
                {viewingFile.file_type} • {(viewingFile.file_size / 1024).toFixed(1)} KB
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewingFile(null)}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* 模态框内容 */}
          <div className="flex-1 overflow-auto p-4">
            {viewingFile.extracted_text ? (
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  提取的文本内容：
                </div>
                <div className="bg-gray-50 rounded-lg p-4 whitespace-pre-wrap font-mono text-sm">
                  {viewingFile.extracted_text}
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>无法预览此文件类型的内容</p>
                <p className="text-sm mt-2">请下载文件查看完整内容</p>
              </div>
            )}
          </div>

          {/* 模态框底部 */}
          <div className="flex justify-end gap-2 p-4 border-t">
            <Button
              variant="outline"
              onClick={() => setViewingFile(null)}
            >
              关闭
            </Button>
            {viewingFile.extracted_text && (
              <Button
                onClick={() => copyToClipboard(viewingFile.extracted_text)}
              >
                复制内容
              </Button>
            )}
          </div>
        </div>
      </div>
    )}
    </>
  )
}
