'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Send, Loader2, <PERSON>clip, X, FileText, Image } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface ChatInputProps {
  onSendMessage: (message: string, files?: File[]) => void
  disabled?: boolean
  placeholder?: string
}

interface FilePreview {
  file: File
  id: string
  preview?: string
}

export function ChatInput({
  onSendMessage,
  disabled = false,
  placeholder = "输入您的消息..."
}: ChatInputProps) {
  const [message, setMessage] = useState('')
  const [files, setFiles] = useState<FilePreview[]>([])
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if ((message.trim() || files.length > 0) && !disabled) {
      onSendMessage(message.trim(), files.map(f => f.file))
      setMessage('')
      setFiles([])
      // 重置textarea高度
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto'
      }
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  // 处理文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || [])
    handleFiles(selectedFiles)
  }

  // 处理文件拖拽
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const droppedFiles = Array.from(e.dataTransfer.files)
    handleFiles(droppedFiles)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  // 统一处理文件
  const handleFiles = (newFiles: File[]) => {
    const validFiles = newFiles.filter(file => {
      // 检查文件大小 (10MB限制)
      if (file.size > 10 * 1024 * 1024) {
        alert(`文件 ${file.name} 超过10MB限制`)
        return false
      }

      // 检查文件类型
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'text/plain', 'text/markdown', 'application/pdf',
        'text/javascript', 'text/typescript', 'text/html', 'text/css',
        'application/json', 'text/csv'
      ]

      const isValidType = allowedTypes.includes(file.type) ||
        file.name.match(/\.(py|js|ts|tsx|jsx|html|css|md|txt|json|csv)$/i)

      if (!isValidType) {
        alert(`不支持的文件类型: ${file.name}`)
        return false
      }

      return true
    })

    const newFilePreviews: FilePreview[] = validFiles.map(file => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
      preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined
    }))

    setFiles(prev => [...prev, ...newFilePreviews])
  }

  // 移除文件
  const removeFile = (id: string) => {
    setFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id)
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  // 自动调整textarea高度
  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`
    }
  }, [message])

  // 清理预览URL
  useEffect(() => {
    return () => {
      files.forEach(file => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview)
        }
      })
    }
  }, [])

  return (
    <div className="border-t border-gray-200/60 bg-white/80 backdrop-blur-sm p-6">
      <div className="max-w-4xl mx-auto">
        {/* 文件预览区域 */}
        {files.length > 0 && (
          <div className="mb-4 flex flex-wrap gap-2">
            {files.map((filePreview) => (
              <div
                key={filePreview.id}
                className="relative group bg-gray-50 border border-gray-200 rounded-lg p-2 flex items-center gap-2 max-w-xs"
              >
                {/* 文件图标或预览 */}
                {filePreview.preview ? (
                  <img
                    src={filePreview.preview}
                    alt={filePreview.file.name}
                    className="w-8 h-8 object-cover rounded"
                  />
                ) : (
                  <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                    <FileText className="w-4 h-4 text-blue-600" />
                  </div>
                )}

                {/* 文件信息 */}
                <div className="flex-1 min-w-0">
                  <div className="text-xs font-medium text-gray-900 truncate">
                    {filePreview.file.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {(filePreview.file.size / 1024).toFixed(1)} KB
                  </div>
                </div>

                {/* 删除按钮 */}
                <button
                  type="button"
                  onClick={() => removeFile(filePreview.id)}
                  className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-200 rounded"
                >
                  <X className="w-3 h-3 text-gray-500" />
                </button>
              </div>
            ))}
          </div>
        )}

        <form onSubmit={handleSubmit} className="flex gap-3 items-end">
          <div className="flex-1 relative">
            <div
              className={cn(
                "relative rounded-full border border-gray-200 bg-white shadow-sm transition-all duration-200",
                "focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent"
              )}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
            >
              <textarea
                ref={textareaRef}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                disabled={disabled}
                rows={1}
                className={cn(
                  "w-full resize-none bg-transparent px-6 py-4 pr-12 text-sm",
                  "placeholder:text-gray-500 focus:outline-none",
                  "disabled:cursor-not-allowed disabled:opacity-50",
                  "min-h-[52px] max-h-[120px] transition-all duration-200"
                )}
              />

              {/* 文件上传按钮 */}
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={disabled}
                className={cn(
                  "absolute right-3 top-1/2 -translate-y-1/2 p-2 rounded-full",
                  "hover:bg-gray-100 transition-colors",
                  "disabled:opacity-50 disabled:cursor-not-allowed"
                )}
              >
                <Paperclip className="w-4 h-4 text-gray-500" />
              </button>

              {/* 字符计数 */}
              {message.length > 0 && (
                <div className="absolute bottom-2 right-12 text-xs text-gray-400">
                  {message.length}
                </div>
              )}
            </div>

            {/* 隐藏的文件输入 */}
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*,.txt,.md,.pdf,.py,.js,.ts,.tsx,.jsx,.html,.css,.json,.csv"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>

          <Button
            type="submit"
            disabled={(!message.trim() && files.length === 0) || disabled}
            className={cn(
              "h-[52px] w-[52px] rounded-full shadow-sm",
              "bg-blue-600 hover:bg-blue-700 text-white",
              "disabled:bg-gray-300 disabled:text-gray-500"
            )}
          >
            {disabled ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </Button>
        </form>

        {/* 提示文本 */}
        <div className="mt-3 text-xs text-gray-500 text-center">
          按 Enter 发送，Shift + Enter 换行 • 支持拖拽文件或点击 📎 上传
        </div>
      </div>
    </div>
  )
}
