'use client'

import React, { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON>les, Zap, Brain, MessageCircle } from 'lucide-react'
import { Sidebar } from '@/components/chat/sidebar'
import { Message } from '@/components/chat/message'
import { ChatInput } from '@/components/chat/chat-input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Button } from '@/components/ui/button'
import { apiClient } from '@/lib/api'
import { Conversation, Message as MessageType } from '@/types/chat'
import { initDevelopmentUtils } from '@/lib/dev-utils'

export default function HomePage() {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<MessageType[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isStreaming, setIsStreaming] = useState(false)
  const [streamingMessageId, setStreamingMessageId] = useState<number | null>(null)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  // 加载对话列表
  const loadConversations = async () => {
    try {
      const data = await apiClient.getConversations()
      setConversations(data)
    } catch (error) {
      console.error('加载对话列表失败:', error)
    }
  }

  // 加载消息
  const loadMessages = async (conversationId: number) => {
    try {
      const data = await apiClient.getConversationMessages(conversationId)
      setMessages(data)
    } catch (error) {
      console.error('加载消息失败:', error)
      setMessages([])
    }
  }

  // 创建新对话
  const handleNewConversation = async () => {
    try {
      setIsLoading(true)
      const conversation = await apiClient.createConversation()
      setConversations(prev => [conversation, ...prev])
      setCurrentConversation(conversation)
      setMessages([])
    } catch (error) {
      console.error('创建对话失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 选择对话
  const handleSelectConversation = async (id: number) => {
    const conversation = conversations.find(c => c.id === id)
    if (conversation) {
      setCurrentConversation(conversation)
      await loadMessages(id)
    }
  }

  // 删除对话
  const handleDeleteConversation = async (id: number) => {
    try {
      await apiClient.deleteConversation(id)
      setConversations(prev => prev.filter(c => c.id !== id))
      
      if (currentConversation?.id === id) {
        setCurrentConversation(null)
        setMessages([])
      }
    } catch (error) {
      console.error('删除对话失败:', error)
    }
  }

  // 发送消息
  const handleSendMessage = async (content: string, files?: File[]) => {
    if (!currentConversation || isStreaming) return

    try {
      setIsStreaming(true)
      
      // 添加用户消息到界面
      const userMessage: MessageType = {
        id: Date.now(),
        role: 'user',
        content,
        status: 'completed',
        created_at: new Date().toISOString(),
        files: files?.map(file => ({
          name: file.name,
          size: file.size,
          type: file.type
        }))
      }
      setMessages(prev => [...prev, userMessage])

      // 创建临时助手消息
      const tempAssistantMessage: MessageType = {
        id: Date.now() + 1,
        role: 'assistant',
        content: '',
        status: 'processing',
        created_at: new Date().toISOString()
      }
      setMessages(prev => [...prev, tempAssistantMessage])
      setStreamingMessageId(tempAssistantMessage.id)

      // 开始流式对话
      let assistantContent = ''
      let chunkCount = 0
      let lastUpdateTime = Date.now()

      try {
        for await (const chunk of apiClient.streamChat(currentConversation.id, content, files)) {
          chunkCount++

          // 调试日志
          console.log('📦 收到SSE chunk:', {
            type: chunk.type,
            content: chunk.content,
            contentLength: chunk.content?.length,
            chunkCount,
            totalContent: assistantContent.length
          })

          if (chunk.type === 'content' && chunk.content) {
            assistantContent += chunk.content

            // 优化更新频率 - 避免过于频繁的重新渲染
            const now = Date.now()
            const shouldUpdate = now - lastUpdateTime > 100 || // 至少间隔100ms
                               chunk.content.includes('\n') ||  // 遇到换行立即更新
                               chunk.content.includes('。') ||  // 遇到句号立即更新
                               chunk.content.includes('！') ||  // 遇到感叹号立即更新
                               chunk.content.includes('？')    // 遇到问号立即更新

            if (shouldUpdate) {
              // 实时更新消息内容
              setMessages(prev => prev.map(msg =>
                msg.id === tempAssistantMessage.id
                  ? { ...msg, content: assistantContent }
                  : msg
              ))
              lastUpdateTime = now

              // 滚动到底部
              setTimeout(() => scrollToBottom(), 50)
            }

          } else if (chunk.type === 'complete') {
            // 确保最终内容完整更新
            setMessages(prev => prev.map(msg =>
              msg.id === tempAssistantMessage.id
                ? {
                    ...msg,
                    id: chunk.message_id || msg.id,
                    status: 'completed',
                    processing_time: chunk.processing_time,
                    content: assistantContent.trim() // 确保使用最终内容并去除多余空格
                  }
                : msg
            ))
            console.log(`✅ 流式响应完成，共收到 ${chunkCount} 个chunk，最终内容长度: ${assistantContent.length}`)
            break

          } else if (chunk.type === 'error') {
            setMessages(prev => prev.map(msg =>
              msg.id === tempAssistantMessage.id
                ? {
                    ...msg,
                    content: `错误: ${chunk.error}`,
                    status: 'failed'
                  }
                : msg
            ))
            console.error('流式响应错误:', chunk.error)
            break
          }
        }
      } catch (streamError) {
        console.error('流式连接错误:', streamError)
        setMessages(prev => prev.map(msg =>
          msg.id === tempAssistantMessage.id
            ? {
                ...msg,
                content: `连接错误: ${streamError}`,
                status: 'failed'
              }
            : msg
        ))
      }

      // 重新加载对话列表以更新消息计数
      await loadConversations()

    } catch (error) {
      console.error('发送消息失败:', error)
      setMessages(prev => prev.map(msg => 
        msg.id === streamingMessageId 
          ? { 
              ...msg, 
              content: `发送失败: ${error}`,
              status: 'failed'
            }
          : msg
      ))
    } finally {
      setIsStreaming(false)
      setStreamingMessageId(null)
    }
  }

  // 初始化
  useEffect(() => {
    initDevelopmentUtils()
    loadConversations()
  }, [])

  // 自动滚动到底部
  useEffect(() => {
    scrollToBottom()
  }, [messages])

  return (
    <div className="flex h-screen">
      {/* 侧边栏 */}
      <Sidebar
        conversations={conversations}
        currentConversationId={currentConversation?.id}
        onSelectConversation={handleSelectConversation}
        onNewConversation={handleNewConversation}
        onDeleteConversation={handleDeleteConversation}
        isLoading={isLoading}
      />

      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col">
        {currentConversation ? (
          <>
            {/* 聊天头部 */}
            <div className="border-b border-gray-200/60 p-6 bg-white/80 backdrop-blur-sm">
              <div className="max-w-4xl mx-auto">
                <h2 className="font-semibold text-xl text-gray-900">{currentConversation.title}</h2>
                <p className="text-sm text-gray-600 mt-1">
                  {messages.length} 条消息
                </p>
              </div>
            </div>

            {/* 消息列表 */}
            <ScrollArea className="flex-1 bg-gray-50/50">
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center py-16">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <MessageCircle className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2 text-gray-900">开始对话</h3>
                    <p className="text-gray-600">发送消息开始与AI助手对话</p>
                  </div>
                </div>
              ) : (
                <div className="py-6">
                  {messages.map((message) => (
                    <Message
                      key={message.id}
                      message={message}
                      isStreaming={isStreaming && message.id === streamingMessageId}
                    />
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </ScrollArea>

            {/* 输入区域 */}
            <ChatInput
              onSendMessage={handleSendMessage}
              disabled={isStreaming}
              placeholder="输入您的消息..."
            />
          </>
        ) : (
          <WelcomeScreen onNewConversation={handleNewConversation} />
        )}
      </div>
    </div>
  )
}

function WelcomeScreen({ onNewConversation }: { onNewConversation: () => void }) {
  const features = [
    {
      icon: Brain,
      title: "智能对话",
      description: "基于先进的AI模型，提供自然流畅的对话体验"
    },
    {
      icon: Zap,
      title: "实时响应",
      description: "支持流式输出，实时查看AI的思考过程"
    },
    {
      icon: Sparkles,
      title: "多Agent协作",
      description: "集成Autogen框架，支持多智能体协同工作"
    }
  ]

  return (
    <div className="flex-1 flex items-center justify-center p-8 bg-gray-50/50">
      <div className="max-w-4xl text-center">
        <div className="mb-12">
          <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <Sparkles className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-5xl font-bold mb-6 gradient-text">
            你好，我是 Liangxi AI
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            心有灵犀一点通，一个功能完善的AI对话系统，具备现代化的界面设计和强大的AI对话能力
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {features.map((feature, index) => (
            <div key={index} className="p-8 rounded-2xl gemini-card">
              <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <feature.icon className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-semibold mb-3 text-gray-900">{feature.title}</h3>
              <p className="text-sm text-gray-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>

        <Button
          onClick={onNewConversation}
          size="lg"
          className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-full text-lg shadow-sm"
        >
          开始对话
        </Button>
      </div>
    </div>
  )
}
