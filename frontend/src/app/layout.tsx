import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'AI系统 - 智能对话助手',
  description: '一个功能完善的AI对话系统，具备现代化的界面设计和强大的AI对话能力',
  keywords: ['AI', '人工智能', '对话系统', 'ChatGPT', 'Autogen'],
  authors: [{ name: 'AI系统开发团队' }],
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" className="light">
      <body className={`${inter.className} antialiased`}>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
          {children}
        </div>
      </body>
    </html>
  )
}
