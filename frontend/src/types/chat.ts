export interface FileAttachment {
  name: string
  size: number
  type: string
  url?: string
}

export interface Message {
  id: number
  role: 'user' | 'assistant' | 'system'
  content: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  created_at: string
  model_name?: string
  processing_time?: number
  tokens_used?: number
  files?: FileAttachment[]
}

export interface Conversation {
  id: number
  title: string
  created_at: string
  updated_at: string
  message_count: number
}

export interface ChatRequest {
  message: string
  conversation_id?: number
}

export interface ChatResponse {
  message_id: number
  conversation_id: number
  content: string
  role: string
  created_at: string
}

export interface StreamChunk {
  type: 'content' | 'complete' | 'error'
  content?: string
  message_id?: number
  conversation_id?: number
  processing_time?: number
  error?: string
}

export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}
