/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZqaW5nbHYlMkZQeWNoYXJtUHJvamVjdHMlMkZhaS1zeXN0ZW0lMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmFwcC1yb3V0ZXIuanMmbW9kdWxlcz0lMkZVc2VycyUyRmppbmdsdiUyRlB5Y2hhcm1Qcm9qZWN0cyUyRmFpLXN5c3RlbSUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGZXJyb3ItYm91bmRhcnkuanMmbW9kdWxlcz0lMkZVc2VycyUyRmppbmdsdiUyRlB5Y2hhcm1Qcm9qZWN0cyUyRmFpLXN5c3RlbSUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGamluZ2x2JTJGUHljaGFybVByb2plY3RzJTJGYWktc3lzdGVtJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZub3QtZm91bmQtYm91bmRhcnkuanMmbW9kdWxlcz0lMkZVc2VycyUyRmppbmdsdiUyRlB5Y2hhcm1Qcm9qZWN0cyUyRmFpLXN5c3RlbSUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGamluZ2x2JTJGUHljaGFybVByb2plY3RzJTJGYWktc3lzdGVtJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTRJO0FBQzVJLDBPQUFnSjtBQUNoSix3T0FBK0k7QUFDL0ksa1BBQW9KO0FBQ3BKLHNRQUE4SjtBQUM5SiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXN5c3RlbS1mcm9udGVuZC8/YTE0MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9qaW5nbHYvUHljaGFybVByb2plY3RzL2FpLXN5c3RlbS9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9qaW5nbHYvUHljaGFybVByb2plY3RzL2FpLXN5c3RlbS9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvamluZ2x2L1B5Y2hhcm1Qcm9qZWN0cy9haS1zeXN0ZW0vZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvamluZ2x2L1B5Y2hhcm1Qcm9qZWN0cy9haS1zeXN0ZW0vZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9qaW5nbHYvUHljaGFybVByb2plY3RzL2FpLXN5c3RlbS9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9qaW5nbHYvUHljaGFybVByb2plY3RzL2FpLXN5c3RlbS9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLXNlYXJjaHBhcmFtcy1iYWlsb3V0LXByb3ZpZGVyLmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZqaW5nbHYlMkZQeWNoYXJtUHJvamVjdHMlMkZhaS1zeXN0ZW0lMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXN5c3RlbS1mcm9udGVuZC8/YTdkZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9qaW5nbHYvUHljaGFybVByb2plY3RzL2FpLXN5c3RlbS9mcm9udGVuZC9zcmMvYXBwL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _components_chat_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/chat/sidebar */ \"(ssr)/./src/components/chat/sidebar.tsx\");\n/* harmony import */ var _components_chat_message__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/chat/message */ \"(ssr)/./src/components/chat/message.tsx\");\n/* harmony import */ var _components_chat_chat_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/chat-input */ \"(ssr)/./src/components/chat/chat-input.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(ssr)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_dev_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/dev-utils */ \"(ssr)/./src/lib/dev-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 滚动到底部\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    // 加载对话列表\n    const loadConversations = async ()=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.getConversations();\n            setConversations(data);\n        } catch (error) {\n            console.error(\"加载对话列表失败:\", error);\n        }\n    };\n    // 加载消息\n    const loadMessages = async (conversationId)=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.getConversationMessages(conversationId);\n            setMessages(data);\n        } catch (error) {\n            console.error(\"加载消息失败:\", error);\n            setMessages([]);\n        }\n    };\n    // 创建新对话\n    const handleNewConversation = async ()=>{\n        try {\n            setIsLoading(true);\n            const conversation = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.createConversation();\n            setConversations((prev)=>[\n                    conversation,\n                    ...prev\n                ]);\n            setCurrentConversation(conversation);\n            setMessages([]);\n        } catch (error) {\n            console.error(\"创建对话失败:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 选择对话\n    const handleSelectConversation = async (id)=>{\n        const conversation = conversations.find((c)=>c.id === id);\n        if (conversation) {\n            setCurrentConversation(conversation);\n            await loadMessages(id);\n        }\n    };\n    // 删除对话\n    const handleDeleteConversation = async (id)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.deleteConversation(id);\n            setConversations((prev)=>prev.filter((c)=>c.id !== id));\n            if (currentConversation?.id === id) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n        } catch (error) {\n            console.error(\"删除对话失败:\", error);\n        }\n    };\n    // 发送消息\n    const handleSendMessage = async (content, files)=>{\n        if (!currentConversation || isStreaming) return;\n        try {\n            setIsStreaming(true);\n            // 添加用户消息到界面\n            const userMessage = {\n                id: Date.now(),\n                role: \"user\",\n                content,\n                status: \"completed\",\n                created_at: new Date().toISOString(),\n                files: files?.map((file)=>({\n                        name: file.name,\n                        size: file.size,\n                        type: file.type\n                    }))\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // 创建临时助手消息\n            const tempAssistantMessage = {\n                id: Date.now() + 1,\n                role: \"assistant\",\n                content: \"\",\n                status: \"processing\",\n                created_at: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    tempAssistantMessage\n                ]);\n            setStreamingMessageId(tempAssistantMessage.id);\n            // 开始流式对话\n            let assistantContent = \"\";\n            let chunkCount = 0;\n            let lastUpdateTime = Date.now();\n            try {\n                for await (const chunk of _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.streamChat(currentConversation.id, content, files)){\n                    chunkCount++;\n                    // 调试日志\n                    console.log(\"\\uD83D\\uDCE6 收到SSE chunk:\", {\n                        type: chunk.type,\n                        content: chunk.content,\n                        contentLength: chunk.content?.length,\n                        chunkCount,\n                        totalContent: assistantContent.length\n                    });\n                    if (chunk.type === \"content\" && chunk.content) {\n                        assistantContent += chunk.content;\n                        // 优化更新频率 - 避免过于频繁的重新渲染\n                        const now = Date.now();\n                        const shouldUpdate = now - lastUpdateTime > 100 || // 至少间隔100ms\n                        chunk.content.includes(\"\\n\") || // 遇到换行立即更新\n                        chunk.content.includes(\"。\") || // 遇到句号立即更新\n                        chunk.content.includes(\"！\") || // 遇到感叹号立即更新\n                        chunk.content.includes(\"？\") // 遇到问号立即更新\n                        ;\n                        if (shouldUpdate) {\n                            // 实时更新消息内容\n                            setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                        ...msg,\n                                        content: assistantContent\n                                    } : msg));\n                            lastUpdateTime = now;\n                            // 滚动到底部\n                            setTimeout(()=>scrollToBottom(), 50);\n                        }\n                    } else if (chunk.type === \"complete\") {\n                        // 确保最终内容完整更新\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                    ...msg,\n                                    id: chunk.message_id || msg.id,\n                                    status: \"completed\",\n                                    processing_time: chunk.processing_time,\n                                    content: assistantContent.trim() // 确保使用最终内容并去除多余空格\n                                } : msg));\n                        console.log(`✅ 流式响应完成，共收到 ${chunkCount} 个chunk，最终内容长度: ${assistantContent.length}`);\n                        break;\n                    } else if (chunk.type === \"error\") {\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                    ...msg,\n                                    content: `错误: ${chunk.error}`,\n                                    status: \"failed\"\n                                } : msg));\n                        console.error(\"流式响应错误:\", chunk.error);\n                        break;\n                    }\n                }\n            } catch (streamError) {\n                console.error(\"流式连接错误:\", streamError);\n                setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                            ...msg,\n                            content: `连接错误: ${streamError}`,\n                            status: \"failed\"\n                        } : msg));\n            }\n            // 重新加载对话列表以更新消息计数\n            await loadConversations();\n        } catch (error) {\n            console.error(\"发送消息失败:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === streamingMessageId ? {\n                        ...msg,\n                        content: `发送失败: ${error}`,\n                        status: \"failed\"\n                    } : msg));\n        } finally{\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        }\n    };\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (0,_lib_dev_utils__WEBPACK_IMPORTED_MODULE_8__.initDevelopmentUtils)();\n        loadConversations();\n    }, []);\n    // 自动滚动到底部\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n                conversations: conversations,\n                currentConversationId: currentConversation?.id,\n                onSelectConversation: handleSelectConversation,\n                onNewConversation: handleNewConversation,\n                onDeleteConversation: handleDeleteConversation,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: currentConversation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200/60 p-6 bg-white/80 backdrop-blur-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-xl text-gray-900\",\n                                        children: currentConversation.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: [\n                                            messages.length,\n                                            \" 条消息\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                            className: \"flex-1 bg-gray-50/50\",\n                            children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2 text-gray-900\",\n                                            children: \"开始对话\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"发送消息开始与AI助手对话\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: [\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_message__WEBPACK_IMPORTED_MODULE_3__.Message, {\n                                            message: message,\n                                            isStreaming: isStreaming && message.id === streamingMessageId\n                                        }, message.id, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 21\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_input__WEBPACK_IMPORTED_MODULE_4__.ChatInput, {\n                            onSendMessage: handleSendMessage,\n                            disabled: isStreaming,\n                            placeholder: \"输入您的消息...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeScreen, {\n                    onNewConversation: handleNewConversation\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\nfunction WelcomeScreen({ onNewConversation }) {\n    const features = [\n        {\n            icon: _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"智能对话\",\n            description: \"基于先进的AI模型，提供自然流畅的对话体验\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"实时响应\",\n            description: \"支持流式输出，实时查看AI的思考过程\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"多Agent协作\",\n            description: \"集成Autogen框架，支持多智能体协同工作\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex items-center justify-center p-8 bg-gray-50/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-10 w-10 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold mb-6 gradient-text\",\n                            children: \"你好，我是 Liangxi AI\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                            children: \"心有灵犀一点通，一个功能完善的AI对话系统，具备现代化的界面设计和强大的AI对话能力\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 rounded-2xl gemini-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 text-gray-900\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                    onClick: onNewConversation,\n                    size: \"lg\",\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-full text-lg shadow-sm\",\n                    children: \"开始对话\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n            lineNumber: 326,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/chat-input.tsx":
/*!********************************************!*\
  !*** ./src/components/chat/chat-input.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInput: () => (/* binding */ ChatInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FileText_Loader2_Paperclip_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Loader2,Paperclip,Send,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Loader2_Paperclip_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Loader2,Paperclip,Send,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Loader2_Paperclip_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Loader2,Paperclip,Send,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Loader2_Paperclip_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Loader2,Paperclip,Send,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Loader2_Paperclip_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Loader2,Paperclip,Send,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ChatInput auto */ \n\n\n\n\nfunction ChatInput({ onSendMessage, disabled = false, placeholder = \"输入您的消息...\" }) {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if ((message.trim() || files.length > 0) && !disabled) {\n            onSendMessage(message.trim(), files.map((f)=>f.file));\n            setMessage(\"\");\n            setFiles([]);\n            // 重置textarea高度\n            if (textareaRef.current) {\n                textareaRef.current.style.height = \"auto\";\n            }\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    // 处理文件选择\n    const handleFileSelect = (e)=>{\n        const selectedFiles = Array.from(e.target.files || []);\n        handleFiles(selectedFiles);\n    };\n    // 处理文件拖拽\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        const droppedFiles = Array.from(e.dataTransfer.files);\n        handleFiles(droppedFiles);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    // 统一处理文件\n    const handleFiles = (newFiles)=>{\n        const validFiles = newFiles.filter((file)=>{\n            // 检查文件大小 (10MB限制)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(`文件 ${file.name} 超过10MB限制`);\n                return false;\n            }\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\",\n                \"image/webp\",\n                \"text/plain\",\n                \"text/markdown\",\n                \"application/pdf\",\n                \"text/javascript\",\n                \"text/typescript\",\n                \"text/html\",\n                \"text/css\",\n                \"application/json\",\n                \"text/csv\"\n            ];\n            const isValidType = allowedTypes.includes(file.type) || file.name.match(/\\.(py|js|ts|tsx|jsx|html|css|md|txt|json|csv)$/i);\n            if (!isValidType) {\n                alert(`不支持的文件类型: ${file.name}`);\n                return false;\n            }\n            return true;\n        });\n        const newFilePreviews = validFiles.map((file)=>({\n                file,\n                id: Math.random().toString(36).substr(2, 9),\n                preview: file.type.startsWith(\"image/\") ? URL.createObjectURL(file) : undefined\n            }));\n        setFiles((prev)=>[\n                ...prev,\n                ...newFilePreviews\n            ]);\n    };\n    // 移除文件\n    const removeFile = (id)=>{\n        setFiles((prev)=>{\n            const fileToRemove = prev.find((f)=>f.id === id);\n            if (fileToRemove?.preview) {\n                URL.revokeObjectURL(fileToRemove.preview);\n            }\n            return prev.filter((f)=>f.id !== id);\n        });\n    };\n    // 自动调整textarea高度\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const textarea = textareaRef.current;\n        if (textarea) {\n            textarea.style.height = \"auto\";\n            textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;\n        }\n    }, [\n        message\n    ]);\n    // 清理预览URL\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            files.forEach((file)=>{\n                if (file.preview) {\n                    URL.revokeObjectURL(file.preview);\n                }\n            });\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-t border-gray-200/60 bg-white/80 backdrop-blur-sm p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 flex flex-wrap gap-2\",\n                    children: files.map((filePreview)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group bg-gray-50 border border-gray-200 rounded-lg p-2 flex items-center gap-2 max-w-xs\",\n                            children: [\n                                filePreview.preview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: filePreview.preview,\n                                    alt: filePreview.file.name,\n                                    className: \"w-8 h-8 object-cover rounded\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-blue-100 rounded flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Loader2_Paperclip_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs font-medium text-gray-900 truncate\",\n                                            children: filePreview.file.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                (filePreview.file.size / 1024).toFixed(1),\n                                                \" KB\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>removeFile(filePreview.id),\n                                    className: \"opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-200 rounded\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Loader2_Paperclip_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-3 h-3 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, filePreview.id, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"flex gap-3 items-end\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative rounded-full border border-gray-200 bg-white shadow-sm transition-all duration-200\", \"focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent\"),\n                                    onDrop: handleDrop,\n                                    onDragOver: handleDragOver,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: textareaRef,\n                                            value: message,\n                                            onChange: (e)=>setMessage(e.target.value),\n                                            onKeyDown: handleKeyDown,\n                                            placeholder: placeholder,\n                                            disabled: disabled,\n                                            rows: 1,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full resize-none bg-transparent px-6 py-4 pr-12 text-sm\", \"placeholder:text-gray-500 focus:outline-none\", \"disabled:cursor-not-allowed disabled:opacity-50\", \"min-h-[52px] max-h-[120px] transition-all duration-200\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>fileInputRef.current?.click(),\n                                            disabled: disabled,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-3 top-1/2 -translate-y-1/2 p-2 rounded-full\", \"hover:bg-gray-100 transition-colors\", \"disabled:opacity-50 disabled:cursor-not-allowed\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Loader2_Paperclip_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        message.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-2 right-12 text-xs text-gray-400\",\n                                            children: message.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    multiple: true,\n                                    accept: \"image/*,.txt,.md,.pdf,.py,.js,.ts,.tsx,.jsx,.html,.css,.json,.csv\",\n                                    onChange: handleFileSelect,\n                                    className: \"hidden\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"submit\",\n                            disabled: !message.trim() && files.length === 0 || disabled,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-[52px] w-[52px] rounded-full shadow-sm\", \"bg-blue-600 hover:bg-blue-700 text-white\", \"disabled:bg-gray-300 disabled:text-gray-500\"),\n                            children: disabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Loader2_Paperclip_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Loader2_Paperclip_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 text-xs text-gray-500 text-center\",\n                    children: \"按 Enter 发送，Shift + Enter 换行 • 支持拖拽文件或点击 \\uD83D\\uDCCE 上传\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n            lineNumber: 137,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/chat-input.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jaGF0L2NoYXQtaW5wdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMEQ7QUFDaUI7QUFDNUI7QUFDZjtBQWN6QixTQUFTVyxVQUFVLEVBQ3hCQyxhQUFhLEVBQ2JDLFdBQVcsS0FBSyxFQUNoQkMsY0FBYyxXQUFXLEVBQ1Y7SUFDZixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR2YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDZ0IsT0FBT0MsU0FBUyxHQUFHakIsK0NBQVFBLENBQWdCLEVBQUU7SUFDcEQsTUFBTWtCLGNBQWNqQiw2Q0FBTUEsQ0FBc0I7SUFDaEQsTUFBTWtCLGVBQWVsQiw2Q0FBTUEsQ0FBbUI7SUFFOUMsTUFBTW1CLGVBQWUsQ0FBQ0M7UUFDcEJBLEVBQUVDLGNBQWM7UUFDaEIsSUFBSSxDQUFDUixRQUFRUyxJQUFJLE1BQU1QLE1BQU1RLE1BQU0sR0FBRyxNQUFNLENBQUNaLFVBQVU7WUFDckRELGNBQWNHLFFBQVFTLElBQUksSUFBSVAsTUFBTVMsR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJO1lBQ25EWixXQUFXO1lBQ1hFLFNBQVMsRUFBRTtZQUNYLGVBQWU7WUFDZixJQUFJQyxZQUFZVSxPQUFPLEVBQUU7Z0JBQ3ZCVixZQUFZVSxPQUFPLENBQUNDLEtBQUssQ0FBQ0MsTUFBTSxHQUFHO1lBQ3JDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLGdCQUFnQixDQUFDVjtRQUNyQixJQUFJQSxFQUFFVyxHQUFHLEtBQUssV0FBVyxDQUFDWCxFQUFFWSxRQUFRLEVBQUU7WUFDcENaLEVBQUVDLGNBQWM7WUFDaEJGLGFBQWFDO1FBQ2Y7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNYSxtQkFBbUIsQ0FBQ2I7UUFDeEIsTUFBTWMsZ0JBQWdCQyxNQUFNQyxJQUFJLENBQUNoQixFQUFFaUIsTUFBTSxDQUFDdEIsS0FBSyxJQUFJLEVBQUU7UUFDckR1QixZQUFZSjtJQUNkO0lBRUEsU0FBUztJQUNULE1BQU1LLGFBQWEsQ0FBQ25CO1FBQ2xCQSxFQUFFQyxjQUFjO1FBQ2hCLE1BQU1tQixlQUFlTCxNQUFNQyxJQUFJLENBQUNoQixFQUFFcUIsWUFBWSxDQUFDMUIsS0FBSztRQUNwRHVCLFlBQVlFO0lBQ2Q7SUFFQSxNQUFNRSxpQkFBaUIsQ0FBQ3RCO1FBQ3RCQSxFQUFFQyxjQUFjO0lBQ2xCO0lBRUEsU0FBUztJQUNULE1BQU1pQixjQUFjLENBQUNLO1FBQ25CLE1BQU1DLGFBQWFELFNBQVNFLE1BQU0sQ0FBQ25CLENBQUFBO1lBQ2pDLGtCQUFrQjtZQUNsQixJQUFJQSxLQUFLb0IsSUFBSSxHQUFHLEtBQUssT0FBTyxNQUFNO2dCQUNoQ0MsTUFBTSxDQUFDLEdBQUcsRUFBRXJCLEtBQUtzQixJQUFJLENBQUMsU0FBUyxDQUFDO2dCQUNoQyxPQUFPO1lBQ1Q7WUFFQSxTQUFTO1lBQ1QsTUFBTUMsZUFBZTtnQkFDbkI7Z0JBQWM7Z0JBQWE7Z0JBQWE7Z0JBQ3hDO2dCQUFjO2dCQUFpQjtnQkFDL0I7Z0JBQW1CO2dCQUFtQjtnQkFBYTtnQkFDbkQ7Z0JBQW9CO2FBQ3JCO1lBRUQsTUFBTUMsY0FBY0QsYUFBYUUsUUFBUSxDQUFDekIsS0FBSzBCLElBQUksS0FDakQxQixLQUFLc0IsSUFBSSxDQUFDSyxLQUFLLENBQUM7WUFFbEIsSUFBSSxDQUFDSCxhQUFhO2dCQUNoQkgsTUFBTSxDQUFDLFVBQVUsRUFBRXJCLEtBQUtzQixJQUFJLENBQUMsQ0FBQztnQkFDOUIsT0FBTztZQUNUO1lBRUEsT0FBTztRQUNUO1FBRUEsTUFBTU0sa0JBQWlDVixXQUFXcEIsR0FBRyxDQUFDRSxDQUFBQSxPQUFTO2dCQUM3REE7Z0JBQ0E2QixJQUFJQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRztnQkFDekNDLFNBQVNsQyxLQUFLMEIsSUFBSSxDQUFDUyxVQUFVLENBQUMsWUFBWUMsSUFBSUMsZUFBZSxDQUFDckMsUUFBUXNDO1lBQ3hFO1FBRUFoRCxTQUFTaUQsQ0FBQUEsT0FBUTttQkFBSUE7bUJBQVNYO2FBQWdCO0lBQ2hEO0lBRUEsT0FBTztJQUNQLE1BQU1ZLGFBQWEsQ0FBQ1g7UUFDbEJ2QyxTQUFTaUQsQ0FBQUE7WUFDUCxNQUFNRSxlQUFlRixLQUFLRyxJQUFJLENBQUMzQyxDQUFBQSxJQUFLQSxFQUFFOEIsRUFBRSxLQUFLQTtZQUM3QyxJQUFJWSxjQUFjUCxTQUFTO2dCQUN6QkUsSUFBSU8sZUFBZSxDQUFDRixhQUFhUCxPQUFPO1lBQzFDO1lBQ0EsT0FBT0ssS0FBS3BCLE1BQU0sQ0FBQ3BCLENBQUFBLElBQUtBLEVBQUU4QixFQUFFLEtBQUtBO1FBQ25DO0lBQ0Y7SUFFQSxpQkFBaUI7SUFDakJ0RCxnREFBU0EsQ0FBQztRQUNSLE1BQU1xRSxXQUFXckQsWUFBWVUsT0FBTztRQUNwQyxJQUFJMkMsVUFBVTtZQUNaQSxTQUFTMUMsS0FBSyxDQUFDQyxNQUFNLEdBQUc7WUFDeEJ5QyxTQUFTMUMsS0FBSyxDQUFDQyxNQUFNLEdBQUcsQ0FBQyxFQUFFMkIsS0FBS2UsR0FBRyxDQUFDRCxTQUFTRSxZQUFZLEVBQUUsS0FBSyxFQUFFLENBQUM7UUFDckU7SUFDRixHQUFHO1FBQUMzRDtLQUFRO0lBRVosVUFBVTtJQUNWWixnREFBU0EsQ0FBQztRQUNSLE9BQU87WUFDTGMsTUFBTTBELE9BQU8sQ0FBQy9DLENBQUFBO2dCQUNaLElBQUlBLEtBQUtrQyxPQUFPLEVBQUU7b0JBQ2hCRSxJQUFJTyxlQUFlLENBQUMzQyxLQUFLa0MsT0FBTztnQkFDbEM7WUFDRjtRQUNGO0lBQ0YsR0FBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUNjO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7O2dCQUVaNUQsTUFBTVEsTUFBTSxHQUFHLG1CQUNkLDhEQUFDbUQ7b0JBQUlDLFdBQVU7OEJBQ1o1RCxNQUFNUyxHQUFHLENBQUMsQ0FBQ29ELDRCQUNWLDhEQUFDRjs0QkFFQ0MsV0FBVTs7Z0NBR1RDLFlBQVloQixPQUFPLGlCQUNsQiw4REFBQ2lCO29DQUNDQyxLQUFLRixZQUFZaEIsT0FBTztvQ0FDeEJtQixLQUFLSCxZQUFZbEQsSUFBSSxDQUFDc0IsSUFBSTtvQ0FDMUIyQixXQUFVOzs7Ozt5REFHWiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNyRSw2R0FBUUE7d0NBQUNxRSxXQUFVOzs7Ozs7Ozs7Ozs4Q0FLeEIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ1pDLFlBQVlsRCxJQUFJLENBQUNzQixJQUFJOzs7Ozs7c0RBRXhCLDhEQUFDMEI7NENBQUlDLFdBQVU7O2dEQUNYQyxDQUFBQSxZQUFZbEQsSUFBSSxDQUFDb0IsSUFBSSxHQUFHLElBQUcsRUFBR2tDLE9BQU8sQ0FBQztnREFBRzs7Ozs7Ozs7Ozs7Ozs4Q0FLL0MsOERBQUNDO29DQUNDN0IsTUFBSztvQ0FDTDhCLFNBQVMsSUFBTWhCLFdBQVdVLFlBQVlyQixFQUFFO29DQUN4Q29CLFdBQVU7OENBRVYsNEVBQUN0RSw2R0FBQ0E7d0NBQUNzRSxXQUFVOzs7Ozs7Ozs7Ozs7MkJBaENWQyxZQUFZckIsRUFBRTs7Ozs7Ozs7Ozs4QkF1QzNCLDhEQUFDNEI7b0JBQUtDLFVBQVVqRTtvQkFBY3dELFdBQVU7O3NDQUN0Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FDQ0MsV0FBV25FLDhDQUFFQSxDQUNYLCtGQUNBO29DQUVGNkUsUUFBUTlDO29DQUNSK0MsWUFBWTVDOztzREFFWiw4REFBQzRCOzRDQUNDaUIsS0FBS3RFOzRDQUNMdUUsT0FBTzNFOzRDQUNQNEUsVUFBVSxDQUFDckUsSUFBTU4sV0FBV00sRUFBRWlCLE1BQU0sQ0FBQ21ELEtBQUs7NENBQzFDRSxXQUFXNUQ7NENBQ1hsQixhQUFhQTs0Q0FDYkQsVUFBVUE7NENBQ1ZnRixNQUFNOzRDQUNOaEIsV0FBV25FLDhDQUFFQSxDQUNYLDZEQUNBLGdEQUNBLG1EQUNBOzs7Ozs7c0RBS0osOERBQUN5RTs0Q0FDQzdCLE1BQUs7NENBQ0w4QixTQUFTLElBQU1oRSxhQUFhUyxPQUFPLEVBQUVpRTs0Q0FDckNqRixVQUFVQTs0Q0FDVmdFLFdBQVduRSw4Q0FBRUEsQ0FDWCw4REFDQSx1Q0FDQTtzREFHRiw0RUFBQ0osNkdBQVNBO2dEQUFDdUUsV0FBVTs7Ozs7Ozs7Ozs7d0NBSXRCOUQsUUFBUVUsTUFBTSxHQUFHLG1CQUNoQiw4REFBQ21EOzRDQUFJQyxXQUFVO3NEQUNaOUQsUUFBUVUsTUFBTTs7Ozs7Ozs7Ozs7OzhDQU1yQiw4REFBQ3NFO29DQUNDTixLQUFLckU7b0NBQ0xrQyxNQUFLO29DQUNMMEMsUUFBUTtvQ0FDUkMsUUFBTztvQ0FDUE4sVUFBVXhEO29DQUNWMEMsV0FBVTs7Ozs7Ozs7Ozs7O3NDQUlkLDhEQUFDcEUseURBQU1BOzRCQUNMNkMsTUFBSzs0QkFDTHpDLFVBQVUsQ0FBRUUsUUFBUVMsSUFBSSxNQUFNUCxNQUFNUSxNQUFNLEtBQUssS0FBTVo7NEJBQ3JEZ0UsV0FBV25FLDhDQUFFQSxDQUNYLDRDQUNBLDRDQUNBO3NDQUdERyx5QkFDQyw4REFBQ1IsNkdBQU9BO2dDQUFDd0UsV0FBVTs7Ozs7cURBRW5CLDhEQUFDekUsNkdBQUlBO2dDQUFDeUUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTXRCLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFBeUM7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWhFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vc3JjL2NvbXBvbmVudHMvY2hhdC9jaGF0LWlucHV0LnRzeD9jMzg5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBTZW5kLCBMb2FkZXIyLCBQYXBlcmNsaXAsIFgsIEZpbGVUZXh0LCBJbWFnZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJ1xuXG5pbnRlcmZhY2UgQ2hhdElucHV0UHJvcHMge1xuICBvblNlbmRNZXNzYWdlOiAobWVzc2FnZTogc3RyaW5nLCBmaWxlcz86IEZpbGVbXSkgPT4gdm9pZFxuICBkaXNhYmxlZD86IGJvb2xlYW5cbiAgcGxhY2Vob2xkZXI/OiBzdHJpbmdcbn1cblxuaW50ZXJmYWNlIEZpbGVQcmV2aWV3IHtcbiAgZmlsZTogRmlsZVxuICBpZDogc3RyaW5nXG4gIHByZXZpZXc/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIENoYXRJbnB1dCh7XG4gIG9uU2VuZE1lc3NhZ2UsXG4gIGRpc2FibGVkID0gZmFsc2UsXG4gIHBsYWNlaG9sZGVyID0gXCLovpPlhaXmgqjnmoTmtojmga8uLi5cIlxufTogQ2hhdElucHV0UHJvcHMpIHtcbiAgY29uc3QgW21lc3NhZ2UsIHNldE1lc3NhZ2VdID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFtmaWxlcywgc2V0RmlsZXNdID0gdXNlU3RhdGU8RmlsZVByZXZpZXdbXT4oW10pXG4gIGNvbnN0IHRleHRhcmVhUmVmID0gdXNlUmVmPEhUTUxUZXh0QXJlYUVsZW1lbnQ+KG51bGwpXG4gIGNvbnN0IGZpbGVJbnB1dFJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50PihudWxsKVxuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICBpZiAoKG1lc3NhZ2UudHJpbSgpIHx8IGZpbGVzLmxlbmd0aCA+IDApICYmICFkaXNhYmxlZCkge1xuICAgICAgb25TZW5kTWVzc2FnZShtZXNzYWdlLnRyaW0oKSwgZmlsZXMubWFwKGYgPT4gZi5maWxlKSlcbiAgICAgIHNldE1lc3NhZ2UoJycpXG4gICAgICBzZXRGaWxlcyhbXSlcbiAgICAgIC8vIOmHjee9rnRleHRhcmVh6auY5bqmXG4gICAgICBpZiAodGV4dGFyZWFSZWYuY3VycmVudCkge1xuICAgICAgICB0ZXh0YXJlYVJlZi5jdXJyZW50LnN0eWxlLmhlaWdodCA9ICdhdXRvJ1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgICBoYW5kbGVTdWJtaXQoZSlcbiAgICB9XG4gIH1cblxuICAvLyDlpITnkIbmlofku7bpgInmi6lcbiAgY29uc3QgaGFuZGxlRmlsZVNlbGVjdCA9IChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IHNlbGVjdGVkRmlsZXMgPSBBcnJheS5mcm9tKGUudGFyZ2V0LmZpbGVzIHx8IFtdKVxuICAgIGhhbmRsZUZpbGVzKHNlbGVjdGVkRmlsZXMpXG4gIH1cblxuICAvLyDlpITnkIbmlofku7bmi5bmi71cbiAgY29uc3QgaGFuZGxlRHJvcCA9IChlOiBSZWFjdC5EcmFnRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICBjb25zdCBkcm9wcGVkRmlsZXMgPSBBcnJheS5mcm9tKGUuZGF0YVRyYW5zZmVyLmZpbGVzKVxuICAgIGhhbmRsZUZpbGVzKGRyb3BwZWRGaWxlcylcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZURyYWdPdmVyID0gKGU6IFJlYWN0LkRyYWdFdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKVxuICB9XG5cbiAgLy8g57uf5LiA5aSE55CG5paH5Lu2XG4gIGNvbnN0IGhhbmRsZUZpbGVzID0gKG5ld0ZpbGVzOiBGaWxlW10pID0+IHtcbiAgICBjb25zdCB2YWxpZEZpbGVzID0gbmV3RmlsZXMuZmlsdGVyKGZpbGUgPT4ge1xuICAgICAgLy8g5qOA5p+l5paH5Lu25aSn5bCPICgxME1C6ZmQ5Yi2KVxuICAgICAgaWYgKGZpbGUuc2l6ZSA+IDEwICogMTAyNCAqIDEwMjQpIHtcbiAgICAgICAgYWxlcnQoYOaWh+S7tiAke2ZpbGUubmFtZX0g6LaF6L+HMTBNQumZkOWItmApXG4gICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgfVxuXG4gICAgICAvLyDmo4Dmn6Xmlofku7bnsbvlnotcbiAgICAgIGNvbnN0IGFsbG93ZWRUeXBlcyA9IFtcbiAgICAgICAgJ2ltYWdlL2pwZWcnLCAnaW1hZ2UvcG5nJywgJ2ltYWdlL2dpZicsICdpbWFnZS93ZWJwJyxcbiAgICAgICAgJ3RleHQvcGxhaW4nLCAndGV4dC9tYXJrZG93bicsICdhcHBsaWNhdGlvbi9wZGYnLFxuICAgICAgICAndGV4dC9qYXZhc2NyaXB0JywgJ3RleHQvdHlwZXNjcmlwdCcsICd0ZXh0L2h0bWwnLCAndGV4dC9jc3MnLFxuICAgICAgICAnYXBwbGljYXRpb24vanNvbicsICd0ZXh0L2NzdidcbiAgICAgIF1cblxuICAgICAgY29uc3QgaXNWYWxpZFR5cGUgPSBhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKSB8fFxuICAgICAgICBmaWxlLm5hbWUubWF0Y2goL1xcLihweXxqc3x0c3x0c3h8anN4fGh0bWx8Y3NzfG1kfHR4dHxqc29ufGNzdikkL2kpXG5cbiAgICAgIGlmICghaXNWYWxpZFR5cGUpIHtcbiAgICAgICAgYWxlcnQoYOS4jeaUr+aMgeeahOaWh+S7tuexu+WeizogJHtmaWxlLm5hbWV9YClcbiAgICAgICAgcmV0dXJuIGZhbHNlXG4gICAgICB9XG5cbiAgICAgIHJldHVybiB0cnVlXG4gICAgfSlcblxuICAgIGNvbnN0IG5ld0ZpbGVQcmV2aWV3czogRmlsZVByZXZpZXdbXSA9IHZhbGlkRmlsZXMubWFwKGZpbGUgPT4gKHtcbiAgICAgIGZpbGUsXG4gICAgICBpZDogTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpLFxuICAgICAgcHJldmlldzogZmlsZS50eXBlLnN0YXJ0c1dpdGgoJ2ltYWdlLycpID8gVVJMLmNyZWF0ZU9iamVjdFVSTChmaWxlKSA6IHVuZGVmaW5lZFxuICAgIH0pKVxuXG4gICAgc2V0RmlsZXMocHJldiA9PiBbLi4ucHJldiwgLi4ubmV3RmlsZVByZXZpZXdzXSlcbiAgfVxuXG4gIC8vIOenu+mZpOaWh+S7tlxuICBjb25zdCByZW1vdmVGaWxlID0gKGlkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRGaWxlcyhwcmV2ID0+IHtcbiAgICAgIGNvbnN0IGZpbGVUb1JlbW92ZSA9IHByZXYuZmluZChmID0+IGYuaWQgPT09IGlkKVxuICAgICAgaWYgKGZpbGVUb1JlbW92ZT8ucHJldmlldykge1xuICAgICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKGZpbGVUb1JlbW92ZS5wcmV2aWV3KVxuICAgICAgfVxuICAgICAgcmV0dXJuIHByZXYuZmlsdGVyKGYgPT4gZi5pZCAhPT0gaWQpXG4gICAgfSlcbiAgfVxuXG4gIC8vIOiHquWKqOiwg+aVtHRleHRhcmVh6auY5bqmXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGV4dGFyZWEgPSB0ZXh0YXJlYVJlZi5jdXJyZW50XG4gICAgaWYgKHRleHRhcmVhKSB7XG4gICAgICB0ZXh0YXJlYS5zdHlsZS5oZWlnaHQgPSAnYXV0bydcbiAgICAgIHRleHRhcmVhLnN0eWxlLmhlaWdodCA9IGAke01hdGgubWluKHRleHRhcmVhLnNjcm9sbEhlaWdodCwgMTIwKX1weGBcbiAgICB9XG4gIH0sIFttZXNzYWdlXSlcblxuICAvLyDmuIXnkIbpooTop4hVUkxcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZmlsZXMuZm9yRWFjaChmaWxlID0+IHtcbiAgICAgICAgaWYgKGZpbGUucHJldmlldykge1xuICAgICAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwoZmlsZS5wcmV2aWV3KVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH1cbiAgfSwgW10pXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTIwMC82MCBiZy13aGl0ZS84MCBiYWNrZHJvcC1ibHVyLXNtIHAtNlwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy00eGwgbXgtYXV0b1wiPlxuICAgICAgICB7Lyog5paH5Lu26aKE6KeI5Yy65Z+fICovfVxuICAgICAgICB7ZmlsZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IGZsZXggZmxleC13cmFwIGdhcC0yXCI+XG4gICAgICAgICAgICB7ZmlsZXMubWFwKChmaWxlUHJldmlldykgPT4gKFxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAga2V5PXtmaWxlUHJldmlldy5pZH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBncm91cCBiZy1ncmF5LTUwIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBwLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWF4LXcteHNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgey8qIOaWh+S7tuWbvuagh+aIlumihOiniCAqL31cbiAgICAgICAgICAgICAgICB7ZmlsZVByZXZpZXcucHJldmlldyA/IChcbiAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgc3JjPXtmaWxlUHJldmlldy5wcmV2aWV3fVxuICAgICAgICAgICAgICAgICAgICBhbHQ9e2ZpbGVQcmV2aWV3LmZpbGUubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy04IGgtOCBvYmplY3QtY292ZXIgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctYmx1ZS0xMDAgcm91bmRlZCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7Lyog5paH5Lu25L+h5oGvICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmaWxlUHJldmlldy5maWxlLm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHsoZmlsZVByZXZpZXcuZmlsZS5zaXplIC8gMTAyNCkudG9GaXhlZCgxKX0gS0JcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIOWIoOmZpOaMiemSriAqL31cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZUZpbGUoZmlsZVByZXZpZXcuaWQpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBwLTEgaG92ZXI6YmctZ3JheS0yMDAgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy0zIGgtMyB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJmbGV4IGdhcC0zIGl0ZW1zLWVuZFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgXCJyZWxhdGl2ZSByb3VuZGVkLWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBiZy13aGl0ZSBzaGFkb3ctc20gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCIsXG4gICAgICAgICAgICAgICAgXCJmb2N1cy13aXRoaW46cmluZy0yIGZvY3VzLXdpdGhpbjpyaW5nLWJsdWUtNTAwIGZvY3VzLXdpdGhpbjpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICBvbkRyb3A9e2hhbmRsZURyb3B9XG4gICAgICAgICAgICAgIG9uRHJhZ092ZXI9e2hhbmRsZURyYWdPdmVyfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICByZWY9e3RleHRhcmVhUmVmfVxuICAgICAgICAgICAgICAgIHZhbHVlPXttZXNzYWdlfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TWVzc2FnZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgb25LZXlEb3duPXtoYW5kbGVLZXlEb3dufVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtwbGFjZWhvbGRlcn1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XG4gICAgICAgICAgICAgICAgcm93cz17MX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgXCJ3LWZ1bGwgcmVzaXplLW5vbmUgYmctdHJhbnNwYXJlbnQgcHgtNiBweS00IHByLTEyIHRleHQtc21cIixcbiAgICAgICAgICAgICAgICAgIFwicGxhY2Vob2xkZXI6dGV4dC1ncmF5LTUwMCBmb2N1czpvdXRsaW5lLW5vbmVcIixcbiAgICAgICAgICAgICAgICAgIFwiZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICAgICAgICAgIFwibWluLWgtWzUycHhdIG1heC1oLVsxMjBweF0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgIHsvKiDmlofku7bkuIrkvKDmjInpkq4gKi99XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBmaWxlSW5wdXRSZWYuY3VycmVudD8uY2xpY2soKX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgIFwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMS8yIC10cmFuc2xhdGUteS0xLzIgcC0yIHJvdW5kZWQtZnVsbFwiLFxuICAgICAgICAgICAgICAgICAgXCJob3ZlcjpiZy1ncmF5LTEwMCB0cmFuc2l0aW9uLWNvbG9yc1wiLFxuICAgICAgICAgICAgICAgICAgXCJkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxQYXBlcmNsaXAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgey8qIOWtl+espuiuoeaVsCAqL31cbiAgICAgICAgICAgICAge21lc3NhZ2UubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMiByaWdodC0xMiB0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgIHttZXNzYWdlLmxlbmd0aH1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog6ZqQ6JeP55qE5paH5Lu26L6T5YWlICovfVxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHJlZj17ZmlsZUlucHV0UmVmfVxuICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICAgIG11bHRpcGxlXG4gICAgICAgICAgICAgIGFjY2VwdD1cImltYWdlLyosLnR4dCwubWQsLnBkZiwucHksLmpzLC50cywudHN4LC5qc3gsLmh0bWwsLmNzcywuanNvbiwuY3N2XCJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUZpbGVTZWxlY3R9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhpZGRlblwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICBkaXNhYmxlZD17KCFtZXNzYWdlLnRyaW0oKSAmJiBmaWxlcy5sZW5ndGggPT09IDApIHx8IGRpc2FibGVkfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgXCJoLVs1MnB4XSB3LVs1MnB4XSByb3VuZGVkLWZ1bGwgc2hhZG93LXNtXCIsXG4gICAgICAgICAgICAgIFwiYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdGV4dC13aGl0ZVwiLFxuICAgICAgICAgICAgICBcImRpc2FibGVkOmJnLWdyYXktMzAwIGRpc2FibGVkOnRleHQtZ3JheS01MDBcIlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7ZGlzYWJsZWQgPyAoXG4gICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtNSB3LTUgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxTZW5kIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9mb3JtPlxuXG4gICAgICAgIHsvKiDmj5DnpLrmlofmnKwgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMyB0ZXh0LXhzIHRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICDmjIkgRW50ZXIg5Y+R6YCB77yMU2hpZnQgKyBFbnRlciDmjaLooYwg4oCiIOaUr+aMgeaLluaLveaWh+S7tuaIlueCueWHuyDwn5OOIOS4iuS8oFxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsIlNlbmQiLCJMb2FkZXIyIiwiUGFwZXJjbGlwIiwiWCIsIkZpbGVUZXh0IiwiQnV0dG9uIiwiY24iLCJDaGF0SW5wdXQiLCJvblNlbmRNZXNzYWdlIiwiZGlzYWJsZWQiLCJwbGFjZWhvbGRlciIsIm1lc3NhZ2UiLCJzZXRNZXNzYWdlIiwiZmlsZXMiLCJzZXRGaWxlcyIsInRleHRhcmVhUmVmIiwiZmlsZUlucHV0UmVmIiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwidHJpbSIsImxlbmd0aCIsIm1hcCIsImYiLCJmaWxlIiwiY3VycmVudCIsInN0eWxlIiwiaGVpZ2h0IiwiaGFuZGxlS2V5RG93biIsImtleSIsInNoaWZ0S2V5IiwiaGFuZGxlRmlsZVNlbGVjdCIsInNlbGVjdGVkRmlsZXMiLCJBcnJheSIsImZyb20iLCJ0YXJnZXQiLCJoYW5kbGVGaWxlcyIsImhhbmRsZURyb3AiLCJkcm9wcGVkRmlsZXMiLCJkYXRhVHJhbnNmZXIiLCJoYW5kbGVEcmFnT3ZlciIsIm5ld0ZpbGVzIiwidmFsaWRGaWxlcyIsImZpbHRlciIsInNpemUiLCJhbGVydCIsIm5hbWUiLCJhbGxvd2VkVHlwZXMiLCJpc1ZhbGlkVHlwZSIsImluY2x1ZGVzIiwidHlwZSIsIm1hdGNoIiwibmV3RmlsZVByZXZpZXdzIiwiaWQiLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJwcmV2aWV3Iiwic3RhcnRzV2l0aCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsInVuZGVmaW5lZCIsInByZXYiLCJyZW1vdmVGaWxlIiwiZmlsZVRvUmVtb3ZlIiwiZmluZCIsInJldm9rZU9iamVjdFVSTCIsInRleHRhcmVhIiwibWluIiwic2Nyb2xsSGVpZ2h0IiwiZm9yRWFjaCIsImRpdiIsImNsYXNzTmFtZSIsImZpbGVQcmV2aWV3IiwiaW1nIiwic3JjIiwiYWx0IiwidG9GaXhlZCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJmb3JtIiwib25TdWJtaXQiLCJvbkRyb3AiLCJvbkRyYWdPdmVyIiwicmVmIiwidmFsdWUiLCJvbkNoYW5nZSIsIm9uS2V5RG93biIsInJvd3MiLCJjbGljayIsImlucHV0IiwibXVsdGlwbGUiLCJhY2NlcHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/chat-input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/message.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/message.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Message: () => (/* binding */ Message)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/gemini-markdown */ \"(ssr)/./src/components/ui/gemini-markdown.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Message auto */ \n\n\n\n\n\n\n\nfunction Message({ message, isStreaming = false }) {\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    const handleCopy = async ()=>{\n        try {\n            await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(message.content);\n        // 这里可以添加一个toast通知\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex gap-4 p-6 chat-message max-w-5xl mx-auto\", isUser ? \"flex-row-reverse\" : \"flex-row\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center\", isUser ? \"bg-blue-600 text-white\" : \"bg-gradient-to-br from-blue-500 to-purple-600 text-white\"),\n                children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 19\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 40\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-1 min-w-0\", isUser ? \"text-right\" : \"text-left\"),\n                children: [\n                    message.files && message.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mb-3 flex flex-wrap gap-2\", isUser ? \"justify-end\" : \"justify-start\"),\n                        children: message.files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 p-2 rounded-lg border max-w-xs\", isUser ? \"bg-blue-50 border-blue-200 text-blue-900\" : \"bg-gray-50 border-gray-200 text-gray-900\"),\n                                children: [\n                                    file.type.startsWith(\"image/\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-medium truncate\",\n                                                children: file.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-70\",\n                                                children: [\n                                                    (file.size / 1024).toFixed(1),\n                                                    \" KB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, this),\n                                    file.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0\",\n                                        onClick: ()=>window.open(file.url, \"_blank\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(isUser ? \"bg-blue-600 text-white p-4 rounded-2xl ml-auto max-w-2xl shadow-sm inline-block\" : \"bg-transparent text-gray-900 w-full\", isStreaming && \"animate-pulse\"),\n                        children: [\n                            isAssistant ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__.GeminiMarkdown, {\n                                content: message.content,\n                                className: \"leading-relaxed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"whitespace-pre-wrap leading-relaxed\",\n                                children: message.content\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 mt-3 text-xs opacity-70\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-current rounded-full animate-bounce\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0.1s\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0.2s\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"正在输入...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 mt-2 text-xs text-gray-500\", isUser ? \"justify-end\" : \"justify-start\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.formatTime)(message.created_at)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            message.processing_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                size: 12\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    message.processing_time.toFixed(2),\n                                                    \"s\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            message.model_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400\",\n                                        children: message.model_name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 hover:bg-gray-100\",\n                                onClick: handleCopy,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 12\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    message.status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-xs text-red-400 flex items-center gap-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"⚠️ 消息发送失败\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/message.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/sidebar.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/sidebar.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Settings,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Settings,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Settings,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Settings,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(ssr)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\nfunction Sidebar({ conversations, currentConversationId, onSelectConversation, onNewConversation, onDeleteConversation, isLoading = false }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 h-full gemini-sidebar flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-semibold gradient-text\",\n                                children: \"Liangxi AI\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"hover:bg-gray-100/80\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: onNewConversation,\n                        className: \"w-full bg-blue-600 hover:bg-blue-700 text-white rounded-full py-3 shadow-sm\",\n                        disabled: isLoading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this),\n                            \"新建对话\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3\",\n                    children: conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500 py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-3 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"暂无对话\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 text-gray-400\",\n                                children: '点击\"新建对话\"开始聊天'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConversationItem, {\n                                conversation: conversation,\n                                isActive: conversation.id === currentConversationId,\n                                onSelect: ()=>onSelectConversation(conversation.id),\n                                onDelete: ()=>onDeleteConversation(conversation.id)\n                            }, conversation.id, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200/60 text-xs text-gray-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"共 \",\n                                conversations.length,\n                                \" 个对话\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-400\",\n                            children: \"v1.0.0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction ConversationItem({ conversation, isActive, onSelect, onDelete }) {\n    const handleDelete = (e)=>{\n        e.stopPropagation();\n        if (confirm(\"确定要删除这个对话吗？\")) {\n            onDelete();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group relative p-3 rounded-xl cursor-pointer transition-all duration-200\", \"hover:bg-white/60 hover:shadow-sm\", isActive && \"bg-white/80 shadow-sm border border-blue-200/50\"),\n        onClick: onSelect,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start justify-between gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"font-medium text-sm truncate\", isActive ? \"text-gray-900\" : \"text-gray-700\"),\n                            children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.truncateText)(conversation.title, 30)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mt-1 text-xs text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.formatTime)(conversation.updated_at)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                conversation.message_count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                conversation.message_count,\n                                                \" 条消息\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    className: \"opacity-0 group-hover:opacity-100 transition-all duration-200 h-7 w-7 p-0 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg flex-shrink-0\",\n                    onClick: handleDelete,\n                    title: \"删除对话\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/gemini-markdown.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/gemini-markdown.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeminiMarkdown: () => (/* binding */ GeminiMarkdown)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ GeminiMarkdown auto */ \n\n\n\n\n\n\n\nfunction GeminiMarkdown({ content, className }) {\n    const [copiedBlocks, setCopiedBlocks] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(new Set());\n    const handleCopy = async (text, blockId)=>{\n        try {\n            await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.copyToClipboard)(text);\n            setCopiedBlocks((prev)=>new Set(prev).add(blockId));\n            setTimeout(()=>{\n                setCopiedBlocks((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(blockId);\n                    return newSet;\n                });\n            }, 2000);\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"gemini-markdown prose prose-gray max-w-none\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n            ],\n            components: {\n                // 标题样式 - 类似Gemini\n                h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-semibold text-gray-900 mt-6 mb-4 first:mt-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, void 0),\n                h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mt-5 mb-3 first:mt-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 13\n                    }, void 0),\n                h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-semibold text-gray-900 mt-4 mb-2 first:mt-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 13\n                    }, void 0),\n                h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-semibold text-gray-900 mt-3 mb-2 first:mt-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, void 0),\n                // 段落样式\n                p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 leading-relaxed mb-4 last:mb-0\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, void 0),\n                // 列表样式 - 类似Gemini的清晰层次\n                ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-1 mb-4\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, void 0),\n                ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"space-y-1 mb-4 list-decimal list-inside\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0),\n                li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-gray-700 leading-relaxed flex items-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-3 mt-2 w-1 h-1 bg-gray-500 rounded-full flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 13\n                    }, void 0),\n                // 代码块 - 类似Gemini的深色主题\n                code: ({ className, children, ...props })=>{\n                    const match = /language-(\\w+)/.exec(className || \"\");\n                    const language = match ? match[1] : \"\";\n                    const codeContent = String(children).replace(/\\n$/, \"\");\n                    const blockId = `code-${Math.random().toString(36).substring(2, 11)}`;\n                    const isInline = !className;\n                    if (!isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group my-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between bg-gray-800 text-gray-200 px-4 py-2 rounded-t-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-300\",\n                                            children: language || \"code\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>handleCopy(codeContent, blockId),\n                                            className: \"h-6 w-6 p-0 text-gray-400 hover:text-white hover:bg-gray-700 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                            children: copiedBlocks.has(blockId) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 52\n                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                size: 14\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 74\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"bg-gray-900 text-gray-100 p-4 rounded-b-lg overflow-x-auto m-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"text-sm font-mono whitespace-pre text-gray-100\",\n                                        ...props,\n                                        children: children\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    // 行内代码\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono border\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // 引用块\n                blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-blue-500 pl-4 py-2 mb-4 bg-blue-50/50 text-gray-700 italic\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 13\n                    }, void 0),\n                // 链接\n                a: ({ href, children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"text-blue-600 hover:text-blue-800 underline decoration-blue-600/30 hover:decoration-blue-800/50 transition-colors\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 13\n                    }, void 0),\n                // 强调文本\n                strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 13\n                    }, void 0),\n                // 斜体\n                em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-gray-700\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, void 0),\n                // 删除线\n                del: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"del\", {\n                        className: \"line-through text-gray-500\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 13\n                    }, void 0),\n                // 分割线\n                hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"my-6 border-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, void 0),\n                // 表格\n                table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto my-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border border-gray-200 rounded-lg\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 13\n                    }, void 0),\n                thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 13\n                    }, void 0),\n                tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 13\n                    }, void 0),\n                tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: \"hover:bg-gray-50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 13\n                    }, void 0),\n                th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"px-4 py-2 text-left text-sm font-semibold text-gray-900 border-b border-gray-200\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 13\n                    }, void 0),\n                td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"px-4 py-2 text-sm text-gray-700\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, void 0)\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/gemini-markdown.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/gemini-markdown.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/scroll-area.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/scroll-area.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollBar: () => (/* binding */ ScrollBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative overflow-hidden\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                className: \"h-full w-full rounded-[inherit]\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/scroll-area.tsx\",\n                lineNumber: 14,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/scroll-area.tsx\",\n                lineNumber: 17,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/scroll-area.tsx\",\n                lineNumber: 18,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/scroll-area.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nScrollArea.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst ScrollBar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"vertical\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        ref: ref,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none select-none transition-colors\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent p-[1px]\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent p-[1px]\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            className: \"relative flex-1 rounded-full bg-border\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/scroll-area.tsx\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/ui/scroll-area.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nScrollBar.displayName = _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9zY3JvbGwtYXJlYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ29DO0FBQ2xDO0FBRWhDLE1BQU1HLDJCQUFhSCw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSyxTQUFTLEVBQUVDLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUNwQyw4REFBQ1AsNkRBQXdCO1FBQ3ZCTyxLQUFLQTtRQUNMSCxXQUFXSCw4Q0FBRUEsQ0FBQyw0QkFBNEJHO1FBQ3pDLEdBQUdFLEtBQUs7OzBCQUVULDhEQUFDTixpRUFBNEI7Z0JBQUNJLFdBQVU7MEJBQ3JDQzs7Ozs7OzBCQUVILDhEQUFDSzs7Ozs7MEJBQ0QsOERBQUNWLCtEQUEwQjs7Ozs7Ozs7Ozs7QUFHL0JFLFdBQVdVLFdBQVcsR0FBR1osNkRBQXdCLENBQUNZLFdBQVc7QUFFN0QsTUFBTUYsMEJBQVlYLDZDQUFnQixDQUdoQyxDQUFDLEVBQUVLLFNBQVMsRUFBRVMsY0FBYyxVQUFVLEVBQUUsR0FBR1AsT0FBTyxFQUFFQyxvQkFDcEQsOERBQUNQLDRFQUF1QztRQUN0Q08sS0FBS0E7UUFDTE0sYUFBYUE7UUFDYlQsV0FBV0gsOENBQUVBLENBQ1gsaURBQ0FZLGdCQUFnQixjQUNkLHNEQUNGQSxnQkFBZ0IsZ0JBQ2Qsd0RBQ0ZUO1FBRUQsR0FBR0UsS0FBSztrQkFFVCw0RUFBQ04sd0VBQW1DO1lBQUNJLFdBQVU7Ozs7Ozs7Ozs7O0FBR25ETSxVQUFVRSxXQUFXLEdBQUdaLDRFQUF1QyxDQUFDWSxXQUFXO0FBRTNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vc3JjL2NvbXBvbmVudHMvdWkvc2Nyb2xsLWFyZWEudHN4PzI4MTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIFNjcm9sbEFyZWFQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zY3JvbGwtYXJlYVwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IFNjcm9sbEFyZWEgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBTY3JvbGxBcmVhUHJpbWl0aXZlLlJvb3Q+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFNjcm9sbEFyZWFQcmltaXRpdmUuUm9vdD5cbj4oKHsgY2xhc3NOYW1lLCBjaGlsZHJlbiwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxTY3JvbGxBcmVhUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwicmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICA+XG4gICAgPFNjcm9sbEFyZWFQcmltaXRpdmUuVmlld3BvcnQgY2xhc3NOYW1lPVwiaC1mdWxsIHctZnVsbCByb3VuZGVkLVtpbmhlcml0XVwiPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvU2Nyb2xsQXJlYVByaW1pdGl2ZS5WaWV3cG9ydD5cbiAgICA8U2Nyb2xsQmFyIC8+XG4gICAgPFNjcm9sbEFyZWFQcmltaXRpdmUuQ29ybmVyIC8+XG4gIDwvU2Nyb2xsQXJlYVByaW1pdGl2ZS5Sb290PlxuKSlcblNjcm9sbEFyZWEuZGlzcGxheU5hbWUgPSBTY3JvbGxBcmVhUHJpbWl0aXZlLlJvb3QuZGlzcGxheU5hbWVcblxuY29uc3QgU2Nyb2xsQmFyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgU2Nyb2xsQXJlYVByaW1pdGl2ZS5TY3JvbGxBcmVhU2Nyb2xsYmFyPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBTY3JvbGxBcmVhUHJpbWl0aXZlLlNjcm9sbEFyZWFTY3JvbGxiYXI+XG4+KCh7IGNsYXNzTmFtZSwgb3JpZW50YXRpb24gPSBcInZlcnRpY2FsXCIsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8U2Nyb2xsQXJlYVByaW1pdGl2ZS5TY3JvbGxBcmVhU2Nyb2xsYmFyXG4gICAgcmVmPXtyZWZ9XG4gICAgb3JpZW50YXRpb249e29yaWVudGF0aW9ufVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcImZsZXggdG91Y2gtbm9uZSBzZWxlY3Qtbm9uZSB0cmFuc2l0aW9uLWNvbG9yc1wiLFxuICAgICAgb3JpZW50YXRpb24gPT09IFwidmVydGljYWxcIiAmJlxuICAgICAgICBcImgtZnVsbCB3LTIuNSBib3JkZXItbCBib3JkZXItbC10cmFuc3BhcmVudCBwLVsxcHhdXCIsXG4gICAgICBvcmllbnRhdGlvbiA9PT0gXCJob3Jpem9udGFsXCIgJiZcbiAgICAgICAgXCJoLTIuNSBmbGV4LWNvbCBib3JkZXItdCBib3JkZXItdC10cmFuc3BhcmVudCBwLVsxcHhdXCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgPlxuICAgIDxTY3JvbGxBcmVhUHJpbWl0aXZlLlNjcm9sbEFyZWFUaHVtYiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBmbGV4LTEgcm91bmRlZC1mdWxsIGJnLWJvcmRlclwiIC8+XG4gIDwvU2Nyb2xsQXJlYVByaW1pdGl2ZS5TY3JvbGxBcmVhU2Nyb2xsYmFyPlxuKSlcblNjcm9sbEJhci5kaXNwbGF5TmFtZSA9IFNjcm9sbEFyZWFQcmltaXRpdmUuU2Nyb2xsQXJlYVNjcm9sbGJhci5kaXNwbGF5TmFtZVxuXG5leHBvcnQgeyBTY3JvbGxBcmVhLCBTY3JvbGxCYXIgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2Nyb2xsQXJlYVByaW1pdGl2ZSIsImNuIiwiU2Nyb2xsQXJlYSIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJjaGlsZHJlbiIsInByb3BzIiwicmVmIiwiUm9vdCIsIlZpZXdwb3J0IiwiU2Nyb2xsQmFyIiwiQ29ybmVyIiwiZGlzcGxheU5hbWUiLCJvcmllbnRhdGlvbiIsIlNjcm9sbEFyZWFTY3JvbGxiYXIiLCJTY3JvbGxBcmVhVGh1bWIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/scroll-area.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\nconst API_BASE =  false ? 0 : \"http://localhost:8000/api\";\nclass ApiClient {\n    constructor(baseUrl = API_BASE){\n        this.baseUrl = baseUrl;\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseUrl}${endpoint}`;\n        const response = await fetch(url, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API请求失败: ${response.status} ${error}`);\n        }\n        return response.json();\n    }\n    // 对话管理\n    async createConversation(title = \"新对话\") {\n        return this.request(\"/chat/conversations\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                title\n            })\n        });\n    }\n    async getConversations() {\n        return this.request(\"/chat/conversations\");\n    }\n    async getConversationMessages(conversationId) {\n        return this.request(`/chat/conversations/${conversationId}/messages`);\n    }\n    async deleteConversation(conversationId) {\n        await this.request(`/chat/conversations/${conversationId}`, {\n            method: \"DELETE\"\n        });\n    }\n    // 聊天功能\n    async sendMessage(request) {\n        return this.request(\"/chat/send\", {\n            method: \"POST\",\n            body: JSON.stringify(request)\n        });\n    }\n    // SSE流式聊天\n    async *streamChat(conversationId, message, files) {\n        let url;\n        let response;\n        if (files && files.length > 0) {\n            // 如果有文件，使用POST请求发送multipart/form-data\n            const formData = new FormData();\n            formData.append(\"message\", message);\n            formData.append(\"conversation_id\", conversationId.toString());\n            files.forEach((file, index)=>{\n                formData.append(`files`, file);\n            });\n            response = await fetch(`${this.baseUrl}/chat/stream-with-files`, {\n                method: \"POST\",\n                headers: {\n                    \"Accept\": \"text/event-stream\",\n                    \"Cache-Control\": \"no-cache\"\n                },\n                body: formData\n            });\n        } else {\n            // 没有文件时使用原来的GET请求\n            url = `${this.baseUrl}/chat/stream/${conversationId}?message=${encodeURIComponent(message)}`;\n            response = await fetch(url, {\n                headers: {\n                    \"Accept\": \"text/event-stream\",\n                    \"Cache-Control\": \"no-cache\"\n                }\n            });\n        }\n        if (!response.ok) {\n            throw new Error(`流式请求失败: ${response.status}`);\n        }\n        const reader = response.body?.getReader();\n        if (!reader) {\n            throw new Error(\"无法获取响应流\");\n        }\n        const decoder = new TextDecoder();\n        let buffer = \"\";\n        try {\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                buffer += decoder.decode(value, {\n                    stream: true\n                });\n                const lines = buffer.split(\"\\n\");\n                buffer = lines.pop() || \"\";\n                for (const line of lines){\n                    if (line.startsWith(\"data: \")) {\n                        const data = line.slice(6).trim();\n                        if (data === \"[DONE]\") {\n                            return;\n                        }\n                        try {\n                            const chunk = JSON.parse(data);\n                            yield chunk;\n                        } catch (e) {\n                            console.warn(\"解析SSE数据失败:\", data);\n                        }\n                    }\n                }\n            }\n        } finally{\n            reader.releaseLock();\n        }\n    }\n    // 健康检查\n    async healthCheck() {\n        return this.request(\"/chat/health\");\n    }\n}\nconst apiClient = new ApiClient();\n// 工具函数\nconst formatTime = (dateString)=>{\n    const date = new Date(dateString);\n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    const minutes = Math.floor(diff / 60000);\n    const hours = Math.floor(diff / 3600000);\n    const days = Math.floor(diff / 86400000);\n    if (minutes < 1) return \"刚刚\";\n    if (minutes < 60) return `${minutes}分钟前`;\n    if (hours < 24) return `${hours}小时前`;\n    if (days < 7) return `${days}天前`;\n    return date.toLocaleDateString(\"zh-CN\");\n};\nconst truncateText = (text, maxLength = 50)=>{\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/dev-utils.ts":
/*!******************************!*\
  !*** ./src/lib/dev-utils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initDevelopmentUtils: () => (/* binding */ initDevelopmentUtils),\n/* harmony export */   suppressReactDevToolsWarning: () => (/* binding */ suppressReactDevToolsWarning)\n/* harmony export */ });\n// 开发环境工具函数\n/**\n * 抑制 React DevTools 警告\n */ function suppressReactDevToolsWarning() {\n    if (false) {}\n}\n/**\n * 开发环境初始化\n */ function initDevelopmentUtils() {\n    if (true) {\n        suppressReactDevToolsWarning();\n        // 添加开发环境的全局样式\n        if (false) {}\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/dev-utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n\nfunction cn(...inputs) {\n    return (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs);\n}\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\nfunction debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\nfunction copyToClipboard(text) {\n    if (navigator.clipboard && window.isSecureContext) {\n        return navigator.clipboard.writeText(text);\n    } else {\n        // 降级方案\n        const textArea = document.createElement(\"textarea\");\n        textArea.value = text;\n        textArea.style.position = \"absolute\";\n        textArea.style.left = \"-999999px\";\n        document.body.prepend(textArea);\n        textArea.select();\n        try {\n            document.execCommand(\"copy\");\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n            throw error;\n        } finally{\n            textArea.remove();\n        }\n        return Promise.resolve();\n    }\n}\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\nfunction isValidUrl(string) {\n    try {\n        new URL(string);\n        return true;\n    } catch (_) {\n        return false;\n    }\n}\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"47933f3411ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9iOWE2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNDc5MzNmMzQxMWVhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"AI系统 - 智能对话助手\",\n    description: \"一个功能完善的AI对话系统，具备现代化的界面设计和强大的AI对话能力\",\n    keywords: [\n        \"AI\",\n        \"人工智能\",\n        \"对话系统\",\n        \"ChatGPT\",\n        \"Autogen\"\n    ],\n    authors: [\n        {\n            name: \"AI系统开发团队\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        className: \"light\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/lucide-react","vendor-chunks/property-information","vendor-chunks/@radix-ui","vendor-chunks/micromark","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-gfm","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/character-entities","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/@babel","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();