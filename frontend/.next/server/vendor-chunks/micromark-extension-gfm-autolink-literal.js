"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-autolink-literal";
exports.ids = ["vendor-chunks/micromark-extension-gfm-autolink-literal"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteralHtml: () => (/* binding */ gfmAutolinkLiteralHtml)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @import {CompileContext, Handle, HtmlExtension, Token} from 'micromark-util-types'\n */ \n/**\n * Create an HTML extension for `micromark` to support GitHub autolink literal\n * when serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub autolink literal when serializing to HTML.\n */ function gfmAutolinkLiteralHtml() {\n    return {\n        exit: {\n            literalAutolinkEmail,\n            literalAutolinkHttp,\n            literalAutolinkWww\n        }\n    };\n}\n/**\n * @this {CompileContext}\n * @type {Handle}\n */ function literalAutolinkWww(token) {\n    anchorFromToken.call(this, token, \"http://\");\n}\n/**\n * @this {CompileContext}\n * @type {Handle}\n */ function literalAutolinkEmail(token) {\n    anchorFromToken.call(this, token, \"mailto:\");\n}\n/**\n * @this {CompileContext}\n * @type {Handle}\n */ function literalAutolinkHttp(token) {\n    anchorFromToken.call(this, token);\n}\n/**\n * @this CompileContext\n * @param {Token} token\n * @param {string | null | undefined} [protocol]\n * @returns {undefined}\n */ function anchorFromToken(token, protocol) {\n    const url = this.sliceSerialize(token);\n    this.tag('<a href=\"' + (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.sanitizeUri)((protocol || \"\") + url) + '\">');\n    this.raw(this.encode(url));\n    this.tag(\"</a>\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmAutolinkLiteral: () => (/* binding */ gfmAutolinkLiteral)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {Code, ConstructRecord, Event, Extension, Previous, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ \n\nconst wwwPrefix = {\n    tokenize: tokenizeWwwPrefix,\n    partial: true\n};\nconst domain = {\n    tokenize: tokenizeDomain,\n    partial: true\n};\nconst path = {\n    tokenize: tokenizePath,\n    partial: true\n};\nconst trail = {\n    tokenize: tokenizeTrail,\n    partial: true\n};\nconst emailDomainDotTrail = {\n    tokenize: tokenizeEmailDomainDotTrail,\n    partial: true\n};\nconst wwwAutolink = {\n    name: \"wwwAutolink\",\n    tokenize: tokenizeWwwAutolink,\n    previous: previousWww\n};\nconst protocolAutolink = {\n    name: \"protocolAutolink\",\n    tokenize: tokenizeProtocolAutolink,\n    previous: previousProtocol\n};\nconst emailAutolink = {\n    name: \"emailAutolink\",\n    tokenize: tokenizeEmailAutolink,\n    previous: previousEmail\n};\n/** @type {ConstructRecord} */ const text = {};\n/**\n * Create an extension for `micromark` to support GitHub autolink literal\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   autolink literal syntax.\n */ function gfmAutolinkLiteral() {\n    return {\n        text\n    };\n}\n/** @type {Code} */ let code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0;\n// Add alphanumerics.\nwhile(code < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftCurlyBrace){\n    text[code] = emailAutolink;\n    code++;\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseA;\n    else if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket) code = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseA;\n}\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign] = emailAutolink;\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash] = emailAutolink;\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot] = emailAutolink;\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore] = emailAutolink;\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseH] = [\n    emailAutolink,\n    protocolAutolink\n];\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseH] = [\n    emailAutolink,\n    protocolAutolink\n];\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW] = [\n    emailAutolink,\n    wwwAutolink\n];\ntext[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW] = [\n    emailAutolink,\n    wwwAutolink\n];\n// To do: perform email autolink literals on events, afterwards.\n// That’s where `markdown-rs` and `cmark-gfm` perform it.\n// It should look for `@`, then for atext backwards, and then for a label\n// forwards.\n// To do: `mailto:`, `xmpp:` protocol as prefix.\n/**\n * Email autolink literal.\n *\n * ```markdown\n * > | a <EMAIL> b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeEmailAutolink(effects, ok, nok) {\n    const self = this;\n    /** @type {boolean | undefined} */ let dot;\n    /** @type {boolean} */ let data;\n    return start;\n    /**\n   * Start of email autolink literal.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function start(code) {\n        if (!gfmAtext(code) || !previousEmail.call(self, self.previous) || previousUnbalanced(self.events)) {\n            return nok(code);\n        }\n        effects.enter(\"literalAutolink\");\n        effects.enter(\"literalAutolinkEmail\");\n        return atext(code);\n    }\n    /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function atext(code) {\n        if (gfmAtext(code)) {\n            effects.consume(code);\n            return atext;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.atSign) {\n            effects.consume(code);\n            return emailDomain;\n        }\n        return nok(code);\n    }\n    /**\n   * In email domain.\n   *\n   * The reference code is a bit overly complex as it handles the `@`, of which\n   * there may be just one.\n   * Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L318>\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */ function emailDomain(code) {\n        // Dot followed by alphanumerical (not `-` or `_`).\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot) {\n            return effects.check(emailDomainDotTrail, emailDomainAfter, emailDomainDot)(code);\n        }\n        // Alphanumerical, `-`, and `_`.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code)) {\n            data = true;\n            effects.consume(code);\n            return emailDomain;\n        }\n        // To do: `/` if xmpp.\n        // Note: normally we’d truncate trailing punctuation from the link.\n        // However, email autolink literals cannot contain any of those markers,\n        // except for `.`, but that can only occur if it isn’t trailing.\n        // So we can ignore truncating!\n        return emailDomainAfter(code);\n    }\n    /**\n   * In email domain, on dot that is not a trail.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                      ^\n   * ```\n   *\n   * @type {State}\n   */ function emailDomainDot(code) {\n        effects.consume(code);\n        dot = true;\n        return emailDomain;\n    }\n    /**\n   * After email domain.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */ function emailDomainAfter(code) {\n        // Domain must not be empty, must include a dot, and must end in alphabetical.\n        // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L332>.\n        if (data && dot && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(self.previous)) {\n            effects.exit(\"literalAutolinkEmail\");\n            effects.exit(\"literalAutolink\");\n            return ok(code);\n        }\n        return nok(code);\n    }\n}\n/**\n * `www` autolink literal.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeWwwAutolink(effects, ok, nok) {\n    const self = this;\n    return wwwStart;\n    /**\n   * Start of www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */ function wwwStart(code) {\n        if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW && code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW || !previousWww.call(self, self.previous) || previousUnbalanced(self.events)) {\n            return nok(code);\n        }\n        effects.enter(\"literalAutolink\");\n        effects.enter(\"literalAutolinkWww\");\n        // Note: we *check*, so we can discard the `www.` we parsed.\n        // If it worked, we consider it as a part of the domain.\n        return effects.check(wwwPrefix, effects.attempt(domain, effects.attempt(path, wwwAfter), nok), nok)(code);\n    }\n    /**\n   * After a www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */ function wwwAfter(code) {\n        effects.exit(\"literalAutolinkWww\");\n        effects.exit(\"literalAutolink\");\n        return ok(code);\n    }\n}\n/**\n * Protocol autolink literal.\n *\n * ```markdown\n * > | a https://example.org b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeProtocolAutolink(effects, ok, nok) {\n    const self = this;\n    let buffer = \"\";\n    let seen = false;\n    return protocolStart;\n    /**\n   * Start of protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */ function protocolStart(code) {\n        if ((code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseH || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseH) && previousProtocol.call(self, self.previous) && !previousUnbalanced(self.events)) {\n            effects.enter(\"literalAutolink\");\n            effects.enter(\"literalAutolinkHttp\");\n            buffer += String.fromCodePoint(code);\n            effects.consume(code);\n            return protocolPrefixInside;\n        }\n        return nok(code);\n    }\n    /**\n   * In protocol.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^^^^^\n   * ```\n   *\n   * @type {State}\n   */ function protocolPrefixInside(code) {\n        // `5` is size of `https`\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code) && buffer.length < 5) {\n            // @ts-expect-error: definitely number.\n            buffer += String.fromCodePoint(code);\n            effects.consume(code);\n            return protocolPrefixInside;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) {\n            const protocol = buffer.toLowerCase();\n            if (protocol === \"http\" || protocol === \"https\") {\n                effects.consume(code);\n                return protocolSlashesInside;\n            }\n        }\n        return nok(code);\n    }\n    /**\n   * In slashes.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *           ^^\n   * ```\n   *\n   * @type {State}\n   */ function protocolSlashesInside(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.slash) {\n            effects.consume(code);\n            if (seen) {\n                return afterProtocol;\n            }\n            seen = true;\n            return protocolSlashesInside;\n        }\n        return nok(code);\n    }\n    /**\n   * After protocol, before domain.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */ function afterProtocol(code) {\n        // To do: this is different from `markdown-rs`:\n        // https://github.com/wooorm/markdown-rs/blob/b3a921c761309ae00a51fe348d8a43adbc54b518/src/construct/gfm_autolink_literal.rs#L172-L182\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiControl)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code) ? nok(code) : effects.attempt(domain, effects.attempt(path, protocolAfter), nok)(code);\n    }\n    /**\n   * After a protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *                              ^\n   * ```\n   *\n   * @type {State}\n   */ function protocolAfter(code) {\n        effects.exit(\"literalAutolinkHttp\");\n        effects.exit(\"literalAutolink\");\n        return ok(code);\n    }\n}\n/**\n * `www` prefix.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeWwwPrefix(effects, ok, nok) {\n    let size = 0;\n    return wwwPrefixInside;\n    /**\n   * In www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *     ^^^^\n   * ```\n   *\n   * @type {State}\n   */ function wwwPrefixInside(code) {\n        if ((code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.uppercaseW || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lowercaseW) && size < 3) {\n            size++;\n            effects.consume(code);\n            return wwwPrefixInside;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot && size === 3) {\n            effects.consume(code);\n            return wwwPrefixAfter;\n        }\n        return nok(code);\n    }\n    /**\n   * After www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function wwwPrefixAfter(code) {\n        // If there is *anything*, we can link.\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ? nok(code) : ok(code);\n    }\n}\n/**\n * Domain.\n *\n * ```markdown\n * > | a https://example.org b\n *               ^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeDomain(effects, ok, nok) {\n    /** @type {boolean | undefined} */ let underscoreInLastSegment;\n    /** @type {boolean | undefined} */ let underscoreInLastLastSegment;\n    /** @type {boolean | undefined} */ let seen;\n    return domainInside;\n    /**\n   * In domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *             ^^^^^^^^^^^\n   * ```\n   *\n   * @type {State}\n   */ function domainInside(code) {\n        // Check whether this marker, which is a trailing punctuation\n        // marker, optionally followed by more trailing markers, and then\n        // followed by an end.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore) {\n            return effects.check(trail, domainAfter, domainAtPunctuation)(code);\n        }\n        // GH documents that only alphanumerics (other than `-`, `.`, and `_`) can\n        // occur, which sounds like ASCII only, but they also support `www.點看.com`,\n        // so that’s Unicode.\n        // Instead of some new production for Unicode alphanumerics, markdown\n        // already has that for Unicode punctuation and whitespace, so use those.\n        // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L12>.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code) || code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodePunctuation)(code)) {\n            return domainAfter(code);\n        }\n        seen = true;\n        effects.consume(code);\n        return domainInside;\n    }\n    /**\n   * In domain, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */ function domainAtPunctuation(code) {\n        // There is an underscore in the last segment of the domain\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore) {\n            underscoreInLastSegment = true;\n        } else {\n            underscoreInLastLastSegment = underscoreInLastSegment;\n            underscoreInLastSegment = undefined;\n        }\n        effects.consume(code);\n        return domainInside;\n    }\n    /**\n   * After domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^\n   * ```\n   *\n   * @type {State} */ function domainAfter(code) {\n        // Note: that’s GH says a dot is needed, but it’s not true:\n        // <https://github.com/github/cmark-gfm/issues/279>\n        if (underscoreInLastLastSegment || underscoreInLastSegment || !seen) {\n            return nok(code);\n        }\n        return ok(code);\n    }\n}\n/**\n * Path.\n *\n * ```markdown\n * > | a https://example.org/stuff b\n *                          ^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizePath(effects, ok) {\n    let sizeOpen = 0;\n    let sizeClose = 0;\n    return pathInside;\n    /**\n   * In path.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^^\n   * ```\n   *\n   * @type {State}\n   */ function pathInside(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis) {\n            sizeOpen++;\n            effects.consume(code);\n            return pathInside;\n        }\n        // To do: `markdown-rs` also needs this.\n        // If this is a paren, and there are less closings than openings,\n        // we don’t check for a trail.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis && sizeClose < sizeOpen) {\n            return pathAtPunctuation(code);\n        }\n        // Check whether this trailing punctuation marker is optionally\n        // followed by more trailing markers, and then followed\n        // by an end.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.quotationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.apostrophe || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.comma || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.questionMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde) {\n            return effects.check(trail, ok, pathAtPunctuation)(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)) {\n            return ok(code);\n        }\n        effects.consume(code);\n        return pathInside;\n    }\n    /**\n   * In path, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com/a\"b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */ function pathAtPunctuation(code) {\n        // Count closing parens.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis) {\n            sizeClose++;\n        }\n        effects.consume(code);\n        return pathInside;\n    }\n}\n/**\n * Trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the entire trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | https://example.com\").\n *                        ^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeTrail(effects, ok, nok) {\n    return trail;\n    /**\n   * In trail of domain or path.\n   *\n   * ```markdown\n   * > | https://example.com\").\n   *                        ^\n   * ```\n   *\n   * @type {State}\n   */ function trail(code) {\n        // Regular trailing punctuation.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.quotationMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.apostrophe || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightParenthesis || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.comma || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.questionMark || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde) {\n            effects.consume(code);\n            return trail;\n        }\n        // `&` followed by one or more alphabeticals and then a `;`, is\n        // as a whole considered as trailing punctuation.\n        // In all other cases, it is considered as continuation of the URL.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand) {\n            effects.consume(code);\n            return trailCharacterReferenceStart;\n        }\n        // Needed because we allow literals after `[`, as we fix:\n        // <https://github.com/github/cmark-gfm/issues/278>.\n        // Check that it is not followed by `(` or `[`.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n            effects.consume(code);\n            return trailBracketAfter;\n        }\n        if (// `<` is an end.\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan || // So is whitespace.\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)) {\n            return ok(code);\n        }\n        return nok(code);\n    }\n    /**\n   * In trail, after `]`.\n   *\n   * > 👉 **Note**: this deviates from `cmark-gfm` to fix a bug.\n   * > See end of <https://github.com/github/cmark-gfm/issues/278> for more.\n   *\n   * ```markdown\n   * > | https://example.com](\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */ function trailBracketAfter(code) {\n        // Whitespace or something that could start a resource or reference is the end.\n        // Switch back to trail otherwise.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code) || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.unicodeWhitespace)(code)) {\n            return ok(code);\n        }\n        return trail(code);\n    }\n    /**\n   * In character-reference like trail, after `&`.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */ function trailCharacterReferenceStart(code) {\n        // When non-alpha, it’s not a trail.\n        return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code) ? trailCharacterReferenceInside(code) : nok(code);\n    }\n    /**\n   * In character-reference like trail.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */ function trailCharacterReferenceInside(code) {\n        // Switch back to trail if this is well-formed.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.semicolon) {\n            effects.consume(code);\n            return trail;\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code)) {\n            effects.consume(code);\n            return trailCharacterReferenceInside;\n        }\n        // It’s not a trail.\n        return nok(code);\n    }\n}\n/**\n * Dot in email domain trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | <EMAIL>.\n *                        ^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeEmailDomainDotTrail(effects, ok, nok) {\n    return start;\n    /**\n   * Dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                    ^   ^\n   * ```\n   *\n   * @type {State}\n   */ function start(code) {\n        // Must be dot.\n        effects.consume(code);\n        return after;\n    }\n    /**\n   * After dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                     ^   ^\n   * ```\n   *\n   * @type {State}\n   */ function after(code) {\n        // Not a trail if alphanumeric.\n        return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code) ? nok(code) : ok(code);\n    }\n}\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L156>.\n *\n * @type {Previous}\n */ function previousWww(code) {\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftParenthesis || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEndingOrSpace)(code);\n}\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L214>.\n *\n * @type {Previous}\n */ function previousProtocol(code) {\n    return !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlpha)(code);\n}\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */ function previousEmail(code) {\n    // Do not allow a slash “inside” atext.\n    // The reference code is a bit weird, but that’s what it results in.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L307>.\n    // Other than slash, every preceding character is allowed.\n    return !(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.slash || gfmAtext(code));\n}\n/**\n * @param {Code} code\n * @returns {boolean}\n */ function gfmAtext(code) {\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dot || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.asciiAlphanumeric)(code);\n}\n/**\n * @param {Array<Event>} events\n * @returns {boolean}\n */ function previousUnbalanced(events) {\n    let index = events.length;\n    let result = false;\n    while(index--){\n        const token = events[index][1];\n        if ((token.type === \"labelLink\" || token.type === \"labelImage\") && !token._balanced) {\n            result = true;\n            break;\n        }\n        // If we’ve seen this token, and it was marked as not having any unbalanced\n        // bracket before it, we can exit.\n        if (token._gfmAutolinkLiteralWalkedInto) {\n            result = false;\n            break;\n        }\n    }\n    if (events.length > 0 && !result) {\n        // Mark the last token as “walked into” w/o finding\n        // anything.\n        events[events.length - 1][1]._gfmAutolinkLiteralWalkedInto = true;\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js\n");

/***/ })

};
;