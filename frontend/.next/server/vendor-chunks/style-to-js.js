"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/style-to-js";
exports.ids = ["vendor-chunks/style-to-js"];
exports.modules = {

/***/ "(ssr)/./node_modules/style-to-js/cjs/index.js":
/*!***********************************************!*\
  !*** ./node_modules/style-to-js/cjs/index.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nvar style_to_object_1 = __importDefault(__webpack_require__(/*! style-to-object */ \"(ssr)/./node_modules/style-to-object/cjs/index.js\"));\nvar utilities_1 = __webpack_require__(/*! ./utilities */ \"(ssr)/./node_modules/style-to-js/cjs/utilities.js\");\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */ function StyleToJS(style, options) {\n    var output = {};\n    if (!style || typeof style !== \"string\") {\n        return output;\n    }\n    (0, style_to_object_1.default)(style, function(property, value) {\n        // skip CSS comment\n        if (property && value) {\n            output[(0, utilities_1.camelCase)(property, options)] = value;\n        }\n    });\n    return output;\n}\nStyleToJS.default = StyleToJS;\nmodule.exports = StyleToJS; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/style-to-js/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/style-to-js/cjs/utilities.js":
/*!***************************************************!*\
  !*** ./node_modules/style-to-js/cjs/utilities.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.camelCase = void 0;\nvar CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nvar HYPHEN_REGEX = /-([a-z])/g;\nvar NO_HYPHEN_REGEX = /^[^-]+$/;\nvar VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nvar MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n/**\n * Checks whether to skip camelCase.\n */ var skipCamelCase = function(property) {\n    return !property || NO_HYPHEN_REGEX.test(property) || CUSTOM_PROPERTY_REGEX.test(property);\n};\n/**\n * Replacer that capitalizes first character.\n */ var capitalize = function(match, character) {\n    return character.toUpperCase();\n};\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */ var trimHyphen = function(match, prefix) {\n    return \"\".concat(prefix, \"-\");\n};\n/**\n * CamelCases a CSS property.\n */ var camelCase = function(property, options) {\n    if (options === void 0) {\n        options = {};\n    }\n    if (skipCamelCase(property)) {\n        return property;\n    }\n    property = property.toLowerCase();\n    if (options.reactCompat) {\n        // `-ms` vendor prefix should not be capitalized\n        property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n    } else {\n        // for non-React, remove first hyphen so vendor prefix is not capitalized\n        property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    return property.replace(HYPHEN_REGEX, capitalize);\n};\nexports.camelCase = camelCase; //# sourceMappingURL=utilities.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/style-to-js/cjs/utilities.js\n");

/***/ })

};
;