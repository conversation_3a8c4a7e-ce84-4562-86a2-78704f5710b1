"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/number/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@radix-ui/number/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ $ae6933e535247d3d$export$7d15b64cf5a3a4c4)\n/* harmony export */ });\nfunction $ae6933e535247d3d$export$7d15b64cf5a3a4c4(value, [min, max]) {\n    return Math.min(max, Math.max(min, value));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL251bWJlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsMENBQTBDQyxLQUFLLEVBQUUsQ0FBQ0MsS0FBS0MsSUFBSTtJQUNoRSxPQUFPQyxLQUFLRixHQUFHLENBQUNDLEtBQUtDLEtBQUtELEdBQUcsQ0FBQ0QsS0FBS0Q7QUFDdkM7QUFLNEQsQ0FDNUQsa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9udW1iZXIvZGlzdC9pbmRleC5tanM/MGE3NyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiAkYWU2OTMzZTUzNTI0N2QzZCRleHBvcnQkN2QxNWI2NGNmNWEzYTRjNCh2YWx1ZSwgW21pbiwgbWF4XSkge1xuICAgIHJldHVybiBNYXRoLm1pbihtYXgsIE1hdGgubWF4KG1pbiwgdmFsdWUpKTtcbn1cblxuXG5cblxuZXhwb3J0IHskYWU2OTMzZTUzNTI0N2QzZCRleHBvcnQkN2QxNWI2NGNmNWEzYTRjNCBhcyBjbGFtcH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiJGFlNjkzM2U1MzUyNDdkM2QkZXhwb3J0JDdkMTViNjRjZjVhM2E0YzQiLCJ2YWx1ZSIsIm1pbiIsIm1heCIsIk1hdGgiLCJjbGFtcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ $e42e1063c40fb3ef$export$b9ecd428b558ff10)\n/* harmony export */ });\nfunction $e42e1063c40fb3ef$export$b9ecd428b558ff10(originalEventHandler, ourEventHandler, { checkForDefaultPrevented: checkForDefaultPrevented = true } = {}) {\n    return function handleEvent(event) {\n        originalEventHandler === null || originalEventHandler === void 0 || originalEventHandler(event);\n        if (checkForDefaultPrevented === false || !event.defaultPrevented) return ourEventHandler === null || ourEventHandler === void 0 ? void 0 : ourEventHandler(event);\n    };\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsMENBQTBDQyxvQkFBb0IsRUFBRUMsZUFBZSxFQUFFLEVBQUVDLDBCQUEwQkEsMkJBQTJCLElBQUksRUFBRyxHQUFHLENBQUMsQ0FBQztJQUN6SixPQUFPLFNBQVNDLFlBQVlDLEtBQUs7UUFDN0JKLHlCQUF5QixRQUFRQSx5QkFBeUIsS0FBSyxLQUFLQSxxQkFBcUJJO1FBQ3pGLElBQUlGLDZCQUE2QixTQUFTLENBQUNFLE1BQU1DLGdCQUFnQixFQUFFLE9BQU9KLG9CQUFvQixRQUFRQSxvQkFBb0IsS0FBSyxJQUFJLEtBQUssSUFBSUEsZ0JBQWdCRztJQUNoSztBQUNKO0FBSzJFLENBQzNFLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXN5c3RlbS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzPzE4NjgiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gJGU0MmUxMDYzYzQwZmIzZWYkZXhwb3J0JGI5ZWNkNDI4YjU1OGZmMTAob3JpZ2luYWxFdmVudEhhbmRsZXIsIG91ckV2ZW50SGFuZGxlciwgeyBjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQ6IGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9IHRydWUgIH0gPSB7fSkge1xuICAgIHJldHVybiBmdW5jdGlvbiBoYW5kbGVFdmVudChldmVudCkge1xuICAgICAgICBvcmlnaW5hbEV2ZW50SGFuZGxlciA9PT0gbnVsbCB8fCBvcmlnaW5hbEV2ZW50SGFuZGxlciA9PT0gdm9pZCAwIHx8IG9yaWdpbmFsRXZlbnRIYW5kbGVyKGV2ZW50KTtcbiAgICAgICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHJldHVybiBvdXJFdmVudEhhbmRsZXIgPT09IG51bGwgfHwgb3VyRXZlbnRIYW5kbGVyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvdXJFdmVudEhhbmRsZXIoZXZlbnQpO1xuICAgIH07XG59XG5cblxuXG5cbmV4cG9ydCB7JGU0MmUxMDYzYzQwZmIzZWYkZXhwb3J0JGI5ZWNkNDI4YjU1OGZmMTAgYXMgY29tcG9zZUV2ZW50SGFuZGxlcnN9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIiRlNDJlMTA2M2M0MGZiM2VmJGV4cG9ydCRiOWVjZDQyOGI1NThmZjEwIiwib3JpZ2luYWxFdmVudEhhbmRsZXIiLCJvdXJFdmVudEhhbmRsZXIiLCJjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQiLCJoYW5kbGVFdmVudCIsImV2ZW50IiwiZGVmYXVsdFByZXZlbnRlZCIsImNvbXBvc2VFdmVudEhhbmRsZXJzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ $6ed0406888f73fc4$export$43e446d32b3d21af),\n/* harmony export */   useComposedRefs: () => (/* binding */ $6ed0406888f73fc4$export$c7b2cbe3552a0d05)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */ function $6ed0406888f73fc4$var$setRef(ref, value) {\n    if (typeof ref === \"function\") ref(value);\n    else if (ref !== null && ref !== undefined) ref.current = value;\n}\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */ function $6ed0406888f73fc4$export$43e446d32b3d21af(...refs) {\n    return (node)=>refs.forEach((ref)=>$6ed0406888f73fc4$var$setRef(ref, node));\n}\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */ function $6ed0406888f73fc4$export$c7b2cbe3552a0d05(...refs) {\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)($6ed0406888f73fc4$export$43e446d32b3d21af(...refs), refs);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ $c512c27ab02ef895$export$fd42f52fd3ae1109),\n/* harmony export */   createContextScope: () => (/* binding */ $c512c27ab02ef895$export$50c7b4e9d9f19c1)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction $c512c27ab02ef895$export$fd42f52fd3ae1109(rootComponentName, defaultContext) {\n    const Context = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultContext);\n    function Provider(props) {\n        const { children: children, ...context } = props; // Only re-memoize when prop values change\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>context, Object.values(context));\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Context.Provider, {\n            value: value\n        }, children);\n    }\n    function useContext(consumerName) {\n        const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n        if (context) return context;\n        if (defaultContext !== undefined) return defaultContext; // if a defaultContext wasn't specified, it's a required context.\n        throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    Provider.displayName = rootComponentName + \"Provider\";\n    return [\n        Provider,\n        useContext\n    ];\n}\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/ function $c512c27ab02ef895$export$50c7b4e9d9f19c1(scopeName, createContextScopeDeps = []) {\n    let defaultContexts = [];\n    /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/ function $c512c27ab02ef895$export$fd42f52fd3ae1109(rootComponentName, defaultContext) {\n        const BaseContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultContext);\n        const index = defaultContexts.length;\n        defaultContexts = [\n            ...defaultContexts,\n            defaultContext\n        ];\n        function Provider(props) {\n            const { scope: scope, children: children, ...context } = props;\n            const Context = (scope === null || scope === void 0 ? void 0 : scope[scopeName][index]) || BaseContext; // Only re-memoize when prop values change\n            // eslint-disable-next-line react-hooks/exhaustive-deps\n            const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>context, Object.values(context));\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Context.Provider, {\n                value: value\n            }, children);\n        }\n        function useContext(consumerName, scope) {\n            const Context = (scope === null || scope === void 0 ? void 0 : scope[scopeName][index]) || BaseContext;\n            const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n            if (context) return context;\n            if (defaultContext !== undefined) return defaultContext; // if a defaultContext wasn't specified, it's a required context.\n            throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n        }\n        Provider.displayName = rootComponentName + \"Provider\";\n        return [\n            Provider,\n            useContext\n        ];\n    }\n    /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/ const createScope = ()=>{\n        const scopeContexts = defaultContexts.map((defaultContext)=>{\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultContext);\n        });\n        return function useScope(scope) {\n            const contexts = (scope === null || scope === void 0 ? void 0 : scope[scopeName]) || scopeContexts;\n            return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n                    [`__scope${scopeName}`]: {\n                        ...scope,\n                        [scopeName]: contexts\n                    }\n                }), [\n                scope,\n                contexts\n            ]);\n        };\n    };\n    createScope.scopeName = scopeName;\n    return [\n        $c512c27ab02ef895$export$fd42f52fd3ae1109,\n        $c512c27ab02ef895$var$composeContextScopes(createScope, ...createContextScopeDeps)\n    ];\n}\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/ function $c512c27ab02ef895$var$composeContextScopes(...scopes) {\n    const baseScope = scopes[0];\n    if (scopes.length === 1) return baseScope;\n    const createScope1 = ()=>{\n        const scopeHooks = scopes.map((createScope)=>({\n                useScope: createScope(),\n                scopeName: createScope.scopeName\n            }));\n        return function useComposedScopes(overrideScopes) {\n            const nextScopes1 = scopeHooks.reduce((nextScopes, { useScope: useScope, scopeName: scopeName })=>{\n                // We are calling a hook inside a callback which React warns against to avoid inconsistent\n                // renders, however, scoping doesn't have render side effects so we ignore the rule.\n                // eslint-disable-next-line react-hooks/rules-of-hooks\n                const scopeProps = useScope(overrideScopes);\n                const currentScope = scopeProps[`__scope${scopeName}`];\n                return {\n                    ...nextScopes,\n                    ...currentScope\n                };\n            }, {});\n            return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n                    [`__scope${baseScope.scopeName}`]: nextScopes1\n                }), [\n                nextScopes1\n            ]);\n        };\n    };\n    createScope1.scopeName = baseScope.scopeName;\n    return createScope1;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ $f631663db3294ace$export$c760c09fdd558351),\n/* harmony export */   Provider: () => (/* binding */ $f631663db3294ace$export$2881499e37b75b9a),\n/* harmony export */   useDirection: () => (/* binding */ $f631663db3294ace$export$b39126d51d94e6f3)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nconst $f631663db3294ace$var$DirectionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/ const $f631663db3294ace$export$c760c09fdd558351 = (props)=>{\n    const { dir: dir, children: children } = props;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)($f631663db3294ace$var$DirectionContext.Provider, {\n        value: dir\n    }, children);\n};\n/* -----------------------------------------------------------------------------------------------*/ function $f631663db3294ace$export$b39126d51d94e6f3(localDir) {\n    const globalDir = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)($f631663db3294ace$var$DirectionContext);\n    return localDir || globalDir || \"ltr\";\n}\nconst $f631663db3294ace$export$2881499e37b75b9a = $f631663db3294ace$export$c760c09fdd558351;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9JO0FBR3BJLE1BQU1NLHlDQUF5QyxXQUFXLEdBQUdMLG9EQUFvQkEsQ0FBQ007QUFDbEY7O2tHQUVrRyxHQUFHLE1BQU1DLDRDQUE0QyxDQUFDQztJQUNwSixNQUFNLEVBQUVDLEtBQUtBLEdBQUcsRUFBR0MsVUFBVUEsUUFBUSxFQUFHLEdBQUdGO0lBQzNDLE9BQU8sV0FBVyxHQUFHTixvREFBb0JBLENBQUNHLHVDQUF1Q00sUUFBUSxFQUFFO1FBQ3ZGQyxPQUFPSDtJQUNYLEdBQUdDO0FBQ1A7QUFDQSxrR0FBa0csR0FBRyxTQUFTRywwQ0FBMENDLFFBQVE7SUFDNUosTUFBTUMsWUFBWVgsaURBQWlCQSxDQUFDQztJQUNwQyxPQUFPUyxZQUFZQyxhQUFhO0FBQ3BDO0FBQ0EsTUFBTUMsNENBQTRDVDtBQUt3SSxDQUMxTCxrQ0FBa0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zeXN0ZW0tZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcz83NTgzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y3JlYXRlQ29udGV4dCBhcyAkN0dqY2QkY3JlYXRlQ29udGV4dCwgY3JlYXRlRWxlbWVudCBhcyAkN0dqY2QkY3JlYXRlRWxlbWVudCwgdXNlQ29udGV4dCBhcyAkN0dqY2QkdXNlQ29udGV4dH0gZnJvbSBcInJlYWN0XCI7XG5cblxuY29uc3QgJGY2MzE2NjNkYjMyOTRhY2UkdmFyJERpcmVjdGlvbkNvbnRleHQgPSAvKiNfX1BVUkVfXyovICQ3R2pjZCRjcmVhdGVDb250ZXh0KHVuZGVmaW5lZCk7XG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBEaXJlY3Rpb25cbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi8gY29uc3QgJGY2MzE2NjNkYjMyOTRhY2UkZXhwb3J0JGM3NjBjMDlmZGQ1NTgzNTEgPSAocHJvcHMpPT57XG4gICAgY29uc3QgeyBkaXI6IGRpciAsIGNoaWxkcmVuOiBjaGlsZHJlbiAgfSA9IHByb3BzO1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovICQ3R2pjZCRjcmVhdGVFbGVtZW50KCRmNjMxNjYzZGIzMjk0YWNlJHZhciREaXJlY3Rpb25Db250ZXh0LlByb3ZpZGVyLCB7XG4gICAgICAgIHZhbHVlOiBkaXJcbiAgICB9LCBjaGlsZHJlbik7XG59O1xuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qLyBmdW5jdGlvbiAkZjYzMTY2M2RiMzI5NGFjZSRleHBvcnQkYjM5MTI2ZDUxZDk0ZTZmMyhsb2NhbERpcikge1xuICAgIGNvbnN0IGdsb2JhbERpciA9ICQ3R2pjZCR1c2VDb250ZXh0KCRmNjMxNjYzZGIzMjk0YWNlJHZhciREaXJlY3Rpb25Db250ZXh0KTtcbiAgICByZXR1cm4gbG9jYWxEaXIgfHwgZ2xvYmFsRGlyIHx8ICdsdHInO1xufVxuY29uc3QgJGY2MzE2NjNkYjMyOTRhY2UkZXhwb3J0JDI4ODE0OTllMzdiNzViOWEgPSAkZjYzMTY2M2RiMzI5NGFjZSRleHBvcnQkYzc2MGMwOWZkZDU1ODM1MTtcblxuXG5cblxuZXhwb3J0IHskZjYzMTY2M2RiMzI5NGFjZSRleHBvcnQkYjM5MTI2ZDUxZDk0ZTZmMyBhcyB1c2VEaXJlY3Rpb24sICRmNjMxNjYzZGIzMjk0YWNlJGV4cG9ydCQyODgxNDk5ZTM3Yjc1YjlhIGFzIFByb3ZpZGVyLCAkZjYzMTY2M2RiMzI5NGFjZSRleHBvcnQkYzc2MGMwOWZkZDU1ODM1MSBhcyBEaXJlY3Rpb25Qcm92aWRlcn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsIiQ3R2pjZCRjcmVhdGVDb250ZXh0IiwiY3JlYXRlRWxlbWVudCIsIiQ3R2pjZCRjcmVhdGVFbGVtZW50IiwidXNlQ29udGV4dCIsIiQ3R2pjZCR1c2VDb250ZXh0IiwiJGY2MzE2NjNkYjMyOTRhY2UkdmFyJERpcmVjdGlvbkNvbnRleHQiLCJ1bmRlZmluZWQiLCIkZjYzMTY2M2RiMzI5NGFjZSRleHBvcnQkYzc2MGMwOWZkZDU1ODM1MSIsInByb3BzIiwiZGlyIiwiY2hpbGRyZW4iLCJQcm92aWRlciIsInZhbHVlIiwiJGY2MzE2NjNkYjMyOTRhY2UkZXhwb3J0JGIzOTEyNmQ1MWQ5NGU2ZjMiLCJsb2NhbERpciIsImdsb2JhbERpciIsIiRmNjMxNjYzZGIzMjk0YWNlJGV4cG9ydCQyODgxNDk5ZTM3Yjc1YjlhIiwidXNlRGlyZWN0aW9uIiwiRGlyZWN0aW9uUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ $921a889cee6df7e8$export$99c2b779aa4e8b8b)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n\n\n\n\nfunction $fe963b355347cc68$export$3e6543de14f8614f(initialState, machine) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState !== null && nextState !== void 0 ? nextState : state;\n    }, initialState);\n}\nconst $921a889cee6df7e8$export$99c2b779aa4e8b8b = (props)=>{\n    const { present: present, children: children } = props;\n    const presence = $921a889cee6df7e8$var$usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(presence.ref, child.ref);\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {\n        ref: ref\n    }) : null;\n};\n$921a889cee6df7e8$export$99c2b779aa4e8b8b.displayName = \"Presence\";\n/* -------------------------------------------------------------------------------------------------\n * usePresence\n * -----------------------------------------------------------------------------------------------*/ function $921a889cee6df7e8$var$usePresence(present) {\n    const [node1, setNode] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const stylesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const prevPresentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(present);\n    const prevAnimationNameRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = $fe963b355347cc68$export$3e6543de14f8614f(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const currentAnimationName = $921a889cee6df7e8$var$getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = $921a889cee6df7e8$var$getAnimationName(styles);\n            if (present) send(\"MOUNT\");\n            else if (currentAnimationName === \"none\" || (styles === null || styles === void 0 ? void 0 : styles.display) === \"none\") // so we unmount instantly\n            send(\"UNMOUNT\");\n            else {\n                /**\n         * When `present` changes to `false`, we check changes to animation-name to\n         * determine whether an animation has started. We chose this approach (reading\n         * computed styles) because there is no `animationrun` event and `animationstart`\n         * fires after `animation-delay` has expired which would be too late.\n         */ const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) send(\"ANIMATION_OUT\");\n                else send(\"UNMOUNT\");\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>{\n        if (node1) {\n            /**\n       * Triggering an ANIMATION_OUT during an ANIMATION_IN will fire an `animationcancel`\n       * event for ANIMATION_IN after we have entered `unmountSuspended` state. So, we\n       * make sure we only trigger ANIMATION_END for the currently active animation.\n       */ const handleAnimationEnd = (event)=>{\n                const currentAnimationName = $921a889cee6df7e8$var$getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node1 && isCurrentAnimation) // a frame after the animation ends, creating a flash of visible content.\n                // By manually flushing we ensure they sync within a frame, removing the flash.\n                (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(()=>send(\"ANIMATION_END\"));\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node1) prevAnimationNameRef.current = $921a889cee6df7e8$var$getAnimationName(stylesRef.current);\n            };\n            node1.addEventListener(\"animationstart\", handleAnimationStart);\n            node1.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node1.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                node1.removeEventListener(\"animationstart\", handleAnimationStart);\n                node1.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node1.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else // We avoid doing so during cleanup as the node may change but still exist.\n        send(\"ANIMATION_END\");\n    }, [\n        node1,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((node)=>{\n            if (node) stylesRef.current = getComputedStyle(node);\n            setNode(node);\n        }, [])\n    };\n}\n/* -----------------------------------------------------------------------------------------------*/ function $921a889cee6df7e8$var$getAnimationName(styles) {\n    return (styles === null || styles === void 0 ? void 0 : styles.animationName) || \"none\";\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXByZXNlbmNlL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWdQO0FBQ3hMO0FBQytCO0FBQ0s7QUFPNUYsU0FBU29CLDBDQUEwQ0MsWUFBWSxFQUFFQyxPQUFPO0lBQ3BFLE9BQU9ULGlEQUFpQkEsQ0FBQyxDQUFDVSxPQUFPQztRQUM3QixNQUFNQyxZQUFZSCxPQUFPLENBQUNDLE1BQU0sQ0FBQ0MsTUFBTTtRQUN2QyxPQUFPQyxjQUFjLFFBQVFBLGNBQWMsS0FBSyxJQUFJQSxZQUFZRjtJQUNwRSxHQUFHRjtBQUNQO0FBR0EsTUFBTUssNENBQTRDLENBQUNDO0lBQy9DLE1BQU0sRUFBRUMsU0FBU0EsT0FBTyxFQUFHQyxVQUFVQSxRQUFRLEVBQUcsR0FBR0Y7SUFDbkQsTUFBTUcsV0FBV0Msa0NBQWtDSDtJQUNuRCxNQUFNSSxRQUFRLE9BQU9ILGFBQWEsYUFBYUEsU0FBUztRQUNwREQsU0FBU0UsU0FBU0csU0FBUztJQUMvQixLQUFLaEMsMkNBQWVBLENBQUNpQyxJQUFJLENBQUNMO0lBQzFCLE1BQU1NLE1BQU1sQiw2RUFBc0JBLENBQUNhLFNBQVNLLEdBQUcsRUFBRUgsTUFBTUcsR0FBRztJQUMxRCxNQUFNQyxhQUFhLE9BQU9QLGFBQWE7SUFDdkMsT0FBT08sY0FBY04sU0FBU0csU0FBUyxHQUFHLFdBQVcsR0FBRzlCLG1EQUFtQkEsQ0FBQzZCLE9BQU87UUFDL0VHLEtBQUtBO0lBQ1QsS0FBSztBQUNUO0FBQ0FULDBDQUEwQ1csV0FBVyxHQUFHO0FBQ3hEOztrR0FFa0csR0FBRyxTQUFTTixrQ0FBa0NILE9BQU87SUFDbkosTUFBTSxDQUFDVSxPQUFPQyxRQUFRLEdBQUdsQywrQ0FBZUE7SUFDeEMsTUFBTW1DLFlBQVlqQyw2Q0FBYUEsQ0FBQyxDQUFDO0lBQ2pDLE1BQU1rQyxpQkFBaUJsQyw2Q0FBYUEsQ0FBQ3FCO0lBQ3JDLE1BQU1jLHVCQUF1Qm5DLDZDQUFhQSxDQUFDO0lBQzNDLE1BQU1jLGVBQWVPLFVBQVUsWUFBWTtJQUMzQyxNQUFNLENBQUNMLE9BQU9vQixLQUFLLEdBQUd2QiwwQ0FBMENDLGNBQWM7UUFDMUV1QixTQUFTO1lBQ0xDLFNBQVM7WUFDVEMsZUFBZTtRQUNuQjtRQUNBQyxrQkFBa0I7WUFDZEMsT0FBTztZQUNQQyxlQUFlO1FBQ25CO1FBQ0FDLFdBQVc7WUFDUEYsT0FBTztRQUNYO0lBQ0o7SUFDQXZDLGdEQUFnQkEsQ0FBQztRQUNiLE1BQU0wQyx1QkFBdUJDLHVDQUF1Q1osVUFBVWEsT0FBTztRQUNyRlgscUJBQXFCVyxPQUFPLEdBQUc5QixVQUFVLFlBQVk0Qix1QkFBdUI7SUFDaEYsR0FBRztRQUNDNUI7S0FDSDtJQUNESixrRkFBc0JBLENBQUM7UUFDbkIsTUFBTW1DLFNBQVNkLFVBQVVhLE9BQU87UUFDaEMsTUFBTUUsYUFBYWQsZUFBZVksT0FBTztRQUN6QyxNQUFNRyxvQkFBb0JELGVBQWUzQjtRQUN6QyxJQUFJNEIsbUJBQW1CO1lBQ25CLE1BQU1DLG9CQUFvQmYscUJBQXFCVyxPQUFPO1lBQ3RELE1BQU1GLHVCQUF1QkMsdUNBQXVDRTtZQUNwRSxJQUFJMUIsU0FBU2UsS0FBSztpQkFDYixJQUFJUSx5QkFBeUIsVUFBVSxDQUFDRyxXQUFXLFFBQVFBLFdBQVcsS0FBSyxJQUFJLEtBQUssSUFBSUEsT0FBT0ksT0FBTyxNQUFNLFFBQ2pILDBCQUEwQjtZQUMxQmYsS0FBSztpQkFDQTtnQkFDRDs7Ozs7U0FLUCxHQUFHLE1BQU1nQixjQUFjRixzQkFBc0JOO2dCQUN0QyxJQUFJSSxjQUFjSSxhQUFhaEIsS0FBSztxQkFDL0JBLEtBQUs7WUFDZDtZQUNBRixlQUFlWSxPQUFPLEdBQUd6QjtRQUM3QjtJQUNKLEdBQUc7UUFDQ0E7UUFDQWU7S0FDSDtJQUNEeEIsa0ZBQXNCQSxDQUFDO1FBQ25CLElBQUltQixPQUFPO1lBQ1A7Ozs7T0FJTCxHQUFHLE1BQU1zQixxQkFBcUIsQ0FBQ3BDO2dCQUN0QixNQUFNMkIsdUJBQXVCQyx1Q0FBdUNaLFVBQVVhLE9BQU87Z0JBQ3JGLE1BQU1RLHFCQUFxQlYscUJBQXFCVyxRQUFRLENBQUN0QyxNQUFNdUMsYUFBYTtnQkFDNUUsSUFBSXZDLE1BQU13QyxNQUFNLEtBQUsxQixTQUFTdUIsb0JBQzlCLHlFQUF5RTtnQkFDekUsK0VBQStFO2dCQUMvRTlDLG9EQUFnQkEsQ0FBQyxJQUFJNEIsS0FBSztZQUU5QjtZQUNBLE1BQU1zQix1QkFBdUIsQ0FBQ3pDO2dCQUMxQixJQUFJQSxNQUFNd0MsTUFBTSxLQUFLMUIsT0FDckJJLHFCQUFxQlcsT0FBTyxHQUFHRCx1Q0FBdUNaLFVBQVVhLE9BQU87WUFDM0Y7WUFDQWYsTUFBTTRCLGdCQUFnQixDQUFDLGtCQUFrQkQ7WUFDekMzQixNQUFNNEIsZ0JBQWdCLENBQUMsbUJBQW1CTjtZQUMxQ3RCLE1BQU00QixnQkFBZ0IsQ0FBQyxnQkFBZ0JOO1lBQ3ZDLE9BQU87Z0JBQ0h0QixNQUFNNkIsbUJBQW1CLENBQUMsa0JBQWtCRjtnQkFDNUMzQixNQUFNNkIsbUJBQW1CLENBQUMsbUJBQW1CUDtnQkFDN0N0QixNQUFNNkIsbUJBQW1CLENBQUMsZ0JBQWdCUDtZQUM5QztRQUNKLE9BQ0EsMkVBQTJFO1FBQzNFakIsS0FBSztJQUNULEdBQUc7UUFDQ0w7UUFDQUs7S0FDSDtJQUNELE9BQU87UUFDSFYsV0FBVztZQUNQO1lBQ0E7U0FDSCxDQUFDNkIsUUFBUSxDQUFDdkM7UUFDWFksS0FBS3hCLGtEQUFrQkEsQ0FBQyxDQUFDeUQ7WUFDckIsSUFBSUEsTUFBTTVCLFVBQVVhLE9BQU8sR0FBR2dCLGlCQUFpQkQ7WUFDL0M3QixRQUFRNkI7UUFDWixHQUFHLEVBQUU7SUFDVDtBQUNKO0FBQ0Esa0dBQWtHLEdBQUcsU0FBU2hCLHVDQUF1Q0UsTUFBTTtJQUN2SixPQUFPLENBQUNBLFdBQVcsUUFBUUEsV0FBVyxLQUFLLElBQUksS0FBSyxJQUFJQSxPQUFPUyxhQUFhLEtBQUs7QUFDckY7QUFLK0QsQ0FDL0Qsa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1wcmVzZW5jZS9kaXN0L2luZGV4Lm1qcz85YzUxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Q2hpbGRyZW4gYXMgJGlxcTNyJENoaWxkcmVuLCBjbG9uZUVsZW1lbnQgYXMgJGlxcTNyJGNsb25lRWxlbWVudCwgdXNlU3RhdGUgYXMgJGlxcTNyJHVzZVN0YXRlLCB1c2VSZWYgYXMgJGlxcTNyJHVzZVJlZiwgdXNlRWZmZWN0IGFzICRpcXEzciR1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIGFzICRpcXEzciR1c2VDYWxsYmFjaywgdXNlUmVkdWNlciBhcyAkaXFxM3IkdXNlUmVkdWNlcn0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQge2ZsdXNoU3luYyBhcyAkaXFxM3IkZmx1c2hTeW5jfSBmcm9tIFwicmVhY3QtZG9tXCI7XG5pbXBvcnQge3VzZUNvbXBvc2VkUmVmcyBhcyAkaXFxM3IkdXNlQ29tcG9zZWRSZWZzfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmc1wiO1xuaW1wb3J0IHt1c2VMYXlvdXRFZmZlY3QgYXMgJGlxcTNyJHVzZUxheW91dEVmZmVjdH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdFwiO1xuXG5cblxuXG5cblxuZnVuY3Rpb24gJGZlOTYzYjM1NTM0N2NjNjgkZXhwb3J0JDNlNjU0M2RlMTRmODYxNGYoaW5pdGlhbFN0YXRlLCBtYWNoaW5lKSB7XG4gICAgcmV0dXJuICRpcXEzciR1c2VSZWR1Y2VyKChzdGF0ZSwgZXZlbnQpPT57XG4gICAgICAgIGNvbnN0IG5leHRTdGF0ZSA9IG1hY2hpbmVbc3RhdGVdW2V2ZW50XTtcbiAgICAgICAgcmV0dXJuIG5leHRTdGF0ZSAhPT0gbnVsbCAmJiBuZXh0U3RhdGUgIT09IHZvaWQgMCA/IG5leHRTdGF0ZSA6IHN0YXRlO1xuICAgIH0sIGluaXRpYWxTdGF0ZSk7XG59XG5cblxuY29uc3QgJDkyMWE4ODljZWU2ZGY3ZTgkZXhwb3J0JDk5YzJiNzc5YWE0ZThiOGIgPSAocHJvcHMpPT57XG4gICAgY29uc3QgeyBwcmVzZW50OiBwcmVzZW50ICwgY2hpbGRyZW46IGNoaWxkcmVuICB9ID0gcHJvcHM7XG4gICAgY29uc3QgcHJlc2VuY2UgPSAkOTIxYTg4OWNlZTZkZjdlOCR2YXIkdXNlUHJlc2VuY2UocHJlc2VudCk7XG4gICAgY29uc3QgY2hpbGQgPSB0eXBlb2YgY2hpbGRyZW4gPT09ICdmdW5jdGlvbicgPyBjaGlsZHJlbih7XG4gICAgICAgIHByZXNlbnQ6IHByZXNlbmNlLmlzUHJlc2VudFxuICAgIH0pIDogJGlxcTNyJENoaWxkcmVuLm9ubHkoY2hpbGRyZW4pO1xuICAgIGNvbnN0IHJlZiA9ICRpcXEzciR1c2VDb21wb3NlZFJlZnMocHJlc2VuY2UucmVmLCBjaGlsZC5yZWYpO1xuICAgIGNvbnN0IGZvcmNlTW91bnQgPSB0eXBlb2YgY2hpbGRyZW4gPT09ICdmdW5jdGlvbic7XG4gICAgcmV0dXJuIGZvcmNlTW91bnQgfHwgcHJlc2VuY2UuaXNQcmVzZW50ID8gLyojX19QVVJFX18qLyAkaXFxM3IkY2xvbmVFbGVtZW50KGNoaWxkLCB7XG4gICAgICAgIHJlZjogcmVmXG4gICAgfSkgOiBudWxsO1xufTtcbiQ5MjFhODg5Y2VlNmRmN2U4JGV4cG9ydCQ5OWMyYjc3OWFhNGU4YjhiLmRpc3BsYXlOYW1lID0gJ1ByZXNlbmNlJztcbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIHVzZVByZXNlbmNlXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovIGZ1bmN0aW9uICQ5MjFhODg5Y2VlNmRmN2U4JHZhciR1c2VQcmVzZW5jZShwcmVzZW50KSB7XG4gICAgY29uc3QgW25vZGUxLCBzZXROb2RlXSA9ICRpcXEzciR1c2VTdGF0ZSgpO1xuICAgIGNvbnN0IHN0eWxlc1JlZiA9ICRpcXEzciR1c2VSZWYoe30pO1xuICAgIGNvbnN0IHByZXZQcmVzZW50UmVmID0gJGlxcTNyJHVzZVJlZihwcmVzZW50KTtcbiAgICBjb25zdCBwcmV2QW5pbWF0aW9uTmFtZVJlZiA9ICRpcXEzciR1c2VSZWYoJ25vbmUnKTtcbiAgICBjb25zdCBpbml0aWFsU3RhdGUgPSBwcmVzZW50ID8gJ21vdW50ZWQnIDogJ3VubW91bnRlZCc7XG4gICAgY29uc3QgW3N0YXRlLCBzZW5kXSA9ICRmZTk2M2IzNTUzNDdjYzY4JGV4cG9ydCQzZTY1NDNkZTE0Zjg2MTRmKGluaXRpYWxTdGF0ZSwge1xuICAgICAgICBtb3VudGVkOiB7XG4gICAgICAgICAgICBVTk1PVU5UOiAndW5tb3VudGVkJyxcbiAgICAgICAgICAgIEFOSU1BVElPTl9PVVQ6ICd1bm1vdW50U3VzcGVuZGVkJ1xuICAgICAgICB9LFxuICAgICAgICB1bm1vdW50U3VzcGVuZGVkOiB7XG4gICAgICAgICAgICBNT1VOVDogJ21vdW50ZWQnLFxuICAgICAgICAgICAgQU5JTUFUSU9OX0VORDogJ3VubW91bnRlZCdcbiAgICAgICAgfSxcbiAgICAgICAgdW5tb3VudGVkOiB7XG4gICAgICAgICAgICBNT1VOVDogJ21vdW50ZWQnXG4gICAgICAgIH1cbiAgICB9KTtcbiAgICAkaXFxM3IkdXNlRWZmZWN0KCgpPT57XG4gICAgICAgIGNvbnN0IGN1cnJlbnRBbmltYXRpb25OYW1lID0gJDkyMWE4ODljZWU2ZGY3ZTgkdmFyJGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzUmVmLmN1cnJlbnQpO1xuICAgICAgICBwcmV2QW5pbWF0aW9uTmFtZVJlZi5jdXJyZW50ID0gc3RhdGUgPT09ICdtb3VudGVkJyA/IGN1cnJlbnRBbmltYXRpb25OYW1lIDogJ25vbmUnO1xuICAgIH0sIFtcbiAgICAgICAgc3RhdGVcbiAgICBdKTtcbiAgICAkaXFxM3IkdXNlTGF5b3V0RWZmZWN0KCgpPT57XG4gICAgICAgIGNvbnN0IHN0eWxlcyA9IHN0eWxlc1JlZi5jdXJyZW50O1xuICAgICAgICBjb25zdCB3YXNQcmVzZW50ID0gcHJldlByZXNlbnRSZWYuY3VycmVudDtcbiAgICAgICAgY29uc3QgaGFzUHJlc2VudENoYW5nZWQgPSB3YXNQcmVzZW50ICE9PSBwcmVzZW50O1xuICAgICAgICBpZiAoaGFzUHJlc2VudENoYW5nZWQpIHtcbiAgICAgICAgICAgIGNvbnN0IHByZXZBbmltYXRpb25OYW1lID0gcHJldkFuaW1hdGlvbk5hbWVSZWYuY3VycmVudDtcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRBbmltYXRpb25OYW1lID0gJDkyMWE4ODljZWU2ZGY3ZTgkdmFyJGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzKTtcbiAgICAgICAgICAgIGlmIChwcmVzZW50KSBzZW5kKCdNT1VOVCcpO1xuICAgICAgICAgICAgZWxzZSBpZiAoY3VycmVudEFuaW1hdGlvbk5hbWUgPT09ICdub25lJyB8fCAoc3R5bGVzID09PSBudWxsIHx8IHN0eWxlcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogc3R5bGVzLmRpc3BsYXkpID09PSAnbm9uZScpIC8vIElmIHRoZXJlIGlzIG5vIGV4aXQgYW5pbWF0aW9uIG9yIHRoZSBlbGVtZW50IGlzIGhpZGRlbiwgYW5pbWF0aW9ucyB3b24ndCBydW5cbiAgICAgICAgICAgIC8vIHNvIHdlIHVubW91bnQgaW5zdGFudGx5XG4gICAgICAgICAgICBzZW5kKCdVTk1PVU5UJyk7XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAvKipcbiAgICAgICAgICogV2hlbiBgcHJlc2VudGAgY2hhbmdlcyB0byBgZmFsc2VgLCB3ZSBjaGVjayBjaGFuZ2VzIHRvIGFuaW1hdGlvbi1uYW1lIHRvXG4gICAgICAgICAqIGRldGVybWluZSB3aGV0aGVyIGFuIGFuaW1hdGlvbiBoYXMgc3RhcnRlZC4gV2UgY2hvc2UgdGhpcyBhcHByb2FjaCAocmVhZGluZ1xuICAgICAgICAgKiBjb21wdXRlZCBzdHlsZXMpIGJlY2F1c2UgdGhlcmUgaXMgbm8gYGFuaW1hdGlvbnJ1bmAgZXZlbnQgYW5kIGBhbmltYXRpb25zdGFydGBcbiAgICAgICAgICogZmlyZXMgYWZ0ZXIgYGFuaW1hdGlvbi1kZWxheWAgaGFzIGV4cGlyZWQgd2hpY2ggd291bGQgYmUgdG9vIGxhdGUuXG4gICAgICAgICAqLyBjb25zdCBpc0FuaW1hdGluZyA9IHByZXZBbmltYXRpb25OYW1lICE9PSBjdXJyZW50QW5pbWF0aW9uTmFtZTtcbiAgICAgICAgICAgICAgICBpZiAod2FzUHJlc2VudCAmJiBpc0FuaW1hdGluZykgc2VuZCgnQU5JTUFUSU9OX09VVCcpO1xuICAgICAgICAgICAgICAgIGVsc2Ugc2VuZCgnVU5NT1VOVCcpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcHJldlByZXNlbnRSZWYuY3VycmVudCA9IHByZXNlbnQ7XG4gICAgICAgIH1cbiAgICB9LCBbXG4gICAgICAgIHByZXNlbnQsXG4gICAgICAgIHNlbmRcbiAgICBdKTtcbiAgICAkaXFxM3IkdXNlTGF5b3V0RWZmZWN0KCgpPT57XG4gICAgICAgIGlmIChub2RlMSkge1xuICAgICAgICAgICAgLyoqXG4gICAgICAgKiBUcmlnZ2VyaW5nIGFuIEFOSU1BVElPTl9PVVQgZHVyaW5nIGFuIEFOSU1BVElPTl9JTiB3aWxsIGZpcmUgYW4gYGFuaW1hdGlvbmNhbmNlbGBcbiAgICAgICAqIGV2ZW50IGZvciBBTklNQVRJT05fSU4gYWZ0ZXIgd2UgaGF2ZSBlbnRlcmVkIGB1bm1vdW50U3VzcGVuZGVkYCBzdGF0ZS4gU28sIHdlXG4gICAgICAgKiBtYWtlIHN1cmUgd2Ugb25seSB0cmlnZ2VyIEFOSU1BVElPTl9FTkQgZm9yIHRoZSBjdXJyZW50bHkgYWN0aXZlIGFuaW1hdGlvbi5cbiAgICAgICAqLyBjb25zdCBoYW5kbGVBbmltYXRpb25FbmQgPSAoZXZlbnQpPT57XG4gICAgICAgICAgICAgICAgY29uc3QgY3VycmVudEFuaW1hdGlvbk5hbWUgPSAkOTIxYTg4OWNlZTZkZjdlOCR2YXIkZ2V0QW5pbWF0aW9uTmFtZShzdHlsZXNSZWYuY3VycmVudCk7XG4gICAgICAgICAgICAgICAgY29uc3QgaXNDdXJyZW50QW5pbWF0aW9uID0gY3VycmVudEFuaW1hdGlvbk5hbWUuaW5jbHVkZXMoZXZlbnQuYW5pbWF0aW9uTmFtZSk7XG4gICAgICAgICAgICAgICAgaWYgKGV2ZW50LnRhcmdldCA9PT0gbm9kZTEgJiYgaXNDdXJyZW50QW5pbWF0aW9uKSAvLyBXaXRoIFJlYWN0IDE4IGNvbmN1cnJlbmN5IHRoaXMgdXBkYXRlIGlzIGFwcGxpZWRcbiAgICAgICAgICAgICAgICAvLyBhIGZyYW1lIGFmdGVyIHRoZSBhbmltYXRpb24gZW5kcywgY3JlYXRpbmcgYSBmbGFzaCBvZiB2aXNpYmxlIGNvbnRlbnQuXG4gICAgICAgICAgICAgICAgLy8gQnkgbWFudWFsbHkgZmx1c2hpbmcgd2UgZW5zdXJlIHRoZXkgc3luYyB3aXRoaW4gYSBmcmFtZSwgcmVtb3ZpbmcgdGhlIGZsYXNoLlxuICAgICAgICAgICAgICAgICRpcXEzciRmbHVzaFN5bmMoKCk9PnNlbmQoJ0FOSU1BVElPTl9FTkQnKVxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgY29uc3QgaGFuZGxlQW5pbWF0aW9uU3RhcnQgPSAoZXZlbnQpPT57XG4gICAgICAgICAgICAgICAgaWYgKGV2ZW50LnRhcmdldCA9PT0gbm9kZTEpIC8vIGlmIGFuaW1hdGlvbiBvY2N1cnJlZCwgc3RvcmUgaXRzIG5hbWUgYXMgdGhlIHByZXZpb3VzIGFuaW1hdGlvbi5cbiAgICAgICAgICAgICAgICBwcmV2QW5pbWF0aW9uTmFtZVJlZi5jdXJyZW50ID0gJDkyMWE4ODljZWU2ZGY3ZTgkdmFyJGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzUmVmLmN1cnJlbnQpO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIG5vZGUxLmFkZEV2ZW50TGlzdGVuZXIoJ2FuaW1hdGlvbnN0YXJ0JywgaGFuZGxlQW5pbWF0aW9uU3RhcnQpO1xuICAgICAgICAgICAgbm9kZTEuYWRkRXZlbnRMaXN0ZW5lcignYW5pbWF0aW9uY2FuY2VsJywgaGFuZGxlQW5pbWF0aW9uRW5kKTtcbiAgICAgICAgICAgIG5vZGUxLmFkZEV2ZW50TGlzdGVuZXIoJ2FuaW1hdGlvbmVuZCcsIGhhbmRsZUFuaW1hdGlvbkVuZCk7XG4gICAgICAgICAgICByZXR1cm4gKCk9PntcbiAgICAgICAgICAgICAgICBub2RlMS5yZW1vdmVFdmVudExpc3RlbmVyKCdhbmltYXRpb25zdGFydCcsIGhhbmRsZUFuaW1hdGlvblN0YXJ0KTtcbiAgICAgICAgICAgICAgICBub2RlMS5yZW1vdmVFdmVudExpc3RlbmVyKCdhbmltYXRpb25jYW5jZWwnLCBoYW5kbGVBbmltYXRpb25FbmQpO1xuICAgICAgICAgICAgICAgIG5vZGUxLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2FuaW1hdGlvbmVuZCcsIGhhbmRsZUFuaW1hdGlvbkVuZCk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9IGVsc2UgLy8gVHJhbnNpdGlvbiB0byB0aGUgdW5tb3VudGVkIHN0YXRlIGlmIHRoZSBub2RlIGlzIHJlbW92ZWQgcHJlbWF0dXJlbHkuXG4gICAgICAgIC8vIFdlIGF2b2lkIGRvaW5nIHNvIGR1cmluZyBjbGVhbnVwIGFzIHRoZSBub2RlIG1heSBjaGFuZ2UgYnV0IHN0aWxsIGV4aXN0LlxuICAgICAgICBzZW5kKCdBTklNQVRJT05fRU5EJyk7XG4gICAgfSwgW1xuICAgICAgICBub2RlMSxcbiAgICAgICAgc2VuZFxuICAgIF0pO1xuICAgIHJldHVybiB7XG4gICAgICAgIGlzUHJlc2VudDogW1xuICAgICAgICAgICAgJ21vdW50ZWQnLFxuICAgICAgICAgICAgJ3VubW91bnRTdXNwZW5kZWQnXG4gICAgICAgIF0uaW5jbHVkZXMoc3RhdGUpLFxuICAgICAgICByZWY6ICRpcXEzciR1c2VDYWxsYmFjaygobm9kZSk9PntcbiAgICAgICAgICAgIGlmIChub2RlKSBzdHlsZXNSZWYuY3VycmVudCA9IGdldENvbXB1dGVkU3R5bGUobm9kZSk7XG4gICAgICAgICAgICBzZXROb2RlKG5vZGUpO1xuICAgICAgICB9LCBbXSlcbiAgICB9O1xufVxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qLyBmdW5jdGlvbiAkOTIxYTg4OWNlZTZkZjdlOCR2YXIkZ2V0QW5pbWF0aW9uTmFtZShzdHlsZXMpIHtcbiAgICByZXR1cm4gKHN0eWxlcyA9PT0gbnVsbCB8fCBzdHlsZXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHN0eWxlcy5hbmltYXRpb25OYW1lKSB8fCAnbm9uZSc7XG59XG5cblxuXG5cbmV4cG9ydCB7JDkyMWE4ODljZWU2ZGY3ZTgkZXhwb3J0JDk5YzJiNzc5YWE0ZThiOGIgYXMgUHJlc2VuY2V9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIkNoaWxkcmVuIiwiJGlxcTNyJENoaWxkcmVuIiwiY2xvbmVFbGVtZW50IiwiJGlxcTNyJGNsb25lRWxlbWVudCIsInVzZVN0YXRlIiwiJGlxcTNyJHVzZVN0YXRlIiwidXNlUmVmIiwiJGlxcTNyJHVzZVJlZiIsInVzZUVmZmVjdCIsIiRpcXEzciR1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIiRpcXEzciR1c2VDYWxsYmFjayIsInVzZVJlZHVjZXIiLCIkaXFxM3IkdXNlUmVkdWNlciIsImZsdXNoU3luYyIsIiRpcXEzciRmbHVzaFN5bmMiLCJ1c2VDb21wb3NlZFJlZnMiLCIkaXFxM3IkdXNlQ29tcG9zZWRSZWZzIiwidXNlTGF5b3V0RWZmZWN0IiwiJGlxcTNyJHVzZUxheW91dEVmZmVjdCIsIiRmZTk2M2IzNTUzNDdjYzY4JGV4cG9ydCQzZTY1NDNkZTE0Zjg2MTRmIiwiaW5pdGlhbFN0YXRlIiwibWFjaGluZSIsInN0YXRlIiwiZXZlbnQiLCJuZXh0U3RhdGUiLCIkOTIxYTg4OWNlZTZkZjdlOCRleHBvcnQkOTljMmI3NzlhYTRlOGI4YiIsInByb3BzIiwicHJlc2VudCIsImNoaWxkcmVuIiwicHJlc2VuY2UiLCIkOTIxYTg4OWNlZTZkZjdlOCR2YXIkdXNlUHJlc2VuY2UiLCJjaGlsZCIsImlzUHJlc2VudCIsIm9ubHkiLCJyZWYiLCJmb3JjZU1vdW50IiwiZGlzcGxheU5hbWUiLCJub2RlMSIsInNldE5vZGUiLCJzdHlsZXNSZWYiLCJwcmV2UHJlc2VudFJlZiIsInByZXZBbmltYXRpb25OYW1lUmVmIiwic2VuZCIsIm1vdW50ZWQiLCJVTk1PVU5UIiwiQU5JTUFUSU9OX09VVCIsInVubW91bnRTdXNwZW5kZWQiLCJNT1VOVCIsIkFOSU1BVElPTl9FTkQiLCJ1bm1vdW50ZWQiLCJjdXJyZW50QW5pbWF0aW9uTmFtZSIsIiQ5MjFhODg5Y2VlNmRmN2U4JHZhciRnZXRBbmltYXRpb25OYW1lIiwiY3VycmVudCIsInN0eWxlcyIsIndhc1ByZXNlbnQiLCJoYXNQcmVzZW50Q2hhbmdlZCIsInByZXZBbmltYXRpb25OYW1lIiwiZGlzcGxheSIsImlzQW5pbWF0aW5nIiwiaGFuZGxlQW5pbWF0aW9uRW5kIiwiaXNDdXJyZW50QW5pbWF0aW9uIiwiaW5jbHVkZXMiLCJhbmltYXRpb25OYW1lIiwidGFyZ2V0IiwiaGFuZGxlQW5pbWF0aW9uU3RhcnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsIm5vZGUiLCJnZXRDb21wdXRlZFN0eWxlIiwiUHJlc2VuY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ $8927f6f2acc4f386$export$250ffa63cdc0d034),\n/* harmony export */   Root: () => (/* binding */ $8927f6f2acc4f386$export$be92b6f5f03c0fe9),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ $8927f6f2acc4f386$export$6d1a0317bde7de7f)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n\n\n\n\nconst $8927f6f2acc4f386$var$NODES = [\n    \"a\",\n    \"button\",\n    \"div\",\n    \"form\",\n    \"h2\",\n    \"h3\",\n    \"img\",\n    \"input\",\n    \"label\",\n    \"li\",\n    \"nav\",\n    \"ol\",\n    \"p\",\n    \"span\",\n    \"svg\",\n    \"ul\"\n]; // Temporary while we await merge of this fix:\n// https://github.com/DefinitelyTyped/DefinitelyTyped/pull/55396\n// prettier-ignore\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/ const $8927f6f2acc4f386$export$250ffa63cdc0d034 = $8927f6f2acc4f386$var$NODES.reduce((primitive, node)=>{\n    const Node = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n        const { asChild: asChild, ...primitiveProps } = props;\n        const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : node;\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            window[Symbol.for(\"radix-ui\")] = true;\n        }, []);\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Comp, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, primitiveProps, {\n            ref: forwardedRef\n        }));\n    });\n    Node.displayName = `Primitive.${node}`;\n    return {\n        ...primitive,\n        [node]: Node\n    };\n}, {});\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/ /**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not nessesary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */ function $8927f6f2acc4f386$export$6d1a0317bde7de7f(target, event) {\n    if (target) (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.flushSync)(()=>target.dispatchEvent(event));\n}\n/* -----------------------------------------------------------------------------------------------*/ const $8927f6f2acc4f386$export$be92b6f5f03c0fe9 = $8927f6f2acc4f386$export$250ffa63cdc0d034;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: () => (/* binding */ $57acba87d6e25586$export$ac61190d9fc311a9),\n/* harmony export */   Root: () => (/* binding */ $57acba87d6e25586$export$be92b6f5f03c0fe9),\n/* harmony export */   ScrollArea: () => (/* binding */ $57acba87d6e25586$export$ccf8d8d7bbf3c2cc),\n/* harmony export */   ScrollAreaCorner: () => (/* binding */ $57acba87d6e25586$export$56969d565df7cc4b),\n/* harmony export */   ScrollAreaScrollbar: () => (/* binding */ $57acba87d6e25586$export$2fabd85d0eba3c57),\n/* harmony export */   ScrollAreaThumb: () => (/* binding */ $57acba87d6e25586$export$9fba1154677d7cd2),\n/* harmony export */   ScrollAreaViewport: () => (/* binding */ $57acba87d6e25586$export$a21cbf9f11fca853),\n/* harmony export */   Scrollbar: () => (/* binding */ $57acba87d6e25586$export$9a4e88b92edfce6b),\n/* harmony export */   Thumb: () => (/* binding */ $57acba87d6e25586$export$6521433ed15a34db),\n/* harmony export */   Viewport: () => (/* binding */ $57acba87d6e25586$export$d5c6c08dc2d3ca7),\n/* harmony export */   createScrollAreaScope: () => (/* binding */ $57acba87d6e25586$export$488468afe3a6f2b1)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n\n\n\n\n\n\n\n\n\n\n\nfunction $6c2e24571c90391f$export$3e6543de14f8614f(initialState, machine) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState !== null && nextState !== void 0 ? nextState : state;\n    }, initialState);\n}\n/* -------------------------------------------------------------------------------------------------\n * ScrollArea\n * -----------------------------------------------------------------------------------------------*/ const $57acba87d6e25586$var$SCROLL_AREA_NAME = \"ScrollArea\";\nconst [$57acba87d6e25586$var$createScrollAreaContext, $57acba87d6e25586$export$488468afe3a6f2b1] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)($57acba87d6e25586$var$SCROLL_AREA_NAME);\nconst [$57acba87d6e25586$var$ScrollAreaProvider, $57acba87d6e25586$var$useScrollAreaContext] = $57acba87d6e25586$var$createScrollAreaContext($57acba87d6e25586$var$SCROLL_AREA_NAME);\nconst $57acba87d6e25586$export$ccf8d8d7bbf3c2cc = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { __scopeScrollArea: __scopeScrollArea, type: type = \"hover\", dir: dir, scrollHideDelay: scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewport, setViewport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scrollbarX, setScrollbarX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scrollbarY, setScrollbarY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cornerWidth, setCornerWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [cornerHeight, setCornerHeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollArea(node));\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type: type,\n        dir: direction,\n        scrollHideDelay: scrollHideDelay,\n        scrollArea: scrollArea,\n        viewport: viewport,\n        onViewportChange: setViewport,\n        content: content,\n        onContentChange: setContent,\n        scrollbarX: scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled: scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY: scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled: scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight\n    }, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        dir: direction\n    }, scrollAreaProps, {\n        ref: composedRefs,\n        style: {\n            position: \"relative\",\n            // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n            [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n            [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n            ...props.style\n        }\n    })));\n});\n/*#__PURE__*/ Object.assign($57acba87d6e25586$export$ccf8d8d7bbf3c2cc, {\n    displayName: $57acba87d6e25586$var$SCROLL_AREA_NAME\n});\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaViewport\n * -----------------------------------------------------------------------------------------------*/ const $57acba87d6e25586$var$VIEWPORT_NAME = \"ScrollAreaViewport\";\nconst $57acba87d6e25586$export$a21cbf9f11fca853 = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { __scopeScrollArea: __scopeScrollArea, children: children, ...viewportProps } = props;\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$VIEWPORT_NAME, __scopeScrollArea);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"style\", {\n        dangerouslySetInnerHTML: {\n            __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n        }\n    }), /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        \"data-radix-scroll-area-viewport\": \"\"\n    }, viewportProps, {\n        ref: composedRefs,\n        style: {\n            /**\n       * We don't support `visible` because the intention is to have at least one scrollbar\n       * if this component is used and `visible` will behave like `auto` in that case\n       * https://developer.mozilla.org/en-US/docs/Web/CSS/overflowed#description\n       *\n       * We don't handle `auto` because the intention is for the native implementation\n       * to be hidden if using this component. We just want to ensure the node is scrollable\n       * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n       * the browser from having to work out whether to render native scrollbars or not,\n       * we tell it to with the intention of hiding them in CSS.\n       */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n            overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n            ...props.style\n        }\n    }), /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"div\", {\n        ref: context.onContentChange,\n        style: {\n            minWidth: \"100%\",\n            display: \"table\"\n        }\n    }, children)));\n});\n/*#__PURE__*/ Object.assign($57acba87d6e25586$export$a21cbf9f11fca853, {\n    displayName: $57acba87d6e25586$var$VIEWPORT_NAME\n});\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaScrollbar\n * -----------------------------------------------------------------------------------------------*/ const $57acba87d6e25586$var$SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nconst $57acba87d6e25586$export$2fabd85d0eba3c57 = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { forceMount: forceMount, ...scrollbarProps } = props;\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange: onScrollbarXEnabledChange, onScrollbarYEnabledChange: onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n        return ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n        };\n    }, [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarHover, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, scrollbarProps, {\n        ref: forwardedRef,\n        forceMount: forceMount\n    })) : context.type === \"scroll\" ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarScroll, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, scrollbarProps, {\n        ref: forwardedRef,\n        forceMount: forceMount\n    })) : context.type === \"auto\" ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarAuto, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, scrollbarProps, {\n        ref: forwardedRef,\n        forceMount: forceMount\n    })) : context.type === \"always\" ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarVisible, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, scrollbarProps, {\n        ref: forwardedRef\n    })) : null;\n});\n/*#__PURE__*/ Object.assign($57acba87d6e25586$export$2fabd85d0eba3c57, {\n    displayName: $57acba87d6e25586$var$SCROLLBAR_NAME\n});\n/* -----------------------------------------------------------------------------------------------*/ const $57acba87d6e25586$var$ScrollAreaScrollbarHover = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { forceMount: forceMount, ...scrollbarProps } = props;\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const scrollArea = context.scrollArea;\n        let hideTimer = 0;\n        if (scrollArea) {\n            const handlePointerEnter = ()=>{\n                window.clearTimeout(hideTimer);\n                setVisible(true);\n            };\n            const handlePointerLeave = ()=>{\n                hideTimer = window.setTimeout(()=>setVisible(false), context.scrollHideDelay);\n            };\n            scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n            scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n            return ()=>{\n                window.clearTimeout(hideTimer);\n                scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n            };\n        }\n    }, [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible\n    }, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarAuto, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        \"data-state\": visible ? \"visible\" : \"hidden\"\n    }, scrollbarProps, {\n        ref: forwardedRef\n    })));\n});\nconst $57acba87d6e25586$var$ScrollAreaScrollbarScroll = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { forceMount: forceMount, ...scrollbarProps } = props;\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = $57acba87d6e25586$var$useDebounceCallback(()=>send(\"SCROLL_END\"), 100);\n    const [state, send] = $6c2e24571c90391f$export$3e6543de14f8614f(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (state === \"idle\") {\n            const hideTimer = window.setTimeout(()=>send(\"HIDE\"), context.scrollHideDelay);\n            return ()=>window.clearTimeout(hideTimer);\n        }\n    }, [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const viewport = context.viewport;\n        const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n        if (viewport) {\n            let prevScrollPos = viewport[scrollDirection];\n            const handleScroll = ()=>{\n                const scrollPos = viewport[scrollDirection];\n                const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                if (hasScrollInDirectionChanged) {\n                    send(\"SCROLL\");\n                    debounceScrollEnd();\n                }\n                prevScrollPos = scrollPos;\n            };\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\"\n    }, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarVisible, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\"\n    }, scrollbarProps, {\n        ref: forwardedRef,\n        onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n    })));\n});\nconst $57acba87d6e25586$var$ScrollAreaScrollbarAuto = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount: forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = $57acba87d6e25586$var$useDebounceCallback(()=>{\n        if (context.viewport) {\n            const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n            const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n            setVisible(isHorizontal ? isOverflowX : isOverflowY);\n        }\n    }, 10);\n    $57acba87d6e25586$var$useResizeObserver(context.viewport, handleResize);\n    $57acba87d6e25586$var$useResizeObserver(context.content, handleResize);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible\n    }, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarVisible, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        \"data-state\": visible ? \"visible\" : \"hidden\"\n    }, scrollbarProps, {\n        ref: forwardedRef\n    })));\n});\n/* -----------------------------------------------------------------------------------------------*/ const $57acba87d6e25586$var$ScrollAreaScrollbarVisible = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { orientation: orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pointerOffsetRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const [sizes, setSizes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = $57acba87d6e25586$var$getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes: sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return $57acba87d6e25586$var$getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarX, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        ref: forwardedRef,\n        onThumbPositionChange: ()=>{\n            if (context.viewport && thumbRef.current) {\n                const scrollPos = context.viewport.scrollLeft;\n                const offset = $57acba87d6e25586$var$getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n            }\n        },\n        onWheelScroll: (scrollPos)=>{\n            if (context.viewport) context.viewport.scrollLeft = scrollPos;\n        },\n        onDragScroll: (pointerPos)=>{\n            if (context.viewport) context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n        }\n    }));\n    if (orientation === \"vertical\") return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarY, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, commonProps, {\n        ref: forwardedRef,\n        onThumbPositionChange: ()=>{\n            if (context.viewport && thumbRef.current) {\n                const scrollPos = context.viewport.scrollTop;\n                const offset = $57acba87d6e25586$var$getThumbOffsetFromScroll(scrollPos, sizes);\n                thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n            }\n        },\n        onWheelScroll: (scrollPos)=>{\n            if (context.viewport) context.viewport.scrollTop = scrollPos;\n        },\n        onDragScroll: (pointerPos)=>{\n            if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n        }\n    }));\n    return null;\n});\n/* -----------------------------------------------------------------------------------------------*/ const $57acba87d6e25586$var$ScrollAreaScrollbarX = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { sizes: sizes, onSizesChange: onSizesChange, ...scrollbarProps } = props;\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarImpl, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        \"data-orientation\": \"horizontal\"\n    }, scrollbarProps, {\n        ref: composeRefs,\n        sizes: sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: $57acba87d6e25586$var$getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos); // prevent window scroll when wheeling on scrollbar\n                if ($57acba87d6e25586$var$isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) event.preventDefault();\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) onSizesChange({\n                content: context.viewport.scrollWidth,\n                viewport: context.viewport.offsetWidth,\n                scrollbar: {\n                    size: ref.current.clientWidth,\n                    paddingStart: $57acba87d6e25586$var$toInt(computedStyle.paddingLeft),\n                    paddingEnd: $57acba87d6e25586$var$toInt(computedStyle.paddingRight)\n                }\n            });\n        }\n    }));\n});\nconst $57acba87d6e25586$var$ScrollAreaScrollbarY = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { sizes: sizes, onSizesChange: onSizesChange, ...scrollbarProps } = props;\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaScrollbarImpl, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        \"data-orientation\": \"vertical\"\n    }, scrollbarProps, {\n        ref: composeRefs,\n        sizes: sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : undefined,\n            left: context.dir === \"rtl\" ? 0 : undefined,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: $57acba87d6e25586$var$getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos); // prevent window scroll when wheeling on scrollbar\n                if ($57acba87d6e25586$var$isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) event.preventDefault();\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) onSizesChange({\n                content: context.viewport.scrollHeight,\n                viewport: context.viewport.offsetHeight,\n                scrollbar: {\n                    size: ref.current.clientHeight,\n                    paddingStart: $57acba87d6e25586$var$toInt(computedStyle.paddingTop),\n                    paddingEnd: $57acba87d6e25586$var$toInt(computedStyle.paddingBottom)\n                }\n            });\n        }\n    }));\n});\n/* -----------------------------------------------------------------------------------------------*/ const [$57acba87d6e25586$var$ScrollbarProvider, $57acba87d6e25586$var$useScrollbarContext] = $57acba87d6e25586$var$createScrollAreaContext($57acba87d6e25586$var$SCROLLBAR_NAME);\nconst $57acba87d6e25586$var$ScrollAreaScrollbarImpl = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { __scopeScrollArea: __scopeScrollArea, sizes: sizes, hasThumb: hasThumb, onThumbChange: onThumbChange, onThumbPointerUp: onThumbPointerUp, onThumbPointerDown: onThumbPointerDown, onThumbPositionChange: onThumbPositionChange, onDragScroll: onDragScroll, onWheelScroll: onWheelScroll, onResize: onResize, ...scrollbarProps } = props;\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollbar(node));\n    const rectRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const prevWebkitUserSelectRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = $57acba87d6e25586$var$useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x: x,\n                y: y\n            });\n        }\n    }\n    /**\n   * We bind wheel event imperatively so we can switch off passive\n   * mode for document wheel event to allow it to be prevented\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleWheel = (event)=>{\n            const element = event.target;\n            const isScrollbarWheel = scrollbar === null || scrollbar === void 0 ? void 0 : scrollbar.contains(element);\n            if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n        };\n        document.addEventListener(\"wheel\", handleWheel, {\n            passive: false\n        });\n        return ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n    }, [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    /**\n   * Update thumb position on sizes change\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    $57acba87d6e25586$var$useResizeObserver(scrollbar, handleResize);\n    $57acba87d6e25586$var$useResizeObserver(context.content, handleResize);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar: scrollbar,\n        hasThumb: hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown)\n    }, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, scrollbarProps, {\n        ref: composeRefs,\n        style: {\n            position: \"absolute\",\n            ...scrollbarProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n            const mainPointer = 0;\n            if (event.button === mainPointer) {\n                const element = event.target;\n                element.setPointerCapture(event.pointerId);\n                rectRef.current = scrollbar.getBoundingClientRect(); // pointer capture doesn't prevent text selection in Safari\n                // so we remove text selection manually when scrolling\n                prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                document.body.style.webkitUserSelect = \"none\";\n                if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                handleDragScroll(event);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            const element = event.target;\n            if (element.hasPointerCapture(event.pointerId)) element.releasePointerCapture(event.pointerId);\n            document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n            if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n            rectRef.current = null;\n        })\n    })));\n});\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaThumb\n * -----------------------------------------------------------------------------------------------*/ const $57acba87d6e25586$var$THUMB_NAME = \"ScrollAreaThumb\";\nconst $57acba87d6e25586$export$9fba1154677d7cd2 = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { forceMount: forceMount, ...thumbProps } = props;\n    const scrollbarContext = $57acba87d6e25586$var$useScrollbarContext($57acba87d6e25586$var$THUMB_NAME, props.__scopeScrollArea);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb\n    }, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaThumbImpl, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        ref: forwardedRef\n    }, thumbProps)));\n});\nconst $57acba87d6e25586$var$ScrollAreaThumbImpl = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { __scopeScrollArea: __scopeScrollArea, style: style, ...thumbProps } = props;\n    const scrollAreaContext = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = $57acba87d6e25586$var$useScrollbarContext($57acba87d6e25586$var$THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange: onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>scrollbarContext.onThumbChange(node));\n    const removeUnlinkedScrollListenerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const debounceScrollEnd = $57acba87d6e25586$var$useDebounceCallback(()=>{\n        if (removeUnlinkedScrollListenerRef.current) {\n            removeUnlinkedScrollListenerRef.current();\n            removeUnlinkedScrollListenerRef.current = undefined;\n        }\n    }, 100);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const viewport = scrollAreaContext.viewport;\n        if (viewport) {\n            /**\n       * We only bind to native scroll event so we know when scroll starts and ends.\n       * When scroll starts we start a requestAnimationFrame loop that checks for\n       * changes to scroll position. That rAF loop triggers our thumb position change\n       * when relevant to avoid scroll-linked effects. We cancel the loop when scroll ends.\n       * https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\n       */ const handleScroll = ()=>{\n                debounceScrollEnd();\n                if (!removeUnlinkedScrollListenerRef.current) {\n                    const listener = $57acba87d6e25586$var$addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                    removeUnlinkedScrollListenerRef.current = listener;\n                    onThumbPositionChange();\n                }\n            };\n            onThumbPositionChange();\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\"\n    }, thumbProps, {\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x: x,\n                y: y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    }));\n});\n/*#__PURE__*/ Object.assign($57acba87d6e25586$export$9fba1154677d7cd2, {\n    displayName: $57acba87d6e25586$var$THUMB_NAME\n});\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaCorner\n * -----------------------------------------------------------------------------------------------*/ const $57acba87d6e25586$var$CORNER_NAME = \"ScrollAreaCorner\";\nconst $57acba87d6e25586$export$56969d565df7cc4b = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($57acba87d6e25586$var$ScrollAreaCornerImpl, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n        ref: forwardedRef\n    })) : null;\n});\n/*#__PURE__*/ Object.assign($57acba87d6e25586$export$56969d565df7cc4b, {\n    displayName: $57acba87d6e25586$var$CORNER_NAME\n});\n/* -----------------------------------------------------------------------------------------------*/ const $57acba87d6e25586$var$ScrollAreaCornerImpl = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { __scopeScrollArea: __scopeScrollArea, ...cornerProps } = props;\n    const context = $57acba87d6e25586$var$useScrollAreaContext($57acba87d6e25586$var$CORNER_NAME, __scopeScrollArea);\n    const [width1, setWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [height1, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const hasSize = Boolean(width1 && height1);\n    $57acba87d6e25586$var$useResizeObserver(context.scrollbarX, ()=>{\n        var _context$scrollbarX;\n        const height = ((_context$scrollbarX = context.scrollbarX) === null || _context$scrollbarX === void 0 ? void 0 : _context$scrollbarX.offsetHeight) || 0;\n        context.onCornerHeightChange(height);\n        setHeight(height);\n    });\n    $57acba87d6e25586$var$useResizeObserver(context.scrollbarY, ()=>{\n        var _context$scrollbarY;\n        const width = ((_context$scrollbarY = context.scrollbarY) === null || _context$scrollbarY === void 0 ? void 0 : _context$scrollbarY.offsetWidth) || 0;\n        context.onCornerWidthChange(width);\n        setWidth(width);\n    });\n    return hasSize ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, cornerProps, {\n        ref: forwardedRef,\n        style: {\n            width: width1,\n            height: height1,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : undefined,\n            left: context.dir === \"rtl\" ? 0 : undefined,\n            bottom: 0,\n            ...props.style\n        }\n    })) : null;\n});\n/* -----------------------------------------------------------------------------------------------*/ function $57acba87d6e25586$var$toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction $57acba87d6e25586$var$getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction $57acba87d6e25586$var$getThumbSize(sizes) {\n    const ratio = $57acba87d6e25586$var$getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio; // minimum of 18 matches macOS minimum\n    return Math.max(thumbSize, 18);\n}\nfunction $57acba87d6e25586$var$getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n    const thumbSizePx = $57acba87d6e25586$var$getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = $57acba87d6e25586$var$linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction $57acba87d6e25586$var$getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n    const thumbSizePx = $57acba87d6e25586$var$getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = $57acba87d6e25586$var$linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n} // https://github.com/tmcw-up-for-adoption/simple-linear-scale/blob/master/index.js\nfunction $57acba87d6e25586$var$linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction $57acba87d6e25586$var$isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n} // Custom scroll handler to avoid scroll-linked effects\n// https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\nconst $57acba87d6e25586$var$addUnlinkedScrollListener = (node, handler = ()=>{})=>{\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction $57acba87d6e25586$var$useDebounceCallback(callback, delay) {\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>()=>window.clearTimeout(debounceTimerRef.current), []);\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        window.clearTimeout(debounceTimerRef.current);\n        debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n    }, [\n        handleCallback,\n        delay\n    ]);\n}\nfunction $57acba87d6e25586$var$useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)(()=>{\n        let rAF = 0;\n        if (element) {\n            /**\n       * Resize Observer will throw an often benign error that says `ResizeObserver loop\n       * completed with undelivered notifications`. This means that ResizeObserver was not\n       * able to deliver all observations within a single animation frame, so we use\n       * `requestAnimationFrame` to ensure we don't deliver unnecessary observations.\n       * Further reading: https://github.com/WICG/resize-observer/issues/38\n       */ const resizeObserver = new ResizeObserver(()=>{\n                cancelAnimationFrame(rAF);\n                rAF = window.requestAnimationFrame(handleResize);\n            });\n            resizeObserver.observe(element);\n            return ()=>{\n                window.cancelAnimationFrame(rAF);\n                resizeObserver.unobserve(element);\n            };\n        }\n    }, [\n        element,\n        handleResize\n    ]);\n}\n/* -----------------------------------------------------------------------------------------------*/ const $57acba87d6e25586$export$be92b6f5f03c0fe9 = $57acba87d6e25586$export$ccf8d8d7bbf3c2cc;\nconst $57acba87d6e25586$export$d5c6c08dc2d3ca7 = $57acba87d6e25586$export$a21cbf9f11fca853;\nconst $57acba87d6e25586$export$9a4e88b92edfce6b = $57acba87d6e25586$export$2fabd85d0eba3c57;\nconst $57acba87d6e25586$export$6521433ed15a34db = $57acba87d6e25586$export$9fba1154677d7cd2;\nconst $57acba87d6e25586$export$ac61190d9fc311a9 = $57acba87d6e25586$export$56969d565df7cc4b;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ $5e63c961fc1ce211$export$be92b6f5f03c0fe9),\n/* harmony export */   Slot: () => (/* binding */ $5e63c961fc1ce211$export$8c6ed5c666ac1360),\n/* harmony export */   Slottable: () => (/* binding */ $5e63c961fc1ce211$export$d9f1ccf0bdb05d45)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n\n\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/ const $5e63c961fc1ce211$export$8c6ed5c666ac1360 = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { children: children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_1__.Children.toArray(children);\n    const slottable = childrenArray.find($5e63c961fc1ce211$var$isSlottable);\n    if (slottable) {\n        // the new element to render is the one passed as a child of `Slottable`\n        const newElement = slottable.props.children;\n        const newChildren = childrenArray.map((child)=>{\n            if (child === slottable) {\n                // because the new element will be the one rendered, we are only interested\n                // in grabbing its children (`newElement.props.children`)\n                if (react__WEBPACK_IMPORTED_MODULE_1__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_1__.Children.only(null);\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.isValidElement)(newElement) ? newElement.props.children : null;\n            } else return child;\n        });\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($5e63c961fc1ce211$var$SlotClone, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, slotProps, {\n            ref: forwardedRef\n        }), /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.isValidElement)(newElement) ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.cloneElement)(newElement, undefined, newChildren) : null);\n    }\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)($5e63c961fc1ce211$var$SlotClone, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, slotProps, {\n        ref: forwardedRef\n    }), children);\n});\n$5e63c961fc1ce211$export$8c6ed5c666ac1360.displayName = \"Slot\";\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/ const $5e63c961fc1ce211$var$SlotClone = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, forwardedRef)=>{\n    const { children: children, ...slotProps } = props;\n    if (/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.isValidElement)(children)) return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.cloneElement)(children, {\n        ...$5e63c961fc1ce211$var$mergeProps(slotProps, children.props),\n        ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, children.ref) : children.ref\n    });\n    return react__WEBPACK_IMPORTED_MODULE_1__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_1__.Children.only(null) : null;\n});\n$5e63c961fc1ce211$var$SlotClone.displayName = \"SlotClone\";\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/ const $5e63c961fc1ce211$export$d9f1ccf0bdb05d45 = ({ children: children })=>{\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, children);\n};\n/* ---------------------------------------------------------------------------------------------- */ function $5e63c961fc1ce211$var$isSlottable(child) {\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.isValidElement)(child) && child.type === $5e63c961fc1ce211$export$d9f1ccf0bdb05d45;\n}\nfunction $5e63c961fc1ce211$var$mergeProps(slotProps, childProps) {\n    // all child props should override\n    const overrideProps = {\n        ...childProps\n    };\n    for(const propName in childProps){\n        const slotPropValue = slotProps[propName];\n        const childPropValue = childProps[propName];\n        const isHandler = /^on[A-Z]/.test(propName);\n        if (isHandler) {\n            // if the handler exists on both, we compose them\n            if (slotPropValue && childPropValue) overrideProps[propName] = (...args)=>{\n                childPropValue(...args);\n                slotPropValue(...args);\n            };\n            else if (slotPropValue) overrideProps[propName] = slotPropValue;\n        } else if (propName === \"style\") overrideProps[propName] = {\n            ...slotPropValue,\n            ...childPropValue\n        };\n        else if (propName === \"className\") overrideProps[propName] = [\n            slotPropValue,\n            childPropValue\n        ].filter(Boolean).join(\" \");\n    }\n    return {\n        ...slotProps,\n        ...overrideProps\n    };\n}\nconst $5e63c961fc1ce211$export$be92b6f5f03c0fe9 = $5e63c961fc1ce211$export$8c6ed5c666ac1360;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ $b1b2314f5f9a1d84$export$25bec8c6f54ee79a)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */ function $b1b2314f5f9a1d84$export$25bec8c6f54ee79a(callback) {\n    const callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(callback);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        callbackRef.current = callback;\n    }); // https://github.com/facebook/react/issues/19240\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>(...args)=>{\n            var _callbackRef$current;\n            return (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 ? void 0 : _callbackRef$current.call(callbackRef, ...args);\n        }, []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ $9f79659886946c16$export$e5c5a5f917a5871c)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n/**\n * On the server, React emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */ const $9f79659886946c16$export$e5c5a5f917a5871c = Boolean(globalThis === null || globalThis === void 0 ? void 0 : globalThis.document) ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : ()=>{};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdFO0FBR2hFOzs7Ozs7Q0FNQyxHQUFHLE1BQU1FLDRDQUE0Q0MsUUFBUUMsZUFBZSxRQUFRQSxlQUFlLEtBQUssSUFBSSxLQUFLLElBQUlBLFdBQVdDLFFBQVEsSUFBSUosa0RBQXNCQSxHQUFHLEtBQUs7QUFLckcsQ0FDdEUsa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdC9kaXN0L2luZGV4Lm1qcz8yZDZmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7dXNlTGF5b3V0RWZmZWN0IGFzICRkeGx3SCR1c2VMYXlvdXRFZmZlY3R9IGZyb20gXCJyZWFjdFwiO1xuXG5cbi8qKlxuICogT24gdGhlIHNlcnZlciwgUmVhY3QgZW1pdHMgYSB3YXJuaW5nIHdoZW4gY2FsbGluZyBgdXNlTGF5b3V0RWZmZWN0YC5cbiAqIFRoaXMgaXMgYmVjYXVzZSBuZWl0aGVyIGB1c2VMYXlvdXRFZmZlY3RgIG5vciBgdXNlRWZmZWN0YCBydW4gb24gdGhlIHNlcnZlci5cbiAqIFdlIHVzZSB0aGlzIHNhZmUgdmVyc2lvbiB3aGljaCBzdXBwcmVzc2VzIHRoZSB3YXJuaW5nIGJ5IHJlcGxhY2luZyBpdCB3aXRoIGEgbm9vcCBvbiB0aGUgc2VydmVyLlxuICpcbiAqIFNlZTogaHR0cHM6Ly9yZWFjdGpzLm9yZy9kb2NzL2hvb2tzLXJlZmVyZW5jZS5odG1sI3VzZWxheW91dGVmZmVjdFxuICovIGNvbnN0ICQ5Zjc5NjU5ODg2OTQ2YzE2JGV4cG9ydCRlNWM1YTVmOTE3YTU4NzFjID0gQm9vbGVhbihnbG9iYWxUaGlzID09PSBudWxsIHx8IGdsb2JhbFRoaXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGdsb2JhbFRoaXMuZG9jdW1lbnQpID8gJGR4bHdIJHVzZUxheW91dEVmZmVjdCA6ICgpPT57fTtcblxuXG5cblxuZXhwb3J0IHskOWY3OTY1OTg4Njk0NmMxNiRleHBvcnQkZTVjNWE1ZjkxN2E1ODcxYyBhcyB1c2VMYXlvdXRFZmZlY3R9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbInVzZUxheW91dEVmZmVjdCIsIiRkeGx3SCR1c2VMYXlvdXRFZmZlY3QiLCIkOWY3OTY1OTg4Njk0NmMxNiRleHBvcnQkZTVjNWE1ZjkxN2E1ODcxYyIsIkJvb2xlYW4iLCJnbG9iYWxUaGlzIiwiZG9jdW1lbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ })

};
;