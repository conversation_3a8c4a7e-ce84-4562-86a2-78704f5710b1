"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-strikethrough";
exports.ids = ["vendor-chunks/mdast-util-gfm-strikethrough"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-strikethrough/lib/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-strikethrough/lib/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmStrikethroughFromMarkdown: () => (/* binding */ gfmStrikethroughFromMarkdown),\n/* harmony export */   gfmStrikethroughToMarkdown: () => (/* binding */ gfmStrikethroughToMarkdown)\n/* harmony export */ });\n/**\n * @typedef {import('mdast').Delete} Delete\n *\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n *\n * @typedef {import('mdast-util-to-markdown').ConstructName} ConstructName\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n */ /**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain strikethrough.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * Note: keep in sync with: <https://github.com/syntax-tree/mdast-util-to-markdown/blob/8ce8dbf/lib/unsafe.js#L14>\n *\n * @type {Array<ConstructName>}\n */ const constructsWithoutStrikethrough = [\n    \"autolink\",\n    \"destinationLiteral\",\n    \"destinationRaw\",\n    \"reference\",\n    \"titleQuote\",\n    \"titleApostrophe\"\n];\nhandleDelete.peek = peekDelete;\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM\n * strikethrough in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable GFM strikethrough.\n */ function gfmStrikethroughFromMarkdown() {\n    return {\n        canContainEols: [\n            \"delete\"\n        ],\n        enter: {\n            strikethrough: enterStrikethrough\n        },\n        exit: {\n            strikethrough: exitStrikethrough\n        }\n    };\n}\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM\n * strikethrough in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM strikethrough.\n */ function gfmStrikethroughToMarkdown() {\n    return {\n        unsafe: [\n            {\n                character: \"~\",\n                inConstruct: \"phrasing\",\n                notInConstruct: constructsWithoutStrikethrough\n            }\n        ],\n        handlers: {\n            delete: handleDelete\n        }\n    };\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterStrikethrough(token) {\n    this.enter({\n        type: \"delete\",\n        children: []\n    }, token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitStrikethrough(token) {\n    this.exit(token);\n}\n/**\n * @type {ToMarkdownHandle}\n * @param {Delete} node\n */ function handleDelete(node, _, state, info) {\n    const tracker = state.createTracker(info);\n    const exit = state.enter(\"strikethrough\");\n    let value = tracker.move(\"~~\");\n    value += state.containerPhrasing(node, {\n        ...tracker.current(),\n        before: value,\n        after: \"~\"\n    });\n    value += tracker.move(\"~~\");\n    exit();\n    return value;\n}\n/** @type {ToMarkdownHandle} */ function peekDelete() {\n    return \"~\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-strikethrough/lib/index.js\n");

/***/ })

};
;