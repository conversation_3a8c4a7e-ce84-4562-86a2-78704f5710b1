"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ungap";
exports.ids = ["vendor-chunks/@ungap"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/deserialize.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\");\n\nconst env = typeof self === \"object\" ? self : globalThis;\nconst deserializer = ($, _)=>{\n    const as = (out, index)=>{\n        $.set(index, out);\n        return out;\n    };\n    const unpair = (index)=>{\n        if ($.has(index)) return $.get(index);\n        const [type, value] = _[index];\n        switch(type){\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE:\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.VOID:\n                return as(value, index);\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY:\n                {\n                    const arr = as([], index);\n                    for (const index of value)arr.push(unpair(index));\n                    return arr;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT:\n                {\n                    const object = as({}, index);\n                    for (const [key, index] of value)object[unpair(key)] = unpair(index);\n                    return object;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:\n                return as(new Date(value), index);\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP:\n                {\n                    const { source, flags } = value;\n                    return as(new RegExp(source, flags), index);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP:\n                {\n                    const map = as(new Map, index);\n                    for (const [key, index] of value)map.set(unpair(key), unpair(index));\n                    return map;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET:\n                {\n                    const set = as(new Set, index);\n                    for (const index of value)set.add(unpair(index));\n                    return set;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR:\n                {\n                    const { name, message } = value;\n                    return as(new env[name](message), index);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT:\n                return as(BigInt(value), index);\n            case \"BigInt\":\n                return as(Object(BigInt(value)), index);\n            case \"ArrayBuffer\":\n                return as(new Uint8Array(value).buffer, value);\n            case \"DataView\":\n                {\n                    const { buffer } = new Uint8Array(value);\n                    return as(new DataView(buffer), value);\n                }\n        }\n        return as(new env[type](value), index);\n    };\n    return unpair;\n};\n/**\n * @typedef {Array<string,any>} Record a type representation\n */ /**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */ const deserialize = (serialized)=>deserializer(new Map, serialized)(0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   deserialize: () => (/* reexport safe */ _deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize),\n/* harmony export */   serialize: () => (/* reexport safe */ _serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)\n/* harmony export */ });\n/* harmony import */ var _deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserialize.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js\");\n/* harmony import */ var _serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serialize.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js\");\n\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */ /**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */ /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (typeof structuredClone === \"function\" ? /* c8 ignore start */ (any, options)=>options && (\"json\" in options || \"lossy\" in options) ? (0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)) : structuredClone(any) : (any, options)=>(0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)));\n/* c8 ignore stop */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZDO0FBQ0o7QUFFekM7O0NBRUMsR0FFRDs7Ozs7OztDQU9DLEdBQ0QsaUVBQWUsT0FBT0Usb0JBQW9CLGFBQ3hDLG1CQUFtQixHQUNuQixDQUFDQyxLQUFLQyxVQUNKQSxXQUFZLFdBQVVBLFdBQVcsV0FBV0EsT0FBTSxJQUNoREosNERBQVdBLENBQUNDLHdEQUFTQSxDQUFDRSxLQUFLQyxZQUFZRixnQkFBZ0JDLE9BRTNELENBQUNBLEtBQUtDLFVBQVlKLDREQUFXQSxDQUFDQyx3REFBU0EsQ0FBQ0UsS0FBS0MsU0FBU0EsRUFBQztBQUN2RCxrQkFBa0IsR0FFWSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXN5c3RlbS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AdW5nYXAvc3RydWN0dXJlZC1jbG9uZS9lc20vaW5kZXguanM/YjdkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Rlc2VyaWFsaXplfSBmcm9tICcuL2Rlc2VyaWFsaXplLmpzJztcbmltcG9ydCB7c2VyaWFsaXplfSBmcm9tICcuL3NlcmlhbGl6ZS5qcyc7XG5cbi8qKlxuICogQHR5cGVkZWYge0FycmF5PHN0cmluZyxhbnk+fSBSZWNvcmQgYSB0eXBlIHJlcHJlc2VudGF0aW9uXG4gKi9cblxuLyoqXG4gKiBSZXR1cm5zIGFuIGFycmF5IG9mIHNlcmlhbGl6ZWQgUmVjb3Jkcy5cbiAqIEBwYXJhbSB7YW55fSBhbnkgYSBzZXJpYWxpemFibGUgdmFsdWUuXG4gKiBAcGFyYW0ge3t0cmFuc2Zlcj86IGFueVtdLCBqc29uPzogYm9vbGVhbiwgbG9zc3k/OiBib29sZWFufT99IG9wdGlvbnMgYW4gb2JqZWN0IHdpdGhcbiAqIGEgdHJhbnNmZXIgb3B0aW9uIChpZ25vcmVkIHdoZW4gcG9seWZpbGxlZCkgYW5kL29yIG5vbiBzdGFuZGFyZCBmaWVsZHMgdGhhdFxuICogZmFsbGJhY2sgdG8gdGhlIHBvbHlmaWxsIGlmIHByZXNlbnQuXG4gKiBAcmV0dXJucyB7UmVjb3JkW119XG4gKi9cbmV4cG9ydCBkZWZhdWx0IHR5cGVvZiBzdHJ1Y3R1cmVkQ2xvbmUgPT09IFwiZnVuY3Rpb25cIiA/XG4gIC8qIGM4IGlnbm9yZSBzdGFydCAqL1xuICAoYW55LCBvcHRpb25zKSA9PiAoXG4gICAgb3B0aW9ucyAmJiAoJ2pzb24nIGluIG9wdGlvbnMgfHwgJ2xvc3N5JyBpbiBvcHRpb25zKSA/XG4gICAgICBkZXNlcmlhbGl6ZShzZXJpYWxpemUoYW55LCBvcHRpb25zKSkgOiBzdHJ1Y3R1cmVkQ2xvbmUoYW55KVxuICApIDpcbiAgKGFueSwgb3B0aW9ucykgPT4gZGVzZXJpYWxpemUoc2VyaWFsaXplKGFueSwgb3B0aW9ucykpO1xuICAvKiBjOCBpZ25vcmUgc3RvcCAqL1xuXG5leHBvcnQge2Rlc2VyaWFsaXplLCBzZXJpYWxpemV9O1xuIl0sIm5hbWVzIjpbImRlc2VyaWFsaXplIiwic2VyaWFsaXplIiwic3RydWN0dXJlZENsb25lIiwiYW55Iiwib3B0aW9ucyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/serialize.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\");\n\nconst EMPTY = \"\";\nconst { toString } = {};\nconst { keys } = Object;\nconst typeOf = (value)=>{\n    const type = typeof value;\n    if (type !== \"object\" || !value) return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE,\n        type\n    ];\n    const asString = toString.call(value).slice(8, -1);\n    switch(asString){\n        case \"Array\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY,\n                EMPTY\n            ];\n        case \"Object\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT,\n                EMPTY\n            ];\n        case \"Date\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE,\n                EMPTY\n            ];\n        case \"RegExp\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP,\n                EMPTY\n            ];\n        case \"Map\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP,\n                EMPTY\n            ];\n        case \"Set\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.SET,\n                EMPTY\n            ];\n        case \"DataView\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY,\n                asString\n            ];\n    }\n    if (asString.includes(\"Array\")) return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY,\n        asString\n    ];\n    if (asString.includes(\"Error\")) return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR,\n        asString\n    ];\n    return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT,\n        asString\n    ];\n};\nconst shouldSkip = ([TYPE, type])=>TYPE === _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE && (type === \"function\" || type === \"symbol\");\nconst serializer = (strict, json, $, _)=>{\n    const as = (out, value)=>{\n        const index = _.push(out) - 1;\n        $.set(value, index);\n        return index;\n    };\n    const pair = (value)=>{\n        if ($.has(value)) return $.get(value);\n        let [TYPE, type] = typeOf(value);\n        switch(TYPE){\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE:\n                {\n                    let entry = value;\n                    switch(type){\n                        case \"bigint\":\n                            TYPE = _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT;\n                            entry = value.toString();\n                            break;\n                        case \"function\":\n                        case \"symbol\":\n                            if (strict) throw new TypeError(\"unable to serialize \" + type);\n                            entry = null;\n                            break;\n                        case \"undefined\":\n                            return as([\n                                _types_js__WEBPACK_IMPORTED_MODULE_0__.VOID\n                            ], value);\n                    }\n                    return as([\n                        TYPE,\n                        entry\n                    ], value);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY:\n                {\n                    if (type) {\n                        let spread = value;\n                        if (type === \"DataView\") {\n                            spread = new Uint8Array(value.buffer);\n                        } else if (type === \"ArrayBuffer\") {\n                            spread = new Uint8Array(value);\n                        }\n                        return as([\n                            type,\n                            [\n                                ...spread\n                            ]\n                        ], value);\n                    }\n                    const arr = [];\n                    const index = as([\n                        TYPE,\n                        arr\n                    ], value);\n                    for (const entry of value)arr.push(pair(entry));\n                    return index;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT:\n                {\n                    if (type) {\n                        switch(type){\n                            case \"BigInt\":\n                                return as([\n                                    type,\n                                    value.toString()\n                                ], value);\n                            case \"Boolean\":\n                            case \"Number\":\n                            case \"String\":\n                                return as([\n                                    type,\n                                    value.valueOf()\n                                ], value);\n                        }\n                    }\n                    if (json && \"toJSON\" in value) return pair(value.toJSON());\n                    const entries = [];\n                    const index = as([\n                        TYPE,\n                        entries\n                    ], value);\n                    for (const key of keys(value)){\n                        if (strict || !shouldSkip(typeOf(value[key]))) entries.push([\n                            pair(key),\n                            pair(value[key])\n                        ]);\n                    }\n                    return index;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:\n                return as([\n                    TYPE,\n                    value.toISOString()\n                ], value);\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP:\n                {\n                    const { source, flags } = value;\n                    return as([\n                        TYPE,\n                        {\n                            source,\n                            flags\n                        }\n                    ], value);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP:\n                {\n                    const entries = [];\n                    const index = as([\n                        TYPE,\n                        entries\n                    ], value);\n                    for (const [key, entry] of value){\n                        if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry)))) entries.push([\n                            pair(key),\n                            pair(entry)\n                        ]);\n                    }\n                    return index;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET:\n                {\n                    const entries = [];\n                    const index = as([\n                        TYPE,\n                        entries\n                    ], value);\n                    for (const entry of value){\n                        if (strict || !shouldSkip(typeOf(entry))) entries.push(pair(entry));\n                    }\n                    return index;\n                }\n        }\n        const { message } = value;\n        return as([\n            TYPE,\n            {\n                name: type,\n                message\n            }\n        ], value);\n    };\n    return pair;\n};\n/**\n * @typedef {Array<string,any>} Record a type representation\n */ /**\n * Returns an array of serialized Records.\n * @param {any} value a serializable value.\n * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,\n *  if `true`, will not throw errors on incompatible types, and behave more\n *  like JSON stringify would behave. Symbol and Function will be discarded.\n * @returns {Record[]}\n */ const serialize = (value, { json, lossy } = {})=>{\n    const _ = [];\n    return serializer(!(json || lossy), !!json, new Map, _)(value), _;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/types.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/types.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ARRAY: () => (/* binding */ ARRAY),\n/* harmony export */   BIGINT: () => (/* binding */ BIGINT),\n/* harmony export */   DATE: () => (/* binding */ DATE),\n/* harmony export */   ERROR: () => (/* binding */ ERROR),\n/* harmony export */   MAP: () => (/* binding */ MAP),\n/* harmony export */   OBJECT: () => (/* binding */ OBJECT),\n/* harmony export */   PRIMITIVE: () => (/* binding */ PRIMITIVE),\n/* harmony export */   REGEXP: () => (/* binding */ REGEXP),\n/* harmony export */   SET: () => (/* binding */ SET),\n/* harmony export */   VOID: () => (/* binding */ VOID)\n/* harmony export */ });\nconst VOID = -1;\nconst PRIMITIVE = 0;\nconst ARRAY = 1;\nconst OBJECT = 2;\nconst DATE = 3;\nconst REGEXP = 4;\nconst MAP = 5;\nconst SET = 6;\nconst ERROR = 7;\nconst BIGINT = 8; // export const SYMBOL = 9;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBTyxNQUFNQSxPQUFhLENBQUMsRUFBRTtBQUN0QixNQUFNQyxZQUFhLEVBQUU7QUFDckIsTUFBTUMsUUFBYSxFQUFFO0FBQ3JCLE1BQU1DLFNBQWEsRUFBRTtBQUNyQixNQUFNQyxPQUFhLEVBQUU7QUFDckIsTUFBTUMsU0FBYSxFQUFFO0FBQ3JCLE1BQU1DLE1BQWEsRUFBRTtBQUNyQixNQUFNQyxNQUFhLEVBQUU7QUFDckIsTUFBTUMsUUFBYSxFQUFFO0FBQ3JCLE1BQU1DLFNBQWEsRUFBRSxDQUM1QiwyQkFBMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zeXN0ZW0tZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL3R5cGVzLmpzPzVlOWUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFZPSUQgICAgICAgPSAtMTtcbmV4cG9ydCBjb25zdCBQUklNSVRJVkUgID0gMDtcbmV4cG9ydCBjb25zdCBBUlJBWSAgICAgID0gMTtcbmV4cG9ydCBjb25zdCBPQkpFQ1QgICAgID0gMjtcbmV4cG9ydCBjb25zdCBEQVRFICAgICAgID0gMztcbmV4cG9ydCBjb25zdCBSRUdFWFAgICAgID0gNDtcbmV4cG9ydCBjb25zdCBNQVAgICAgICAgID0gNTtcbmV4cG9ydCBjb25zdCBTRVQgICAgICAgID0gNjtcbmV4cG9ydCBjb25zdCBFUlJPUiAgICAgID0gNztcbmV4cG9ydCBjb25zdCBCSUdJTlQgICAgID0gODtcbi8vIGV4cG9ydCBjb25zdCBTWU1CT0wgPSA5O1xuIl0sIm5hbWVzIjpbIlZPSUQiLCJQUklNSVRJVkUiLCJBUlJBWSIsIk9CSkVDVCIsIkRBVEUiLCJSRUdFWFAiLCJNQVAiLCJTRVQiLCJFUlJPUiIsIkJJR0lOVCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\n");

/***/ })

};
;