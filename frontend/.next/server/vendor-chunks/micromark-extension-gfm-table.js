"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-table";
exports.ids = ["vendor-chunks/micromark-extension-gfm-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js":
/*!************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditMap: () => (/* binding */ EditMap)\n/* harmony export */ });\n/**\n * @import {Event} from 'micromark-util-types'\n */ // Port of `edit_map.rs` from `markdown-rs`.\n// This should move to `markdown-js` later.\n// Deal with several changes in events, batching them together.\n//\n// Preferably, changes should be kept to a minimum.\n// Sometimes, it’s needed to change the list of events, because parsing can be\n// messy, and it helps to expose a cleaner interface of events to the compiler\n// and other users.\n// It can also help to merge many adjacent similar events.\n// And, in other cases, it’s needed to parse subcontent: pass some events\n// through another tokenizer and inject the result.\n/**\n * @typedef {[number, number, Array<Event>]} Change\n * @typedef {[number, number, number]} Jump\n */ /**\n * Tracks a bunch of edits.\n */ class EditMap {\n    /**\n   * Create a new edit map.\n   */ constructor(){\n        /**\n     * Record of changes.\n     *\n     * @type {Array<Change>}\n     */ this.map = [];\n    }\n    /**\n   * Create an edit: a remove and/or add at a certain place.\n   *\n   * @param {number} index\n   * @param {number} remove\n   * @param {Array<Event>} add\n   * @returns {undefined}\n   */ add(index, remove, add) {\n        addImplementation(this, index, remove, add);\n    }\n    // To do: add this when moving to `micromark`.\n    // /**\n    //  * Create an edit: but insert `add` before existing additions.\n    //  *\n    //  * @param {number} index\n    //  * @param {number} remove\n    //  * @param {Array<Event>} add\n    //  * @returns {undefined}\n    //  */\n    // addBefore(index, remove, add) {\n    //   addImplementation(this, index, remove, add, true)\n    // }\n    /**\n   * Done, change the events.\n   *\n   * @param {Array<Event>} events\n   * @returns {undefined}\n   */ consume(events) {\n        this.map.sort(function(a, b) {\n            return a[0] - b[0];\n        });\n        /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */ if (this.map.length === 0) {\n            return;\n        }\n        // To do: if links are added in events, like they are in `markdown-rs`,\n        // this is needed.\n        // // Calculate jumps: where items in the current list move to.\n        // /** @type {Array<Jump>} */\n        // const jumps = []\n        // let index = 0\n        // let addAcc = 0\n        // let removeAcc = 0\n        // while (index < this.map.length) {\n        //   const [at, remove, add] = this.map[index]\n        //   removeAcc += remove\n        //   addAcc += add.length\n        //   jumps.push([at, removeAcc, addAcc])\n        //   index += 1\n        // }\n        //\n        // . shiftLinks(events, jumps)\n        let index = this.map.length;\n        /** @type {Array<Array<Event>>} */ const vecs = [];\n        while(index > 0){\n            index -= 1;\n            vecs.push(events.slice(this.map[index][0] + this.map[index][1]), this.map[index][2]);\n            // Truncate rest.\n            events.length = this.map[index][0];\n        }\n        vecs.push(events.slice());\n        events.length = 0;\n        let slice = vecs.pop();\n        while(slice){\n            for (const element of slice){\n                events.push(element);\n            }\n            slice = vecs.pop();\n        }\n        // Truncate everything.\n        this.map.length = 0;\n    }\n}\n/**\n * Create an edit.\n *\n * @param {EditMap} editMap\n * @param {number} at\n * @param {number} remove\n * @param {Array<Event>} add\n * @returns {undefined}\n */ function addImplementation(editMap, at, remove, add) {\n    let index = 0;\n    /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */ if (remove === 0 && add.length === 0) {\n        return;\n    }\n    while(index < editMap.map.length){\n        if (editMap.map[index][0] === at) {\n            editMap.map[index][1] += remove;\n            // To do: before not used by tables, use when moving to micromark.\n            // if (before) {\n            //   add.push(...editMap.map[index][2])\n            //   editMap.map[index][2] = add\n            // } else {\n            editMap.map[index][2].push(...add);\n            // }\n            return;\n        }\n        index += 1;\n    }\n    editMap.map.push([\n        at,\n        remove,\n        add\n    ]);\n} // /**\n //  * Shift `previous` and `next` links according to `jumps`.\n //  *\n //  * This fixes links in case there are events removed or added between them.\n //  *\n //  * @param {Array<Event>} events\n //  * @param {Array<Jump>} jumps\n //  */\n // function shiftLinks(events, jumps) {\n //   let jumpIndex = 0\n //   let index = 0\n //   let add = 0\n //   let rm = 0\n //   while (index < events.length) {\n //     const rmCurr = rm\n //     while (jumpIndex < jumps.length && jumps[jumpIndex][0] <= index) {\n //       add = jumps[jumpIndex][2]\n //       rm = jumps[jumpIndex][1]\n //       jumpIndex += 1\n //     }\n //     // Ignore items that will be removed.\n //     if (rm > rmCurr) {\n //       index += rm - rmCurr\n //     } else {\n //       // ?\n //       // if let Some(link) = &events[index].link {\n //       //     if let Some(next) = link.next {\n //       //         events[next].link.as_mut().unwrap().previous = Some(index + add - rm);\n //       //         while jumpIndex < jumps.len() && jumps[jumpIndex].0 <= next {\n //       //             add = jumps[jumpIndex].2;\n //       //             rm = jumps[jumpIndex].1;\n //       //             jumpIndex += 1;\n //       //         }\n //       //         events[index].link.as_mut().unwrap().next = Some(next + add - rm);\n //       //         index = next;\n //       //         continue;\n //       //     }\n //       // }\n //       index += 1\n //     }\n //   }\n // }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/html.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableHtml: () => (/* binding */ gfmTableHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */ \nconst alignment = {\n    none: \"\",\n    left: ' align=\"left\"',\n    right: ' align=\"right\"',\n    center: ' align=\"center\"'\n};\n// To do: micromark@5: use `infer` here, when all events are exposed.\n/**\n * Create an HTML extension for `micromark` to support GitHub tables when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub tables when serializing to HTML.\n */ function gfmTableHtml() {\n    return {\n        enter: {\n            table (token) {\n                const tableAlign = token._align;\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `_align`\");\n                this.lineEndingIfNeeded();\n                this.tag(\"<table>\");\n                this.setData(\"tableAlign\", tableAlign);\n            },\n            tableBody () {\n                this.tag(\"<tbody>\");\n            },\n            tableData () {\n                const tableAlign = this.getData(\"tableAlign\");\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                const align = alignment[tableAlign[tableColumn]];\n                if (align === undefined) {\n                    // Capture results to ignore them.\n                    this.buffer();\n                } else {\n                    this.lineEndingIfNeeded();\n                    this.tag(\"<td\" + align + \">\");\n                }\n            },\n            tableHead () {\n                this.lineEndingIfNeeded();\n                this.tag(\"<thead>\");\n            },\n            tableHeader () {\n                const tableAlign = this.getData(\"tableAlign\");\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                const align = alignment[tableAlign[tableColumn]];\n                this.lineEndingIfNeeded();\n                this.tag(\"<th\" + align + \">\");\n            },\n            tableRow () {\n                this.setData(\"tableColumn\", 0);\n                this.lineEndingIfNeeded();\n                this.tag(\"<tr>\");\n            }\n        },\n        exit: {\n            // Overwrite the default code text data handler to unescape escaped pipes when\n            // they are in tables.\n            codeTextData (token) {\n                let value = this.sliceSerialize(token);\n                if (this.getData(\"tableAlign\")) {\n                    value = value.replace(/\\\\([\\\\|])/g, replace);\n                }\n                this.raw(this.encode(value));\n            },\n            table () {\n                this.setData(\"tableAlign\");\n                // Note: we don’t set `slurpAllLineEndings` anymore, in delimiter rows,\n                // but we do need to reset it to match a funky newline GH generates for\n                // list items combined with tables.\n                this.setData(\"slurpAllLineEndings\");\n                this.lineEndingIfNeeded();\n                this.tag(\"</table>\");\n            },\n            tableBody () {\n                this.lineEndingIfNeeded();\n                this.tag(\"</tbody>\");\n            },\n            tableData () {\n                const tableAlign = this.getData(\"tableAlign\");\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                if (tableColumn in tableAlign) {\n                    this.tag(\"</td>\");\n                    this.setData(\"tableColumn\", tableColumn + 1);\n                } else {\n                    // Stop capturing.\n                    this.resume();\n                }\n            },\n            tableHead () {\n                this.lineEndingIfNeeded();\n                this.tag(\"</thead>\");\n            },\n            tableHeader () {\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                this.tag(\"</th>\");\n                this.setData(\"tableColumn\", tableColumn + 1);\n            },\n            tableRow () {\n                const tableAlign = this.getData(\"tableAlign\");\n                let tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                while(tableColumn < tableAlign.length){\n                    this.lineEndingIfNeeded();\n                    this.tag(\"<td\" + alignment[tableAlign[tableColumn]] + \"></td>\");\n                    tableColumn++;\n                }\n                this.setData(\"tableColumn\", tableColumn);\n                this.lineEndingIfNeeded();\n                this.tag(\"</tr>\");\n            }\n        }\n    };\n}\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */ function replace($0, $1) {\n    // Pipes work, backslashes don’t (but can’t escape pipes).\n    return $1 === \"|\" ? $1 : $0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/infer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableAlign: () => (/* binding */ gfmTableAlign)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */ /**\n * @typedef {'center' | 'left' | 'none' | 'right'} Align\n */ \n/**\n * Figure out the alignment of a GFM table.\n *\n * @param {Readonly<Array<Event>>} events\n *   List of events.\n * @param {number} index\n *   Table enter event.\n * @returns {Array<Align>}\n *   List of aligns.\n */ function gfmTableAlign(events, index) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(events[index][1].type === \"table\", \"expected table\");\n    let inDelimiterRow = false;\n    /** @type {Array<Align>} */ const align = [];\n    while(index < events.length){\n        const event = events[index];\n        if (inDelimiterRow) {\n            if (event[0] === \"enter\") {\n                // Start of alignment value: set a new column.\n                // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n                if (event[1].type === \"tableContent\") {\n                    align.push(events[index + 1][1].type === \"tableDelimiterMarker\" ? \"left\" : \"none\");\n                }\n            } else if (event[1].type === \"tableContent\") {\n                if (events[index - 1][1].type === \"tableDelimiterMarker\") {\n                    const alignIndex = align.length - 1;\n                    align[alignIndex] = align[alignIndex] === \"left\" ? \"center\" : \"right\";\n                }\n            } else if (event[1].type === \"tableDelimiterRow\") {\n                break;\n            }\n        } else if (event[0] === \"enter\" && event[1].type === \"tableDelimiterRow\") {\n            inDelimiterRow = true;\n        }\n        index += 1;\n    }\n    return align;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTable: () => (/* binding */ gfmTable)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var _edit_map_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./edit-map.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\");\n/* harmony import */ var _infer_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infer.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\");\n/**\n * @import {Event, Extension, Point, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ /**\n * @typedef {[number, number, number, number]} Range\n *   Cell info.\n *\n * @typedef {0 | 1 | 2 | 3} RowKind\n *   Where we are: `1` for head row, `2` for delimiter row, `3` for body row.\n */ \n\n\n\n\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   table syntax.\n */ function gfmTable() {\n    return {\n        flow: {\n            null: {\n                name: \"table\",\n                tokenize: tokenizeTable,\n                resolveAll: resolveTable\n            }\n        }\n    };\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeTable(effects, ok, nok) {\n    const self = this;\n    let size = 0;\n    let sizeB = 0;\n    /** @type {boolean | undefined} */ let seen;\n    return start;\n    /**\n   * Start of a GFM table.\n   *\n   * If there is a valid table row or table head before, then we try to parse\n   * another row.\n   * Otherwise, we try to parse a head.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   * @type {State}\n   */ function start(code) {\n        let index = self.events.length - 1;\n        while(index > -1){\n            const type = self.events[index][1].type;\n            if (type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding || // Note: markdown-rs uses `whitespace` instead of `linePrefix`\n            type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix) index--;\n            else break;\n        }\n        const tail = index > -1 ? self.events[index][1].type : null;\n        const next = tail === \"tableHead\" || tail === \"tableRow\" ? bodyRowStart : headRowBefore;\n        // Don’t allow lazy body rows.\n        if (next === bodyRowStart && self.parser.lazy[self.now().line]) {\n            return nok(code);\n        }\n        return next(code);\n    }\n    /**\n   * Before table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowBefore(code) {\n        effects.enter(\"tableHead\");\n        effects.enter(\"tableRow\");\n        return headRowStart(code);\n    }\n    /**\n   * Before table head row, after whitespace.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowStart(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            return headRowBreak(code);\n        }\n        // To do: micromark-js should let us parse our own whitespace in extensions,\n        // like `markdown-rs`:\n        //\n        // ```js\n        // // 4+ spaces.\n        // if (markdownSpace(code)) {\n        //   return nok(code)\n        // }\n        // ```\n        seen = true;\n        // Count the first character, that isn’t a pipe, double.\n        sizeB += 1;\n        return headRowBreak(code);\n    }\n    /**\n   * At break in table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *       ^\n   *         ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowBreak(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n            return nok(code);\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            // If anything other than one pipe (ignoring whitespace) was used, it’s fine.\n            if (sizeB > 1) {\n                sizeB = 0;\n                // To do: check if this works.\n                // Feel free to interrupt:\n                self.interrupt = true;\n                effects.exit(\"tableRow\");\n                effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding);\n                effects.consume(code);\n                effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding);\n                return headDelimiterStart;\n            }\n            // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n            return nok(code);\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            // To do: check if this is fine.\n            // effects.attempt(State::Next(StateName::GfmTableHeadRowBreak), State::Nok)\n            // State::Retry(space_or_tab(tokenizer))\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        sizeB += 1;\n        if (seen) {\n            seen = false;\n            // Header cell count.\n            size += 1;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.enter(\"tableCellDivider\");\n            effects.consume(code);\n            effects.exit(\"tableCellDivider\");\n            // Whether a delimiter was seen.\n            seen = true;\n            return headRowBreak;\n        }\n        // Anything else is cell data.\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n        return headRowData(code);\n    }\n    /**\n   * In table head row data.\n   *\n   * ```markdown\n   * > | | a |\n   *       ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowData(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n            return headRowBreak(code);\n        }\n        effects.consume(code);\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? headRowEscape : headRowData;\n    }\n    /**\n   * In table head row escape.\n   *\n   * ```markdown\n   * > | | a\\-b |\n   *         ^\n   *   | | ---- |\n   *   | | c    |\n   * ```\n   *\n   * @type {State}\n   */ function headRowEscape(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.consume(code);\n            return headRowData;\n        }\n        return headRowData(code);\n    }\n    /**\n   * Before delimiter row.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterStart(code) {\n        // Reset `interrupt`.\n        self.interrupt = false;\n        // Note: in `markdown-rs`, we need to handle piercing here too.\n        if (self.parser.lazy[self.now().line]) {\n            return nok(code);\n        }\n        effects.enter(\"tableDelimiterRow\");\n        // Track if we’ve seen a `:` or `|`.\n        seen = false;\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(self.parser.constructs.disable.null, \"expected `disabled.null`\");\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headDelimiterBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix, self.parser.constructs.disable.null.includes(\"codeIndented\") ? undefined : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize)(code);\n        }\n        return headDelimiterBefore(code);\n    }\n    /**\n   * Before delimiter row, after optional whitespace.\n   *\n   * Reused when a `|` is found later, to parse another cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterBefore(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n            return headDelimiterValueBefore(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            seen = true;\n            // If we start with a pipe, we open a cell marker.\n            effects.enter(\"tableCellDivider\");\n            effects.consume(code);\n            effects.exit(\"tableCellDivider\");\n            return headDelimiterCellBefore;\n        }\n        // More whitespace / empty row not allowed at start.\n        return headDelimiterNok(code);\n    }\n    /**\n   * After `|`, before delimiter cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterCellBefore(code) {\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headDelimiterValueBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        return headDelimiterValueBefore(code);\n    }\n    /**\n   * Before delimiter cell value.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterValueBefore(code) {\n        // Align: left.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n            sizeB += 1;\n            seen = true;\n            effects.enter(\"tableDelimiterMarker\");\n            effects.consume(code);\n            effects.exit(\"tableDelimiterMarker\");\n            return headDelimiterLeftAlignmentAfter;\n        }\n        // Align: none.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n            sizeB += 1;\n            // To do: seems weird that this *isn’t* left aligned, but that state is used?\n            return headDelimiterLeftAlignmentAfter(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            return headDelimiterCellAfter(code);\n        }\n        return headDelimiterNok(code);\n    }\n    /**\n   * After delimiter cell left alignment marker.\n   *\n   * ```markdown\n   *   | | a  |\n   * > | | :- |\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterLeftAlignmentAfter(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n            effects.enter(\"tableDelimiterFiller\");\n            return headDelimiterFiller(code);\n        }\n        // Anything else is not ok after the left-align colon.\n        return headDelimiterNok(code);\n    }\n    /**\n   * In delimiter cell filler.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterFiller(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n            effects.consume(code);\n            return headDelimiterFiller;\n        }\n        // Align is `center` if it was `left`, `right` otherwise.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n            seen = true;\n            effects.exit(\"tableDelimiterFiller\");\n            effects.enter(\"tableDelimiterMarker\");\n            effects.consume(code);\n            effects.exit(\"tableDelimiterMarker\");\n            return headDelimiterRightAlignmentAfter;\n        }\n        effects.exit(\"tableDelimiterFiller\");\n        return headDelimiterRightAlignmentAfter(code);\n    }\n    /**\n   * After delimiter cell right alignment marker.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterRightAlignmentAfter(code) {\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headDelimiterCellAfter, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        return headDelimiterCellAfter(code);\n    }\n    /**\n   * After delimiter cell.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterCellAfter(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            return headDelimiterBefore(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            // Exit when:\n            // * there was no `:` or `|` at all (it’s a thematic break or setext\n            //   underline instead)\n            // * the header cell count is not the delimiter cell count\n            if (!seen || size !== sizeB) {\n                return headDelimiterNok(code);\n            }\n            // Note: in markdown-rs`, a reset is needed here.\n            effects.exit(\"tableDelimiterRow\");\n            effects.exit(\"tableHead\");\n            // To do: in `markdown-rs`, resolvers need to be registered manually.\n            // effects.register_resolver(ResolveName::GfmTable)\n            return ok(code);\n        }\n        return headDelimiterNok(code);\n    }\n    /**\n   * In delimiter row, at a disallowed byte.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | x |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterNok(code) {\n        // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n        return nok(code);\n    }\n    /**\n   * Before table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowStart(code) {\n        // Note: in `markdown-rs` we need to manually take care of a prefix,\n        // but in `micromark-js` that is done for us, so if we’re here, we’re\n        // never at whitespace.\n        effects.enter(\"tableRow\");\n        return bodyRowBreak(code);\n    }\n    /**\n   * At break in table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   *       ^\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowBreak(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.enter(\"tableCellDivider\");\n            effects.consume(code);\n            effects.exit(\"tableCellDivider\");\n            return bodyRowBreak;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            effects.exit(\"tableRow\");\n            return ok(code);\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, bodyRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        // Anything else is cell content.\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n        return bodyRowData(code);\n    }\n    /**\n   * In table body row data.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowData(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n            return bodyRowBreak(code);\n        }\n        effects.consume(code);\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? bodyRowEscape : bodyRowData;\n    }\n    /**\n   * In table body row escape.\n   *\n   * ```markdown\n   *   | | a    |\n   *   | | ---- |\n   * > | | b\\-c |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowEscape(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.consume(code);\n            return bodyRowData;\n        }\n        return bodyRowData(code);\n    }\n}\n/** @type {Resolver} */ function resolveTable(events, context) {\n    let index = -1;\n    let inFirstCellAwaitingPipe = true;\n    /** @type {RowKind} */ let rowKind = 0;\n    /** @type {Range} */ let lastCell = [\n        0,\n        0,\n        0,\n        0\n    ];\n    /** @type {Range} */ let cell = [\n        0,\n        0,\n        0,\n        0\n    ];\n    let afterHeadAwaitingFirstBodyRow = false;\n    let lastTableEnd = 0;\n    /** @type {Token | undefined} */ let currentTable;\n    /** @type {Token | undefined} */ let currentBody;\n    /** @type {Token | undefined} */ let currentCell;\n    const map = new _edit_map_js__WEBPACK_IMPORTED_MODULE_6__.EditMap();\n    while(++index < events.length){\n        const event = events[index];\n        const token = event[1];\n        if (event[0] === \"enter\") {\n            // Start of head.\n            if (token.type === \"tableHead\") {\n                afterHeadAwaitingFirstBodyRow = false;\n                // Inject previous (body end and) table end.\n                if (lastTableEnd !== 0) {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, \"there should be a table opening\");\n                    flushTableEnd(map, context, lastTableEnd, currentTable, currentBody);\n                    currentBody = undefined;\n                    lastTableEnd = 0;\n                }\n                // Inject table start.\n                currentTable = {\n                    type: \"table\",\n                    start: Object.assign({}, token.start),\n                    // Note: correct end is set later.\n                    end: Object.assign({}, token.end)\n                };\n                map.add(index, 0, [\n                    [\n                        \"enter\",\n                        currentTable,\n                        context\n                    ]\n                ]);\n            } else if (token.type === \"tableRow\" || token.type === \"tableDelimiterRow\") {\n                inFirstCellAwaitingPipe = true;\n                currentCell = undefined;\n                lastCell = [\n                    0,\n                    0,\n                    0,\n                    0\n                ];\n                cell = [\n                    0,\n                    index + 1,\n                    0,\n                    0\n                ];\n                // Inject table body start.\n                if (afterHeadAwaitingFirstBodyRow) {\n                    afterHeadAwaitingFirstBodyRow = false;\n                    currentBody = {\n                        type: \"tableBody\",\n                        start: Object.assign({}, token.start),\n                        // Note: correct end is set later.\n                        end: Object.assign({}, token.end)\n                    };\n                    map.add(index, 0, [\n                        [\n                            \"enter\",\n                            currentBody,\n                            context\n                        ]\n                    ]);\n                }\n                rowKind = token.type === \"tableDelimiterRow\" ? 2 : currentBody ? 3 : 1;\n            } else if (rowKind && (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data || token.type === \"tableDelimiterMarker\" || token.type === \"tableDelimiterFiller\")) {\n                inFirstCellAwaitingPipe = false;\n                // First value in cell.\n                if (cell[2] === 0) {\n                    if (lastCell[1] !== 0) {\n                        cell[0] = cell[1];\n                        currentCell = flushCell(map, context, lastCell, rowKind, undefined, currentCell);\n                        lastCell = [\n                            0,\n                            0,\n                            0,\n                            0\n                        ];\n                    }\n                    cell[2] = index;\n                }\n            } else if (token.type === \"tableCellDivider\") {\n                if (inFirstCellAwaitingPipe) {\n                    inFirstCellAwaitingPipe = false;\n                } else {\n                    if (lastCell[1] !== 0) {\n                        cell[0] = cell[1];\n                        currentCell = flushCell(map, context, lastCell, rowKind, undefined, currentCell);\n                    }\n                    lastCell = cell;\n                    cell = [\n                        lastCell[1],\n                        index,\n                        0,\n                        0\n                    ];\n                }\n            }\n        } else if (token.type === \"tableHead\") {\n            afterHeadAwaitingFirstBodyRow = true;\n            lastTableEnd = index;\n        } else if (token.type === \"tableRow\" || token.type === \"tableDelimiterRow\") {\n            lastTableEnd = index;\n            if (lastCell[1] !== 0) {\n                cell[0] = cell[1];\n                currentCell = flushCell(map, context, lastCell, rowKind, index, currentCell);\n            } else if (cell[1] !== 0) {\n                currentCell = flushCell(map, context, cell, rowKind, index, currentCell);\n            }\n            rowKind = 0;\n        } else if (rowKind && (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data || token.type === \"tableDelimiterMarker\" || token.type === \"tableDelimiterFiller\")) {\n            cell[3] = index;\n        }\n    }\n    if (lastTableEnd !== 0) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, \"expected table opening\");\n        flushTableEnd(map, context, lastTableEnd, currentTable, currentBody);\n    }\n    map.consume(context.events);\n    // To do: move this into `html`, when events are exposed there.\n    // That’s what `markdown-rs` does.\n    // That needs updates to `mdast-util-gfm-table`.\n    index = -1;\n    while(++index < context.events.length){\n        const event = context.events[index];\n        if (event[0] === \"enter\" && event[1].type === \"table\") {\n            event[1]._align = (0,_infer_js__WEBPACK_IMPORTED_MODULE_7__.gfmTableAlign)(context.events, index);\n        }\n    }\n    return events;\n}\n/**\n * Generate a cell.\n *\n * @param {EditMap} map\n * @param {Readonly<TokenizeContext>} context\n * @param {Readonly<Range>} range\n * @param {RowKind} rowKind\n * @param {number | undefined} rowEnd\n * @param {Token | undefined} previousCell\n * @returns {Token | undefined}\n */ // eslint-disable-next-line max-params\nfunction flushCell(map, context, range, rowKind, rowEnd, previousCell) {\n    // `markdown-rs` uses:\n    // rowKind === 2 ? 'tableDelimiterCell' : 'tableCell'\n    const groupName = rowKind === 1 ? \"tableHeader\" : rowKind === 2 ? \"tableDelimiter\" : \"tableData\";\n    // `markdown-rs` uses:\n    // rowKind === 2 ? 'tableDelimiterCellValue' : 'tableCellText'\n    const valueName = \"tableContent\";\n    // Insert an exit for the previous cell, if there is one.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //          ^-- exit\n    //           ^^^^-- this cell\n    // ```\n    if (range[0] !== 0) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(previousCell, \"expected previous cell enter\");\n        previousCell.end = Object.assign({}, getPoint(context.events, range[0]));\n        map.add(range[0], 0, [\n            [\n                \"exit\",\n                previousCell,\n                context\n            ]\n        ]);\n    }\n    // Insert enter of this cell.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //           ^-- enter\n    //           ^^^^-- this cell\n    // ```\n    const now = getPoint(context.events, range[1]);\n    previousCell = {\n        type: groupName,\n        start: Object.assign({}, now),\n        // Note: correct end is set later.\n        end: Object.assign({}, now)\n    };\n    map.add(range[1], 0, [\n        [\n            \"enter\",\n            previousCell,\n            context\n        ]\n    ]);\n    // Insert text start at first data start and end at last data end, and\n    // remove events between.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //            ^-- enter\n    //             ^-- exit\n    //           ^^^^-- this cell\n    // ```\n    if (range[2] !== 0) {\n        const relatedStart = getPoint(context.events, range[2]);\n        const relatedEnd = getPoint(context.events, range[3]);\n        /** @type {Token} */ const valueToken = {\n            type: valueName,\n            start: Object.assign({}, relatedStart),\n            end: Object.assign({}, relatedEnd)\n        };\n        map.add(range[2], 0, [\n            [\n                \"enter\",\n                valueToken,\n                context\n            ]\n        ]);\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(range[3] !== 0);\n        if (rowKind !== 2) {\n            // Fix positional info on remaining events\n            const start = context.events[range[2]];\n            const end = context.events[range[3]];\n            start[1].end = Object.assign({}, end[1].end);\n            start[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkText;\n            start[1].contentType = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText;\n            // Remove if needed.\n            if (range[3] > range[2] + 1) {\n                const a = range[2] + 1;\n                const b = range[3] - range[2] - 1;\n                map.add(a, b, []);\n            }\n        }\n        map.add(range[3] + 1, 0, [\n            [\n                \"exit\",\n                valueToken,\n                context\n            ]\n        ]);\n    }\n    // Insert an exit for the last cell, if at the row end.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //                    ^-- exit\n    //               ^^^^^^-- this cell (the last one contains two “between” parts)\n    // ```\n    if (rowEnd !== undefined) {\n        previousCell.end = Object.assign({}, getPoint(context.events, rowEnd));\n        map.add(rowEnd, 0, [\n            [\n                \"exit\",\n                previousCell,\n                context\n            ]\n        ]);\n        previousCell = undefined;\n    }\n    return previousCell;\n}\n/**\n * Generate table end (and table body end).\n *\n * @param {Readonly<EditMap>} map\n * @param {Readonly<TokenizeContext>} context\n * @param {number} index\n * @param {Token} table\n * @param {Token | undefined} tableBody\n */ // eslint-disable-next-line max-params\nfunction flushTableEnd(map, context, index, table, tableBody) {\n    /** @type {Array<Event>} */ const exits = [];\n    const related = getPoint(context.events, index);\n    if (tableBody) {\n        tableBody.end = Object.assign({}, related);\n        exits.push([\n            \"exit\",\n            tableBody,\n            context\n        ]);\n    }\n    table.end = Object.assign({}, related);\n    exits.push([\n        \"exit\",\n        table,\n        context\n    ]);\n    map.add(index + 1, 0, exits);\n}\n/**\n * @param {Readonly<Array<Event>>} events\n * @param {number} index\n * @returns {Readonly<Point>}\n */ function getPoint(events, index) {\n    const event = events[index];\n    const side = event[0] === \"enter\" ? \"start\" : \"end\";\n    return event[1][side];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js\n");

/***/ })

};
;