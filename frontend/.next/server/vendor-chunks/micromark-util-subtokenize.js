"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-subtokenize";
exports.ids = ["vendor-chunks/micromark-util-subtokenize"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark-util-subtokenize/dev/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* reexport safe */ _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer),\n/* harmony export */   subtokenize: () => (/* binding */ subtokenize)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/splice-buffer.js */ \"(ssr)/./node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\");\n/**\n * @import {Chunk, Event, Token} from 'micromark-util-types'\n */ \n\n\n\n// Hidden API exposed for testing.\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} eventsArray\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */ // eslint-disable-next-line complexity\nfunction subtokenize(eventsArray) {\n    /** @type {Record<string, number>} */ const jumps = {};\n    let index = -1;\n    /** @type {Event} */ let event;\n    /** @type {number | undefined} */ let lineIndex;\n    /** @type {number} */ let otherIndex;\n    /** @type {Event} */ let otherEvent;\n    /** @type {Array<Event>} */ let parameters;\n    /** @type {Array<Event>} */ let subevents;\n    /** @type {boolean | undefined} */ let more;\n    const events = new _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer(eventsArray);\n    while(++index < events.length){\n        while(index in jumps){\n            index = jumps[index];\n        }\n        event = events.get(index);\n        // Add a hook for the GFM tasklist extension, which needs to know if text\n        // is in the first content of a list item.\n        if (index && event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow && events.get(index - 1)[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefix) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(event[1]._tokenizer, \"expected `_tokenizer` on subtokens\");\n            subevents = event[1]._tokenizer.events;\n            otherIndex = 0;\n            if (otherIndex < subevents.length && subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank) {\n                otherIndex += 2;\n            }\n            if (otherIndex < subevents.length && subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n                while(++otherIndex < subevents.length){\n                    if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n                        break;\n                    }\n                    if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkText) {\n                        subevents[otherIndex][1]._isInFirstContentOfListItem = true;\n                        otherIndex++;\n                    }\n                }\n            }\n        }\n        // Enter.\n        if (event[0] === \"enter\") {\n            if (event[1].contentType) {\n                Object.assign(jumps, subcontent(events, index));\n                index = jumps[index];\n                more = true;\n            }\n        } else if (event[1]._container) {\n            otherIndex = index;\n            lineIndex = undefined;\n            while(otherIndex--){\n                otherEvent = events.get(otherIndex);\n                if (otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding || otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank) {\n                    if (otherEvent[0] === \"enter\") {\n                        if (lineIndex) {\n                            events.get(lineIndex)[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank;\n                        }\n                        otherEvent[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding;\n                        lineIndex = otherIndex;\n                    }\n                } else if (otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix || otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.listItemIndent) {\n                // Move past.\n                } else {\n                    break;\n                }\n            }\n            if (lineIndex) {\n                // Fix position.\n                event[1].end = {\n                    ...events.get(lineIndex)[1].start\n                };\n                // Switch container exit w/ line endings.\n                parameters = events.slice(lineIndex, index);\n                parameters.unshift(event);\n                events.splice(lineIndex, index - lineIndex + 1, parameters);\n            }\n        }\n    }\n    // The changes to the `events` buffer must be copied back into the eventsArray\n    (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__.splice)(eventsArray, 0, Number.POSITIVE_INFINITY, events.slice(0));\n    return !more;\n}\n/**\n * Tokenize embedded tokens.\n *\n * @param {SpliceBuffer<Event>} events\n *   Events.\n * @param {number} eventIndex\n *   Index.\n * @returns {Record<string, number>}\n *   Gaps.\n */ function subcontent(events, eventIndex) {\n    const token = events.get(eventIndex)[1];\n    const context = events.get(eventIndex)[2];\n    let startPosition = eventIndex - 1;\n    /** @type {Array<number>} */ const startPositions = [];\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(token.contentType, \"expected `contentType` on subtokens\");\n    let tokenizer = token._tokenizer;\n    if (!tokenizer) {\n        tokenizer = context.parser[token.contentType](token.start);\n        if (token._contentTypeTextTrailing) {\n            tokenizer._contentTypeTextTrailing = true;\n        }\n    }\n    const childEvents = tokenizer.events;\n    /** @type {Array<[number, number]>} */ const jumps = [];\n    /** @type {Record<string, number>} */ const gaps = {};\n    /** @type {Array<Chunk>} */ let stream;\n    /** @type {Token | undefined} */ let previous;\n    let index = -1;\n    /** @type {Token | undefined} */ let current = token;\n    let adjust = 0;\n    let start = 0;\n    const breaks = [\n        start\n    ];\n    // Loop forward through the linked tokens to pass them in order to the\n    // subtokenizer.\n    while(current){\n        // Find the position of the event for this token.\n        while(events.get(++startPosition)[1] !== current){\n        // Empty.\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!previous || current.previous === previous, \"expected previous to match\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!previous || previous.next === current, \"expected next to match\");\n        startPositions.push(startPosition);\n        if (!current._tokenizer) {\n            stream = context.sliceStream(current);\n            if (!current.next) {\n                stream.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof);\n            }\n            if (previous) {\n                tokenizer.defineSkip(current.start);\n            }\n            if (current._isInFirstContentOfListItem) {\n                tokenizer._gfmTasklistFirstContentOfListItem = true;\n            }\n            tokenizer.write(stream);\n            if (current._isInFirstContentOfListItem) {\n                tokenizer._gfmTasklistFirstContentOfListItem = undefined;\n            }\n        }\n        // Unravel the next token.\n        previous = current;\n        current = current.next;\n    }\n    // Now, loop back through all events (and linked tokens), to figure out which\n    // parts belong where.\n    current = token;\n    while(++index < childEvents.length){\n        if (// Find a void token that includes a break.\n        childEvents[index][0] === \"exit\" && childEvents[index - 1][0] === \"enter\" && childEvents[index][1].type === childEvents[index - 1][1].type && childEvents[index][1].start.line !== childEvents[index][1].end.line) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(current, \"expected a current token\");\n            start = index + 1;\n            breaks.push(start);\n            // Help GC.\n            current._tokenizer = undefined;\n            current.previous = undefined;\n            current = current.next;\n        }\n    }\n    // Help GC.\n    tokenizer.events = [];\n    // If there’s one more token (which is the cases for lines that end in an\n    // EOF), that’s perfect: the last point we found starts it.\n    // If there isn’t then make sure any remaining content is added to it.\n    if (current) {\n        // Help GC.\n        current._tokenizer = undefined;\n        current.previous = undefined;\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!current.next, \"expected no next token\");\n    } else {\n        breaks.pop();\n    }\n    // Now splice the events from the subtokenizer into the current events,\n    // moving back to front so that splice indices aren’t affected.\n    index = breaks.length;\n    while(index--){\n        const slice = childEvents.slice(breaks[index], breaks[index + 1]);\n        const start = startPositions.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(start !== undefined, \"expected a start position when splicing\");\n        jumps.push([\n            start,\n            start + slice.length - 1\n        ]);\n        events.splice(start, 2, slice);\n    }\n    jumps.reverse();\n    index = -1;\n    while(++index < jumps.length){\n        gaps[adjust + jumps[index][0]] = adjust + jumps[index][1];\n        adjust += jumps[index][1] - jumps[index][0] - 1;\n    }\n    return gaps;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js":
/*!**************************************************************************!*\
  !*** ./node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* binding */ SpliceBuffer)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n\n/**\n * Some of the internal operations of micromark do lots of editing\n * operations on very large arrays. This runs into problems with two\n * properties of most circa-2020 JavaScript interpreters:\n *\n *  - Array-length modifications at the high end of an array (push/pop) are\n *    expected to be common and are implemented in (amortized) time\n *    proportional to the number of elements added or removed, whereas\n *    other operations (shift/unshift and splice) are much less efficient.\n *  - Function arguments are passed on the stack, so adding tens of thousands\n *    of elements to an array with `arr.push(...newElements)` will frequently\n *    cause stack overflows. (see <https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why>)\n *\n * SpliceBuffers are an implementation of gap buffers, which are a\n * generalization of the \"queue made of two stacks\" idea. The splice buffer\n * maintains a cursor, and moving the cursor has cost proportional to the\n * distance the cursor moves, but inserting, deleting, or splicing in\n * new information at the cursor is as efficient as the push/pop operation.\n * This allows for an efficient sequence of splices (or pushes, pops, shifts,\n * or unshifts) as long such edits happen at the same part of the array or\n * generally sweep through the array from the beginning to the end.\n *\n * The interface for splice buffers also supports large numbers of inputs by\n * passing a single array argument rather passing multiple arguments on the\n * function call stack.\n *\n * @template T\n *   Item type.\n */ class SpliceBuffer {\n    /**\n   * @param {ReadonlyArray<T> | null | undefined} [initial]\n   *   Initial items (optional).\n   * @returns\n   *   Splice buffer.\n   */ constructor(initial){\n        /** @type {Array<T>} */ this.left = initial ? [\n            ...initial\n        ] : [];\n        /** @type {Array<T>} */ this.right = [];\n    }\n    /**\n   * Array access;\n   * does not move the cursor.\n   *\n   * @param {number} index\n   *   Index.\n   * @return {T}\n   *   Item.\n   */ get(index) {\n        if (index < 0 || index >= this.left.length + this.right.length) {\n            throw new RangeError(\"Cannot access index `\" + index + \"` in a splice buffer of size `\" + (this.left.length + this.right.length) + \"`\");\n        }\n        if (index < this.left.length) return this.left[index];\n        return this.right[this.right.length - index + this.left.length - 1];\n    }\n    /**\n   * The length of the splice buffer, one greater than the largest index in the\n   * array.\n   */ get length() {\n        return this.left.length + this.right.length;\n    }\n    /**\n   * Remove and return `list[0]`;\n   * moves the cursor to `0`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */ shift() {\n        this.setCursor(0);\n        return this.right.pop();\n    }\n    /**\n   * Slice the buffer to get an array;\n   * does not move the cursor.\n   *\n   * @param {number} start\n   *   Start.\n   * @param {number | null | undefined} [end]\n   *   End (optional).\n   * @returns {Array<T>}\n   *   Array of items.\n   */ slice(start, end) {\n        /** @type {number} */ const stop = end === null || end === undefined ? Number.POSITIVE_INFINITY : end;\n        if (stop < this.left.length) {\n            return this.left.slice(start, stop);\n        }\n        if (start > this.left.length) {\n            return this.right.slice(this.right.length - stop + this.left.length, this.right.length - start + this.left.length).reverse();\n        }\n        return this.left.slice(start).concat(this.right.slice(this.right.length - stop + this.left.length).reverse());\n    }\n    /**\n   * Mimics the behavior of Array.prototype.splice() except for the change of\n   * interface necessary to avoid segfaults when patching in very large arrays.\n   *\n   * This operation moves cursor is moved to `start` and results in the cursor\n   * placed after any inserted items.\n   *\n   * @param {number} start\n   *   Start;\n   *   zero-based index at which to start changing the array;\n   *   negative numbers count backwards from the end of the array and values\n   *   that are out-of bounds are clamped to the appropriate end of the array.\n   * @param {number | null | undefined} [deleteCount=0]\n   *   Delete count (default: `0`);\n   *   maximum number of elements to delete, starting from start.\n   * @param {Array<T> | null | undefined} [items=[]]\n   *   Items to include in place of the deleted items (default: `[]`).\n   * @return {Array<T>}\n   *   Any removed items.\n   */ splice(start, deleteCount, items) {\n        /** @type {number} */ const count = deleteCount || 0;\n        this.setCursor(Math.trunc(start));\n        const removed = this.right.splice(this.right.length - count, Number.POSITIVE_INFINITY);\n        if (items) chunkedPush(this.left, items);\n        return removed.reverse();\n    }\n    /**\n   * Remove and return the highest-numbered item in the array, so\n   * `list[list.length - 1]`;\n   * Moves the cursor to `length`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */ pop() {\n        this.setCursor(Number.POSITIVE_INFINITY);\n        return this.left.pop();\n    }\n    /**\n   * Inserts a single item to the high-numbered side of the array;\n   * moves the cursor to `length`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */ push(item) {\n        this.setCursor(Number.POSITIVE_INFINITY);\n        this.left.push(item);\n    }\n    /**\n   * Inserts many items to the high-numbered side of the array.\n   * Moves the cursor to `length`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */ pushMany(items) {\n        this.setCursor(Number.POSITIVE_INFINITY);\n        chunkedPush(this.left, items);\n    }\n    /**\n   * Inserts a single item to the low-numbered side of the array;\n   * Moves the cursor to `0`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */ unshift(item) {\n        this.setCursor(0);\n        this.right.push(item);\n    }\n    /**\n   * Inserts many items to the low-numbered side of the array;\n   * moves the cursor to `0`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */ unshiftMany(items) {\n        this.setCursor(0);\n        chunkedPush(this.right, items.reverse());\n    }\n    /**\n   * Move the cursor to a specific position in the array. Requires\n   * time proportional to the distance moved.\n   *\n   * If `n < 0`, the cursor will end up at the beginning.\n   * If `n > length`, the cursor will end up at the end.\n   *\n   * @param {number} n\n   *   Position.\n   * @return {undefined}\n   *   Nothing.\n   */ setCursor(n) {\n        if (n === this.left.length || n > this.left.length && this.right.length === 0 || n < 0 && this.left.length === 0) return;\n        if (n < this.left.length) {\n            // Move cursor to the this.left\n            const removed = this.left.splice(n, Number.POSITIVE_INFINITY);\n            chunkedPush(this.right, removed.reverse());\n        } else {\n            // Move cursor to the this.right\n            const removed = this.right.splice(this.left.length + this.right.length - n, Number.POSITIVE_INFINITY);\n            chunkedPush(this.left, removed.reverse());\n        }\n    }\n}\n/**\n * Avoid stack overflow by pushing items onto the stack in segments\n *\n * @template T\n *   Item type.\n * @param {Array<T>} list\n *   List to inject into.\n * @param {ReadonlyArray<T>} right\n *   Items to inject.\n * @return {undefined}\n *   Nothing.\n */ function chunkedPush(list, right) {\n    /** @type {number} */ let chunkStart = 0;\n    if (right.length < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize) {\n        list.push(...right);\n    } else {\n        while(chunkStart < right.length){\n            list.push(...right.slice(chunkStart, chunkStart + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize));\n            chunkStart += micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\n");

/***/ })

};
;