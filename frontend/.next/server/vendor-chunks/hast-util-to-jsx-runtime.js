"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-jsx-runtime";
exports.ids = ["vendor-chunks/hast-util-to-jsx-runtime"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-to-jsx-runtime/lib/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toJsxRuntime: () => (/* binding */ toJsxRuntime)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! estree-util-is-identifier-name */ \"(ssr)/./node_modules/estree-util-is-identifier-name/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var style_to_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! style-to-js */ \"(ssr)/./node_modules/style-to-js/cjs/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/**\n * @import {Identifier, Literal, MemberExpression} from 'estree'\n * @import {Jsx, JsxDev, Options, Props} from 'hast-util-to-jsx-runtime'\n * @import {Element, Nodes, Parents, Root, Text} from 'hast'\n * @import {MdxFlowExpressionHast, MdxTextExpressionHast} from 'mdast-util-mdx-expression'\n * @import {MdxJsxFlowElementHast, MdxJsxTextElementHast} from 'mdast-util-mdx-jsx'\n * @import {MdxjsEsmHast} from 'mdast-util-mdxjs-esm'\n * @import {Position} from 'unist'\n * @import {Child, Create, Field, JsxElement, State, Style} from './types.js'\n */ \n\n\n\n\n\n\n\n\n// To do: next major: `Object.hasOwn`.\nconst own = {}.hasOwnProperty;\n/** @type {Map<string, number>} */ const emptyMap = new Map();\nconst cap = /[A-Z]/g;\n// `react-dom` triggers a warning for *any* white space in tables.\n// To follow GFM, `mdast-util-to-hast` injects line endings between elements.\n// Other tools might do so too, but they don’t do here, so we remove all of\n// that.\n// See: <https://github.com/facebook/react/pull/7081>.\n// See: <https://github.com/facebook/react/pull/7515>.\n// See: <https://github.com/remarkjs/remark-react/issues/64>.\n// See: <https://github.com/rehypejs/rehype-react/pull/29>.\n// See: <https://github.com/rehypejs/rehype-react/pull/32>.\n// See: <https://github.com/rehypejs/rehype-react/pull/45>.\nconst tableElements = new Set([\n    \"table\",\n    \"tbody\",\n    \"thead\",\n    \"tfoot\",\n    \"tr\"\n]);\nconst tableCellElement = new Set([\n    \"td\",\n    \"th\"\n]);\nconst docs = \"https://github.com/syntax-tree/hast-util-to-jsx-runtime\";\n/**\n * Transform a hast tree to preact, react, solid, svelte, vue, etc.,\n * with an automatic JSX runtime.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options} options\n *   Configuration (required).\n * @returns {JsxElement}\n *   JSX element.\n */ function toJsxRuntime(tree, options) {\n    if (!options || options.Fragment === undefined) {\n        throw new TypeError(\"Expected `Fragment` in options\");\n    }\n    const filePath = options.filePath || undefined;\n    /** @type {Create} */ let create;\n    if (options.development) {\n        if (typeof options.jsxDEV !== \"function\") {\n            throw new TypeError(\"Expected `jsxDEV` in options when `development: true`\");\n        }\n        create = developmentCreate(filePath, options.jsxDEV);\n    } else {\n        if (typeof options.jsx !== \"function\") {\n            throw new TypeError(\"Expected `jsx` in production options\");\n        }\n        if (typeof options.jsxs !== \"function\") {\n            throw new TypeError(\"Expected `jsxs` in production options\");\n        }\n        create = productionCreate(filePath, options.jsx, options.jsxs);\n    }\n    /** @type {State} */ const state = {\n        Fragment: options.Fragment,\n        ancestors: [],\n        components: options.components || {},\n        create,\n        elementAttributeNameCase: options.elementAttributeNameCase || \"react\",\n        evaluater: options.createEvaluater ? options.createEvaluater() : undefined,\n        filePath,\n        ignoreInvalidStyle: options.ignoreInvalidStyle || false,\n        passKeys: options.passKeys !== false,\n        passNode: options.passNode || false,\n        schema: options.space === \"svg\" ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html,\n        stylePropertyNameCase: options.stylePropertyNameCase || \"dom\",\n        tableCellAlignToStyle: options.tableCellAlignToStyle !== false\n    };\n    const result = one(state, tree, undefined);\n    // JSX element.\n    if (result && typeof result !== \"string\") {\n        return result;\n    }\n    // Text node or something that turned into nothing.\n    return state.create(tree, state.Fragment, {\n        children: result || undefined\n    }, undefined);\n}\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */ function one(state, node, key) {\n    if (node.type === \"element\") {\n        return element(state, node, key);\n    }\n    if (node.type === \"mdxFlowExpression\" || node.type === \"mdxTextExpression\") {\n        return mdxExpression(state, node);\n    }\n    if (node.type === \"mdxJsxFlowElement\" || node.type === \"mdxJsxTextElement\") {\n        return mdxJsxElement(state, node, key);\n    }\n    if (node.type === \"mdxjsEsm\") {\n        return mdxEsm(state, node);\n    }\n    if (node.type === \"root\") {\n        return root(state, node, key);\n    }\n    if (node.type === \"text\") {\n        return text(state, node);\n    }\n}\n/**\n * Handle element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */ function element(state, node, key) {\n    const parentSchema = state.schema;\n    let schema = parentSchema;\n    if (node.tagName.toLowerCase() === \"svg\" && parentSchema.space === \"html\") {\n        schema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg;\n        state.schema = schema;\n    }\n    state.ancestors.push(node);\n    const type = findComponentFromName(state, node.tagName, false);\n    const props = createElementProps(state, node);\n    let children = createChildren(state, node);\n    if (tableElements.has(node.tagName)) {\n        children = children.filter(function(child) {\n            return typeof child === \"string\" ? !(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_2__.whitespace)(child) : true;\n        });\n    }\n    addNode(state, props, type, node);\n    addChildren(props, children);\n    // Restore.\n    state.ancestors.pop();\n    state.schema = parentSchema;\n    return state.create(node, type, props, key);\n}\n/**\n * Handle MDX expression.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxFlowExpressionHast | MdxTextExpressionHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */ function mdxExpression(state, node) {\n    if (node.data && node.data.estree && state.evaluater) {\n        const program = node.data.estree;\n        const expression = program.body[0];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === \"ExpressionStatement\");\n        // Assume result is a child.\n        return /** @type {Child | undefined} */ state.evaluater.evaluateExpression(expression.expression);\n    }\n    crashEstree(state, node.position);\n}\n/**\n * Handle MDX ESM.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxjsEsmHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */ function mdxEsm(state, node) {\n    if (node.data && node.data.estree && state.evaluater) {\n        // Assume result is a child.\n        return /** @type {Child | undefined} */ state.evaluater.evaluateProgram(node.data.estree);\n    }\n    crashEstree(state, node.position);\n}\n/**\n * Handle MDX JSX.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */ function mdxJsxElement(state, node, key) {\n    const parentSchema = state.schema;\n    let schema = parentSchema;\n    if (node.name === \"svg\" && parentSchema.space === \"html\") {\n        schema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg;\n        state.schema = schema;\n    }\n    state.ancestors.push(node);\n    const type = node.name === null ? state.Fragment : findComponentFromName(state, node.name, true);\n    const props = createJsxElementProps(state, node);\n    const children = createChildren(state, node);\n    addNode(state, props, type, node);\n    addChildren(props, children);\n    // Restore.\n    state.ancestors.pop();\n    state.schema = parentSchema;\n    return state.create(node, type, props, key);\n}\n/**\n * Handle root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Root} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */ function root(state, node, key) {\n    /** @type {Props} */ const props = {};\n    addChildren(props, createChildren(state, node));\n    return state.create(node, state.Fragment, props, key);\n}\n/**\n * Handle text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Text} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */ function text(_, node) {\n    return node.value;\n}\n/**\n * Add `node` to props.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Props} props\n *   Props.\n * @param {unknown} type\n *   Type.\n * @param {Element | MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Node.\n * @returns {undefined}\n *   Nothing.\n */ function addNode(state, props, type, node) {\n    // If this is swapped out for a component:\n    if (typeof type !== \"string\" && type !== state.Fragment && state.passNode) {\n        props.node = node;\n    }\n}\n/**\n * Add children to props.\n *\n * @param {Props} props\n *   Props.\n * @param {Array<Child>} children\n *   Children.\n * @returns {undefined}\n *   Nothing.\n */ function addChildren(props, children) {\n    if (children.length > 0) {\n        const value = children.length > 1 ? children : children[0];\n        if (value) {\n            props.children = value;\n        }\n    }\n}\n/**\n * @param {string | undefined} _\n *   Path to file.\n * @param {Jsx} jsx\n *   Dynamic.\n * @param {Jsx} jsxs\n *   Static.\n * @returns {Create}\n *   Create a production element.\n */ function productionCreate(_, jsx, jsxs) {\n    return create;\n    /** @type {Create} */ function create(_, type, props, key) {\n        // Only an array when there are 2 or more children.\n        const isStaticChildren = Array.isArray(props.children);\n        const fn = isStaticChildren ? jsxs : jsx;\n        return key ? fn(type, props, key) : fn(type, props);\n    }\n}\n/**\n * @param {string | undefined} filePath\n *   Path to file.\n * @param {JsxDev} jsxDEV\n *   Development.\n * @returns {Create}\n *   Create a development element.\n */ function developmentCreate(filePath, jsxDEV) {\n    return create;\n    /** @type {Create} */ function create(node, type, props, key) {\n        // Only an array when there are 2 or more children.\n        const isStaticChildren = Array.isArray(props.children);\n        const point = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_4__.pointStart)(node);\n        return jsxDEV(type, props, key, isStaticChildren, {\n            columnNumber: point ? point.column - 1 : undefined,\n            fileName: filePath,\n            lineNumber: point ? point.line : undefined\n        }, undefined);\n    }\n}\n/**\n * Create props from an element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current element.\n * @returns {Props}\n *   Props.\n */ function createElementProps(state, node) {\n    /** @type {Props} */ const props = {};\n    /** @type {string | undefined} */ let alignValue;\n    /** @type {string} */ let prop;\n    for(prop in node.properties){\n        if (prop !== \"children\" && own.call(node.properties, prop)) {\n            const result = createProperty(state, prop, node.properties[prop]);\n            if (result) {\n                const [key, value] = result;\n                if (state.tableCellAlignToStyle && key === \"align\" && typeof value === \"string\" && tableCellElement.has(node.tagName)) {\n                    alignValue = value;\n                } else {\n                    props[key] = value;\n                }\n            }\n        }\n    }\n    if (alignValue) {\n        // Assume style is an object.\n        const style = /** @type {Style} */ props.style || (props.style = {});\n        style[state.stylePropertyNameCase === \"css\" ? \"text-align\" : \"textAlign\"] = alignValue;\n    }\n    return props;\n}\n/**\n * Create props from a JSX element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current JSX element.\n * @returns {Props}\n *   Props.\n */ function createJsxElementProps(state, node) {\n    /** @type {Props} */ const props = {};\n    for (const attribute of node.attributes){\n        if (attribute.type === \"mdxJsxExpressionAttribute\") {\n            if (attribute.data && attribute.data.estree && state.evaluater) {\n                const program = attribute.data.estree;\n                const expression = program.body[0];\n                (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === \"ExpressionStatement\");\n                const objectExpression = expression.expression;\n                (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(objectExpression.type === \"ObjectExpression\");\n                const property = objectExpression.properties[0];\n                (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(property.type === \"SpreadElement\");\n                Object.assign(props, state.evaluater.evaluateExpression(property.argument));\n            } else {\n                crashEstree(state, node.position);\n            }\n        } else {\n            // For JSX, the author is responsible of passing in the correct values.\n            const name = attribute.name;\n            /** @type {unknown} */ let value;\n            if (attribute.value && typeof attribute.value === \"object\") {\n                if (attribute.value.data && attribute.value.data.estree && state.evaluater) {\n                    const program = attribute.value.data.estree;\n                    const expression = program.body[0];\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(expression.type === \"ExpressionStatement\");\n                    value = state.evaluater.evaluateExpression(expression.expression);\n                } else {\n                    crashEstree(state, node.position);\n                }\n            } else {\n                value = attribute.value === null ? true : attribute.value;\n            }\n            // Assume a prop.\n            props[name] = /** @type {Props[keyof Props]} */ value;\n        }\n    }\n    return props;\n}\n/**\n * Create children.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Parents} node\n *   Current element.\n * @returns {Array<Child>}\n *   Children.\n */ function createChildren(state, node) {\n    /** @type {Array<Child>} */ const children = [];\n    let index = -1;\n    /** @type {Map<string, number>} */ // Note: test this when Solid doesn’t want to merge my upcoming PR.\n    /* c8 ignore next */ const countsByName = state.passKeys ? new Map() : emptyMap;\n    while(++index < node.children.length){\n        const child = node.children[index];\n        /** @type {string | undefined} */ let key;\n        if (state.passKeys) {\n            const name = child.type === \"element\" ? child.tagName : child.type === \"mdxJsxFlowElement\" || child.type === \"mdxJsxTextElement\" ? child.name : undefined;\n            if (name) {\n                const count = countsByName.get(name) || 0;\n                key = name + \"-\" + count;\n                countsByName.set(name, count + 1);\n            }\n        }\n        const result = one(state, child, key);\n        if (result !== undefined) children.push(result);\n    }\n    return children;\n}\n/**\n * Handle a property.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Field | undefined}\n *   Field for runtime, optional.\n */ function createProperty(state, prop, value) {\n    const info = (0,property_information__WEBPACK_IMPORTED_MODULE_5__.find)(state.schema, prop);\n    // Ignore nullish and `NaN` values.\n    if (value === null || value === undefined || typeof value === \"number\" && Number.isNaN(value)) {\n        return;\n    }\n    if (Array.isArray(value)) {\n        // Accept `array`.\n        // Most props are space-separated.\n        value = info.commaSeparated ? (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value) : (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_7__.stringify)(value);\n    }\n    // React only accepts `style` as object.\n    if (info.property === \"style\") {\n        let styleObject = typeof value === \"object\" ? value : parseStyle(state, String(value));\n        if (state.stylePropertyNameCase === \"css\") {\n            styleObject = transformStylesToCssCasing(styleObject);\n        }\n        return [\n            \"style\",\n            styleObject\n        ];\n    }\n    return [\n        state.elementAttributeNameCase === \"react\" && info.space ? property_information__WEBPACK_IMPORTED_MODULE_8__.hastToReact[info.property] || info.property : info.attribute,\n        value\n    ];\n}\n/**\n * Parse a CSS declaration to an object.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} value\n *   CSS declarations.\n * @returns {Style}\n *   Properties.\n * @throws\n *   Throws `VFileMessage` when CSS cannot be parsed.\n */ function parseStyle(state, value) {\n    try {\n        return style_to_js__WEBPACK_IMPORTED_MODULE_0__(value, {\n            reactCompat: true\n        });\n    } catch (error) {\n        if (state.ignoreInvalidStyle) {\n            return {};\n        }\n        const cause = /** @type {Error} */ error;\n        const message = new vfile_message__WEBPACK_IMPORTED_MODULE_9__.VFileMessage(\"Cannot parse `style` attribute\", {\n            ancestors: state.ancestors,\n            cause,\n            ruleId: \"style\",\n            source: \"hast-util-to-jsx-runtime\"\n        });\n        message.file = state.filePath || undefined;\n        message.url = docs + \"#cannot-parse-style-attribute\";\n        throw message;\n    }\n}\n/**\n * Create a JSX name from a string.\n *\n * @param {State} state\n *   To do.\n * @param {string} name\n *   Name.\n * @param {boolean} allowExpression\n *   Allow member expressions and identifiers.\n * @returns {unknown}\n *   To do.\n */ function findComponentFromName(state, name, allowExpression) {\n    /** @type {Identifier | Literal | MemberExpression} */ let result;\n    if (!allowExpression) {\n        result = {\n            type: \"Literal\",\n            value: name\n        };\n    } else if (name.includes(\".\")) {\n        const identifiers = name.split(\".\");\n        let index = -1;\n        /** @type {Identifier | Literal | MemberExpression | undefined} */ let node;\n        while(++index < identifiers.length){\n            /** @type {Identifier | Literal} */ const prop = (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__.name)(identifiers[index]) ? {\n                type: \"Identifier\",\n                name: identifiers[index]\n            } : {\n                type: \"Literal\",\n                value: identifiers[index]\n            };\n            node = node ? {\n                type: \"MemberExpression\",\n                object: node,\n                property: prop,\n                computed: Boolean(index && prop.type === \"Literal\"),\n                optional: false\n            } : prop;\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(node, \"always a result\");\n        result = node;\n    } else {\n        result = (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_10__.name)(name) && !/^[a-z]/.test(name) ? {\n            type: \"Identifier\",\n            name\n        } : {\n            type: \"Literal\",\n            value: name\n        };\n    }\n    // Only literals can be passed in `components` currently.\n    // No identifiers / member expressions.\n    if (result.type === \"Literal\") {\n        const name = /** @type {string | number} */ result.value;\n        return own.call(state.components, name) ? state.components[name] : name;\n    }\n    // Assume component.\n    if (state.evaluater) {\n        return state.evaluater.evaluateExpression(result);\n    }\n    crashEstree(state);\n}\n/**\n * @param {State} state\n * @param {Position | undefined} [place]\n * @returns {never}\n */ function crashEstree(state, place) {\n    const message = new vfile_message__WEBPACK_IMPORTED_MODULE_9__.VFileMessage(\"Cannot handle MDX estrees without `createEvaluater`\", {\n        ancestors: state.ancestors,\n        place,\n        ruleId: \"mdx-estree\",\n        source: \"hast-util-to-jsx-runtime\"\n    });\n    message.file = state.filePath || undefined;\n    message.url = docs + \"#cannot-handle-mdx-estrees-without-createevaluater\";\n    throw message;\n}\n/**\n * Transform a DOM casing style object to a CSS casing style object.\n *\n * @param {Style} domCasing\n * @returns {Style}\n */ function transformStylesToCssCasing(domCasing) {\n    /** @type {Style} */ const cssCasing = {};\n    /** @type {string} */ let from;\n    for(from in domCasing){\n        if (own.call(domCasing, from)) {\n            cssCasing[transformStyleToCssCasing(from)] = domCasing[from];\n        }\n    }\n    return cssCasing;\n}\n/**\n * Transform a DOM casing style field to a CSS casing style field.\n *\n * @param {string} from\n * @returns {string}\n */ function transformStyleToCssCasing(from) {\n    let to = from.replace(cap, toDash);\n    // Handle `ms-xxx` -> `-ms-xxx`.\n    if (to.slice(0, 3) === \"ms-\") to = \"-\" + to;\n    return to;\n}\n/**\n * Make `$0` dash cased.\n *\n * @param {string} $0\n *   Capitalized ASCII leter.\n * @returns {string}\n *   Dash and lower letter.\n */ function toDash($0) {\n    return \"-\" + $0.toLowerCase();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\n");

/***/ })

};
;