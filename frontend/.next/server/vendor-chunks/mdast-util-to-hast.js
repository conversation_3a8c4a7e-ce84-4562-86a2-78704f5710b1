"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-hast";
exports.ids = ["vendor-chunks/mdast-util-to-hast"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/footer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultFootnoteBackContent: () => (/* binding */ defaultFootnoteBackContent),\n/* harmony export */   defaultFootnoteBackLabel: () => (/* binding */ defaultFootnoteBackLabel),\n/* harmony export */   footer: () => (/* binding */ footer)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */ /**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */ \n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */ function defaultFootnoteBackContent(_, rereferenceIndex) {\n    /** @type {Array<ElementContent>} */ const result = [\n        {\n            type: \"text\",\n            value: \"↩\"\n        }\n    ];\n    if (rereferenceIndex > 1) {\n        result.push({\n            type: \"element\",\n            tagName: \"sup\",\n            properties: {},\n            children: [\n                {\n                    type: \"text\",\n                    value: String(rereferenceIndex)\n                }\n            ]\n        });\n    }\n    return result;\n}\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */ function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n    return \"Back to reference \" + (referenceIndex + 1) + (rereferenceIndex > 1 ? \"-\" + rereferenceIndex : \"\");\n}\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */ // eslint-disable-next-line complexity\nfunction footer(state) {\n    const clobberPrefix = typeof state.options.clobberPrefix === \"string\" ? state.options.clobberPrefix : \"user-content-\";\n    const footnoteBackContent = state.options.footnoteBackContent || defaultFootnoteBackContent;\n    const footnoteBackLabel = state.options.footnoteBackLabel || defaultFootnoteBackLabel;\n    const footnoteLabel = state.options.footnoteLabel || \"Footnotes\";\n    const footnoteLabelTagName = state.options.footnoteLabelTagName || \"h2\";\n    const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n        className: [\n            \"sr-only\"\n        ]\n    };\n    /** @type {Array<ElementContent>} */ const listItems = [];\n    let referenceIndex = -1;\n    while(++referenceIndex < state.footnoteOrder.length){\n        const definition = state.footnoteById.get(state.footnoteOrder[referenceIndex]);\n        if (!definition) {\n            continue;\n        }\n        const content = state.all(definition);\n        const id = String(definition.identifier).toUpperCase();\n        const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(id.toLowerCase());\n        let rereferenceIndex = 0;\n        /** @type {Array<ElementContent>} */ const backReferences = [];\n        const counts = state.footnoteCounts.get(id);\n        // eslint-disable-next-line no-unmodified-loop-condition\n        while(counts !== undefined && ++rereferenceIndex <= counts){\n            if (backReferences.length > 0) {\n                backReferences.push({\n                    type: \"text\",\n                    value: \" \"\n                });\n            }\n            let children = typeof footnoteBackContent === \"string\" ? footnoteBackContent : footnoteBackContent(referenceIndex, rereferenceIndex);\n            if (typeof children === \"string\") {\n                children = {\n                    type: \"text\",\n                    value: children\n                };\n            }\n            backReferences.push({\n                type: \"element\",\n                tagName: \"a\",\n                properties: {\n                    href: \"#\" + clobberPrefix + \"fnref-\" + safeId + (rereferenceIndex > 1 ? \"-\" + rereferenceIndex : \"\"),\n                    dataFootnoteBackref: \"\",\n                    ariaLabel: typeof footnoteBackLabel === \"string\" ? footnoteBackLabel : footnoteBackLabel(referenceIndex, rereferenceIndex),\n                    className: [\n                        \"data-footnote-backref\"\n                    ]\n                },\n                children: Array.isArray(children) ? children : [\n                    children\n                ]\n            });\n        }\n        const tail = content[content.length - 1];\n        if (tail && tail.type === \"element\" && tail.tagName === \"p\") {\n            const tailTail = tail.children[tail.children.length - 1];\n            if (tailTail && tailTail.type === \"text\") {\n                tailTail.value += \" \";\n            } else {\n                tail.children.push({\n                    type: \"text\",\n                    value: \" \"\n                });\n            }\n            tail.children.push(...backReferences);\n        } else {\n            content.push(...backReferences);\n        }\n        /** @type {Element} */ const listItem = {\n            type: \"element\",\n            tagName: \"li\",\n            properties: {\n                id: clobberPrefix + \"fn-\" + safeId\n            },\n            children: state.wrap(content, true)\n        };\n        state.patch(definition, listItem);\n        listItems.push(listItem);\n    }\n    if (listItems.length === 0) {\n        return;\n    }\n    return {\n        type: \"element\",\n        tagName: \"section\",\n        properties: {\n            dataFootnotes: true,\n            className: [\n                \"footnotes\"\n            ]\n        },\n        children: [\n            {\n                type: \"element\",\n                tagName: footnoteLabelTagName,\n                properties: {\n                    ...(0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(footnoteLabelProperties),\n                    id: \"footnote-label\"\n                },\n                children: [\n                    {\n                        type: \"text\",\n                        value: footnoteLabel\n                    }\n                ]\n            },\n            {\n                type: \"text\",\n                value: \"\\n\"\n            },\n            {\n                type: \"element\",\n                tagName: \"ol\",\n                properties: {},\n                children: state.wrap(listItems, true)\n            },\n            {\n                type: \"text\",\n                value: \"\\n\"\n            }\n        ]\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function blockquote(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"blockquote\",\n        properties: {},\n        children: state.wrap(state.all(node), true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9ibG9ja3F1b3RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7OztDQUlDLEdBRUQsbURBQW1EO0FBQ25EO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0EsV0FBV0MsS0FBSyxFQUFFQyxJQUFJO0lBQ3BDLG9CQUFvQixHQUNwQixNQUFNQyxTQUFTO1FBQ2JDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxZQUFZLENBQUM7UUFDYkMsVUFBVU4sTUFBTU8sSUFBSSxDQUFDUCxNQUFNUSxHQUFHLENBQUNQLE9BQU87SUFDeEM7SUFDQUQsTUFBTVMsS0FBSyxDQUFDUixNQUFNQztJQUNsQixPQUFPRixNQUFNVSxTQUFTLENBQUNULE1BQU1DO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvYmxvY2txdW90ZS5qcz9kYTkzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuQmxvY2txdW90ZX0gQmxvY2txdW90ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgYmxvY2txdW90ZWAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtCbG9ja3F1b3RlfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gYmxvY2txdW90ZShzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2Jsb2NrcXVvdGUnLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS53cmFwKHN0YXRlLmFsbChub2RlKSwgdHJ1ZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbImJsb2NrcXVvdGUiLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsInByb3BlcnRpZXMiLCJjaGlsZHJlbiIsIndyYXAiLCJhbGwiLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/break.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */ function hardBreak(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"br\",\n        properties: {},\n        children: []\n    };\n    state.patch(node, result);\n    return [\n        state.applyData(node, result),\n        {\n            type: \"text\",\n            value: \"\\n\"\n        }\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9icmVhay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7O0NBS0MsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxVQUFVQyxLQUFLLEVBQUVDLElBQUk7SUFDbkMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFBQ0MsTUFBTTtRQUFXQyxTQUFTO1FBQU1DLFlBQVksQ0FBQztRQUFHQyxVQUFVLEVBQUU7SUFBQTtJQUM1RU4sTUFBTU8sS0FBSyxDQUFDTixNQUFNQztJQUNsQixPQUFPO1FBQUNGLE1BQU1RLFNBQVMsQ0FBQ1AsTUFBTUM7UUFBUztZQUFDQyxNQUFNO1lBQVFNLE9BQU87UUFBSTtLQUFFO0FBQ3JFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvYnJlYWsuanM/NmI4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuVGV4dH0gVGV4dFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5CcmVha30gQnJlYWtcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGJyZWFrYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0JyZWFrfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7QXJyYXk8RWxlbWVudCB8IFRleHQ+fVxuICogICBoYXN0IGVsZW1lbnQgY29udGVudC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGhhcmRCcmVhayhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHt0eXBlOiAnZWxlbWVudCcsIHRhZ05hbWU6ICdicicsIHByb3BlcnRpZXM6IHt9LCBjaGlsZHJlbjogW119XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIFtzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KSwge3R5cGU6ICd0ZXh0JywgdmFsdWU6ICdcXG4nfV1cbn1cbiJdLCJuYW1lcyI6WyJoYXJkQnJlYWsiLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsInByb3BlcnRpZXMiLCJjaGlsZHJlbiIsInBhdGNoIiwiYXBwbHlEYXRhIiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/code.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function code(state, node) {\n    const value = node.value ? node.value + \"\\n\" : \"\";\n    /** @type {Properties} */ const properties = {};\n    if (node.lang) {\n        properties.className = [\n            \"language-\" + node.lang\n        ];\n    }\n    // Create `<code>`.\n    /** @type {Element} */ let result = {\n        type: \"element\",\n        tagName: \"code\",\n        properties,\n        children: [\n            {\n                type: \"text\",\n                value\n            }\n        ]\n    };\n    if (node.meta) {\n        result.data = {\n            meta: node.meta\n        };\n    }\n    state.patch(node, result);\n    result = state.applyData(node, result);\n    // Create `<pre>`.\n    result = {\n        type: \"element\",\n        tagName: \"pre\",\n        properties: {},\n        children: [\n            result\n        ]\n    };\n    state.patch(node, result);\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/delete.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strikethrough: () => (/* binding */ strikethrough)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Delete} Delete\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `delete` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Delete} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function strikethrough(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"del\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9kZWxldGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7O0NBSUMsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxjQUFjQyxLQUFLLEVBQUVDLElBQUk7SUFDdkMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXN5c3RlbS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2RlbGV0ZS5qcz9jZTZiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuRGVsZXRlfSBEZWxldGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGRlbGV0ZWAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtEZWxldGV9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJpa2V0aHJvdWdoKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnZGVsJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJzdHJpa2V0aHJvdWdoIiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJwcm9wZXJ0aWVzIiwiY2hpbGRyZW4iLCJhbGwiLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `emphasis` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Emphasis} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function emphasis(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"em\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Q0FJQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLFNBQVNDLEtBQUssRUFBRUMsSUFBSTtJQUNsQyxvQkFBb0IsR0FDcEIsTUFBTUMsU0FBUztRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsWUFBWSxDQUFDO1FBQ2JDLFVBQVVOLE1BQU1PLEdBQUcsQ0FBQ047SUFDdEI7SUFDQUQsTUFBTVEsS0FBSyxDQUFDUCxNQUFNQztJQUNsQixPQUFPRixNQUFNUyxTQUFTLENBQUNSLE1BQU1DO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvZW1waGFzaXMuanM/NzI5NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkVtcGhhc2lzfSBFbXBoYXNpc1xuICogQHR5cGVkZWYge2ltcG9ydCgnLi4vc3RhdGUuanMnKS5TdGF0ZX0gU3RhdGVcbiAqL1xuXG4vLyBNYWtlIFZTIENvZGUgc2hvdyByZWZlcmVuY2VzIHRvIHRoZSBhYm92ZSB0eXBlcy5cbicnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgZW1waGFzaXNgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7RW1waGFzaXN9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlbXBoYXNpcyhzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2VtJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJlbXBoYXNpcyIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJ0YWdOYW1lIiwicHJvcGVydGllcyIsImNoaWxkcmVuIiwiYWxsIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   footnoteReference: () => (/* binding */ footnoteReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function footnoteReference(state, node) {\n    const clobberPrefix = typeof state.options.clobberPrefix === \"string\" ? state.options.clobberPrefix : \"user-content-\";\n    const id = String(node.identifier).toUpperCase();\n    const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(id.toLowerCase());\n    const index = state.footnoteOrder.indexOf(id);\n    /** @type {number} */ let counter;\n    let reuseCounter = state.footnoteCounts.get(id);\n    if (reuseCounter === undefined) {\n        reuseCounter = 0;\n        state.footnoteOrder.push(id);\n        counter = state.footnoteOrder.length;\n    } else {\n        counter = index + 1;\n    }\n    reuseCounter += 1;\n    state.footnoteCounts.set(id, reuseCounter);\n    /** @type {Element} */ const link = {\n        type: \"element\",\n        tagName: \"a\",\n        properties: {\n            href: \"#\" + clobberPrefix + \"fn-\" + safeId,\n            id: clobberPrefix + \"fnref-\" + safeId + (reuseCounter > 1 ? \"-\" + reuseCounter : \"\"),\n            dataFootnoteRef: true,\n            ariaDescribedBy: [\n                \"footnote-label\"\n            ]\n        },\n        children: [\n            {\n                type: \"text\",\n                value: String(counter)\n            }\n        ]\n    };\n    state.patch(node, link);\n    /** @type {Element} */ const sup = {\n        type: \"element\",\n        tagName: \"sup\",\n        properties: {},\n        children: [\n            link\n        ]\n    };\n    state.patch(node, sup);\n    return state.applyData(node, sup);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/heading.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function heading(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"h\" + node.depth,\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9oZWFkaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7OztDQUlDLEdBRUQsbURBQW1EO0FBQ25EO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0EsUUFBUUMsS0FBSyxFQUFFQyxJQUFJO0lBQ2pDLG9CQUFvQixHQUNwQixNQUFNQyxTQUFTO1FBQ2JDLE1BQU07UUFDTkMsU0FBUyxNQUFNSCxLQUFLSSxLQUFLO1FBQ3pCQyxZQUFZLENBQUM7UUFDYkMsVUFBVVAsTUFBTVEsR0FBRyxDQUFDUDtJQUN0QjtJQUNBRCxNQUFNUyxLQUFLLENBQUNSLE1BQU1DO0lBQ2xCLE9BQU9GLE1BQU1VLFNBQVMsQ0FBQ1QsTUFBTUM7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zeXN0ZW0tZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9oZWFkaW5nLmpzP2E3MGEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5IZWFkaW5nfSBIZWFkaW5nXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBoZWFkaW5nYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge0hlYWRpbmd9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoZWFkaW5nKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnaCcgKyBub2RlLmRlcHRoLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbImhlYWRpbmciLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsImRlcHRoIiwicHJvcGVydGllcyIsImNoaWxkcmVuIiwiYWxsIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/html.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Html} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Element | Raw | undefined}\n *   hast node.\n */ function html(state, node) {\n    if (state.options.allowDangerousHtml) {\n        /** @type {Raw} */ const result = {\n            type: \"raw\",\n            value: node.value\n        };\n        state.patch(node, result);\n        return state.applyData(node, result);\n    }\n    return undefined;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9odG1sLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7O0NBVUMsR0FDTSxTQUFTQSxLQUFLQyxLQUFLLEVBQUVDLElBQUk7SUFDOUIsSUFBSUQsTUFBTUUsT0FBTyxDQUFDQyxrQkFBa0IsRUFBRTtRQUNwQyxnQkFBZ0IsR0FDaEIsTUFBTUMsU0FBUztZQUFDQyxNQUFNO1lBQU9DLE9BQU9MLEtBQUtLLEtBQUs7UUFBQTtRQUM5Q04sTUFBTU8sS0FBSyxDQUFDTixNQUFNRztRQUNsQixPQUFPSixNQUFNUSxTQUFTLENBQUNQLE1BQU1HO0lBQy9CO0lBRUEsT0FBT0s7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXN5c3RlbS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL2h0bWwuanM/YTM3NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkh0bWx9IEh0bWxcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi8uLi9pbmRleC5qcycpLlJhd30gUmF3XG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYGh0bWxgIG5vZGUgaW50byBoYXN0IChgcmF3YCBub2RlIGluIGRhbmdlcm91cyBtb2RlLCBvdGhlcndpc2VcbiAqIG5vdGhpbmcpLlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7SHRtbH0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnQgfCBSYXcgfCB1bmRlZmluZWR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGh0bWwoc3RhdGUsIG5vZGUpIHtcbiAgaWYgKHN0YXRlLm9wdGlvbnMuYWxsb3dEYW5nZXJvdXNIdG1sKSB7XG4gICAgLyoqIEB0eXBlIHtSYXd9ICovXG4gICAgY29uc3QgcmVzdWx0ID0ge3R5cGU6ICdyYXcnLCB2YWx1ZTogbm9kZS52YWx1ZX1cbiAgICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gICAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG4gIH1cblxuICByZXR1cm4gdW5kZWZpbmVkXG59XG4iXSwibmFtZXMiOlsiaHRtbCIsInN0YXRlIiwibm9kZSIsIm9wdGlvbnMiLCJhbGxvd0Rhbmdlcm91c0h0bWwiLCJyZXN1bHQiLCJ0eXBlIiwidmFsdWUiLCJwYXRjaCIsImFwcGx5RGF0YSIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js":
/*!*************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/* harmony import */ var _revert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../revert.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */ \n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */ function imageReference(state, node) {\n    const id = String(node.identifier).toUpperCase();\n    const definition = state.definitionById.get(id);\n    if (!definition) {\n        return (0,_revert_js__WEBPACK_IMPORTED_MODULE_0__.revert)(state, node);\n    }\n    /** @type {Properties} */ const properties = {\n        src: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__.normalizeUri)(definition.url || \"\"),\n        alt: node.alt\n    };\n    if (definition.title !== null && definition.title !== undefined) {\n        properties.title = definition.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"img\",\n        properties,\n        children: []\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/image.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function image(state, node) {\n    /** @type {Properties} */ const properties = {\n        src: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(node.url)\n    };\n    if (node.alt !== null && node.alt !== undefined) {\n        properties.alt = node.alt;\n    }\n    if (node.title !== null && node.title !== undefined) {\n        properties.title = node.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"img\",\n        properties,\n        children: []\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9pbWFnZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBOzs7OztDQUtDLEdBRXVEO0FBRXhEOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNDLE1BQU1DLEtBQUssRUFBRUMsSUFBSTtJQUMvQix1QkFBdUIsR0FDdkIsTUFBTUMsYUFBYTtRQUFDQyxLQUFLTCx5RUFBWUEsQ0FBQ0csS0FBS0csR0FBRztJQUFDO0lBRS9DLElBQUlILEtBQUtJLEdBQUcsS0FBSyxRQUFRSixLQUFLSSxHQUFHLEtBQUtDLFdBQVc7UUFDL0NKLFdBQVdHLEdBQUcsR0FBR0osS0FBS0ksR0FBRztJQUMzQjtJQUVBLElBQUlKLEtBQUtNLEtBQUssS0FBSyxRQUFRTixLQUFLTSxLQUFLLEtBQUtELFdBQVc7UUFDbkRKLFdBQVdLLEtBQUssR0FBR04sS0FBS00sS0FBSztJQUMvQjtJQUVBLG9CQUFvQixHQUNwQixNQUFNQyxTQUFTO1FBQUNDLE1BQU07UUFBV0MsU0FBUztRQUFPUjtRQUFZUyxVQUFVLEVBQUU7SUFBQTtJQUN6RVgsTUFBTVksS0FBSyxDQUFDWCxNQUFNTztJQUNsQixPQUFPUixNQUFNYSxTQUFTLENBQUNaLE1BQU1PO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvaW1hZ2UuanM/ZTFkOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuUHJvcGVydGllc30gUHJvcGVydGllc1xuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5JbWFnZX0gSW1hZ2VcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuaW1wb3J0IHtub3JtYWxpemVVcml9IGZyb20gJ21pY3JvbWFyay11dGlsLXNhbml0aXplLXVyaSdcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGBpbWFnZWAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtJbWFnZX0gbm9kZVxuICogICBtZGFzdCBub2RlLlxuICogQHJldHVybnMge0VsZW1lbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGltYWdlKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7UHJvcGVydGllc30gKi9cbiAgY29uc3QgcHJvcGVydGllcyA9IHtzcmM6IG5vcm1hbGl6ZVVyaShub2RlLnVybCl9XG5cbiAgaWYgKG5vZGUuYWx0ICE9PSBudWxsICYmIG5vZGUuYWx0ICE9PSB1bmRlZmluZWQpIHtcbiAgICBwcm9wZXJ0aWVzLmFsdCA9IG5vZGUuYWx0XG4gIH1cblxuICBpZiAobm9kZS50aXRsZSAhPT0gbnVsbCAmJiBub2RlLnRpdGxlICE9PSB1bmRlZmluZWQpIHtcbiAgICBwcm9wZXJ0aWVzLnRpdGxlID0gbm9kZS50aXRsZVxuICB9XG5cbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ2VsZW1lbnQnLCB0YWdOYW1lOiAnaW1nJywgcHJvcGVydGllcywgY2hpbGRyZW46IFtdfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbIm5vcm1hbGl6ZVVyaSIsImltYWdlIiwic3RhdGUiLCJub2RlIiwicHJvcGVydGllcyIsInNyYyIsInVybCIsImFsdCIsInVuZGVmaW5lZCIsInRpdGxlIiwicmVzdWx0IiwidHlwZSIsInRhZ05hbWUiLCJjaGlsZHJlbiIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handlers: () => (/* binding */ handlers)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/code.js\");\n/* harmony import */ var _delete_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./delete.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js\");\n/* harmony import */ var _footnote_reference_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./footnote-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./html.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/html.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./image.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/image.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./link.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./paragraph.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js\");\n/* harmony import */ var _table_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./table.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js\");\n/* harmony import */ var _table_row_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./table-row.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js\");\n/* harmony import */ var _table_cell_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./table-cell.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default handlers for nodes.\n *\n * @satisfies {import('../state.js').Handlers}\n */ const handlers = {\n    blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n    break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n    code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n    delete: _delete_js__WEBPACK_IMPORTED_MODULE_3__.strikethrough,\n    emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n    footnoteReference: _footnote_reference_js__WEBPACK_IMPORTED_MODULE_5__.footnoteReference,\n    heading: _heading_js__WEBPACK_IMPORTED_MODULE_6__.heading,\n    html: _html_js__WEBPACK_IMPORTED_MODULE_7__.html,\n    imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n    image: _image_js__WEBPACK_IMPORTED_MODULE_9__.image,\n    inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_10__.inlineCode,\n    linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n    link: _link_js__WEBPACK_IMPORTED_MODULE_12__.link,\n    listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n    list: _list_js__WEBPACK_IMPORTED_MODULE_14__.list,\n    paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_15__.paragraph,\n    // @ts-expect-error: root is different, but hard to type.\n    root: _root_js__WEBPACK_IMPORTED_MODULE_16__.root,\n    strong: _strong_js__WEBPACK_IMPORTED_MODULE_17__.strong,\n    table: _table_js__WEBPACK_IMPORTED_MODULE_18__.table,\n    tableCell: _table_cell_js__WEBPACK_IMPORTED_MODULE_19__.tableCell,\n    tableRow: _table_row_js__WEBPACK_IMPORTED_MODULE_20__.tableRow,\n    text: _text_js__WEBPACK_IMPORTED_MODULE_21__.text,\n    thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_22__.thematicBreak,\n    toml: ignore,\n    yaml: ignore,\n    definition: ignore,\n    footnoteDefinition: ignore\n};\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n    return undefined;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function inlineCode(state, node) {\n    /** @type {Text} */ const text = {\n        type: \"text\",\n        value: node.value.replace(/\\r?\\n|\\r/g, \" \")\n    };\n    state.patch(node, text);\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"code\",\n        properties: {},\n        children: [\n            text\n        ]\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/* harmony import */ var _revert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../revert.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */ \n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */ function linkReference(state, node) {\n    const id = String(node.identifier).toUpperCase();\n    const definition = state.definitionById.get(id);\n    if (!definition) {\n        return (0,_revert_js__WEBPACK_IMPORTED_MODULE_0__.revert)(state, node);\n    }\n    /** @type {Properties} */ const properties = {\n        href: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_1__.normalizeUri)(definition.url || \"\")\n    };\n    if (definition.title !== null && definition.title !== undefined) {\n        properties.title = definition.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"a\",\n        properties,\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9saW5rLXJlZmVyZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7O0NBTUMsR0FFdUQ7QUFDckI7QUFFbkM7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0UsY0FBY0MsS0FBSyxFQUFFQyxJQUFJO0lBQ3ZDLE1BQU1DLEtBQUtDLE9BQU9GLEtBQUtHLFVBQVUsRUFBRUMsV0FBVztJQUM5QyxNQUFNQyxhQUFhTixNQUFNTyxjQUFjLENBQUNDLEdBQUcsQ0FBQ047SUFFNUMsSUFBSSxDQUFDSSxZQUFZO1FBQ2YsT0FBT1Isa0RBQU1BLENBQUNFLE9BQU9DO0lBQ3ZCO0lBRUEsdUJBQXVCLEdBQ3ZCLE1BQU1RLGFBQWE7UUFBQ0MsTUFBTWIseUVBQVlBLENBQUNTLFdBQVdLLEdBQUcsSUFBSTtJQUFHO0lBRTVELElBQUlMLFdBQVdNLEtBQUssS0FBSyxRQUFRTixXQUFXTSxLQUFLLEtBQUtDLFdBQVc7UUFDL0RKLFdBQVdHLEtBQUssR0FBR04sV0FBV00sS0FBSztJQUNyQztJQUVBLG9CQUFvQixHQUNwQixNQUFNRSxTQUFTO1FBQ2JDLE1BQU07UUFDTkMsU0FBUztRQUNUUDtRQUNBUSxVQUFVakIsTUFBTWtCLEdBQUcsQ0FBQ2pCO0lBQ3RCO0lBQ0FELE1BQU1tQixLQUFLLENBQUNsQixNQUFNYTtJQUNsQixPQUFPZCxNQUFNb0IsU0FBUyxDQUFDbkIsTUFBTWE7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zeXN0ZW0tZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9saW5rLXJlZmVyZW5jZS5qcz85OWFhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50Q29udGVudH0gRWxlbWVudENvbnRlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5Qcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkxpbmtSZWZlcmVuY2V9IExpbmtSZWZlcmVuY2VcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuaW1wb3J0IHtub3JtYWxpemVVcml9IGZyb20gJ21pY3JvbWFyay11dGlsLXNhbml0aXplLXVyaSdcbmltcG9ydCB7cmV2ZXJ0fSBmcm9tICcuLi9yZXZlcnQuanMnXG5cbi8qKlxuICogVHVybiBhbiBtZGFzdCBgbGlua1JlZmVyZW5jZWAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtMaW5rUmVmZXJlbmNlfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7QXJyYXk8RWxlbWVudENvbnRlbnQ+IHwgRWxlbWVudENvbnRlbnR9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGxpbmtSZWZlcmVuY2Uoc3RhdGUsIG5vZGUpIHtcbiAgY29uc3QgaWQgPSBTdHJpbmcobm9kZS5pZGVudGlmaWVyKS50b1VwcGVyQ2FzZSgpXG4gIGNvbnN0IGRlZmluaXRpb24gPSBzdGF0ZS5kZWZpbml0aW9uQnlJZC5nZXQoaWQpXG5cbiAgaWYgKCFkZWZpbml0aW9uKSB7XG4gICAgcmV0dXJuIHJldmVydChzdGF0ZSwgbm9kZSlcbiAgfVxuXG4gIC8qKiBAdHlwZSB7UHJvcGVydGllc30gKi9cbiAgY29uc3QgcHJvcGVydGllcyA9IHtocmVmOiBub3JtYWxpemVVcmkoZGVmaW5pdGlvbi51cmwgfHwgJycpfVxuXG4gIGlmIChkZWZpbml0aW9uLnRpdGxlICE9PSBudWxsICYmIGRlZmluaXRpb24udGl0bGUgIT09IHVuZGVmaW5lZCkge1xuICAgIHByb3BlcnRpZXMudGl0bGUgPSBkZWZpbml0aW9uLnRpdGxlXG4gIH1cblxuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ2EnLFxuICAgIHByb3BlcnRpZXMsXG4gICAgY2hpbGRyZW46IHN0YXRlLmFsbChub2RlKVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsibm9ybWFsaXplVXJpIiwicmV2ZXJ0IiwibGlua1JlZmVyZW5jZSIsInN0YXRlIiwibm9kZSIsImlkIiwiU3RyaW5nIiwiaWRlbnRpZmllciIsInRvVXBwZXJDYXNlIiwiZGVmaW5pdGlvbiIsImRlZmluaXRpb25CeUlkIiwiZ2V0IiwicHJvcGVydGllcyIsImhyZWYiLCJ1cmwiLCJ0aXRsZSIsInVuZGVmaW5lZCIsInJlc3VsdCIsInR5cGUiLCJ0YWdOYW1lIiwiY2hpbGRyZW4iLCJhbGwiLCJwYXRjaCIsImFwcGx5RGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/link.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function link(state, node) {\n    /** @type {Properties} */ const properties = {\n        href: (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_0__.normalizeUri)(node.url)\n    };\n    if (node.title !== null && node.title !== undefined) {\n        properties.title = node.title;\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"a\",\n        properties,\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/list-item.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */ function listItem(state, node, parent) {\n    const results = state.all(node);\n    const loose = parent ? listLoose(parent) : listItemLoose(node);\n    /** @type {Properties} */ const properties = {};\n    /** @type {Array<ElementContent>} */ const children = [];\n    if (typeof node.checked === \"boolean\") {\n        const head = results[0];\n        /** @type {Element} */ let paragraph;\n        if (head && head.type === \"element\" && head.tagName === \"p\") {\n            paragraph = head;\n        } else {\n            paragraph = {\n                type: \"element\",\n                tagName: \"p\",\n                properties: {},\n                children: []\n            };\n            results.unshift(paragraph);\n        }\n        if (paragraph.children.length > 0) {\n            paragraph.children.unshift({\n                type: \"text\",\n                value: \" \"\n            });\n        }\n        paragraph.children.unshift({\n            type: \"element\",\n            tagName: \"input\",\n            properties: {\n                type: \"checkbox\",\n                checked: node.checked,\n                disabled: true\n            },\n            children: []\n        });\n        // According to github-markdown-css, this class hides bullet.\n        // See: <https://github.com/sindresorhus/github-markdown-css>.\n        properties.className = [\n            \"task-list-item\"\n        ];\n    }\n    let index = -1;\n    while(++index < results.length){\n        const child = results[index];\n        // Add eols before nodes, except if this is a loose, first paragraph.\n        if (loose || index !== 0 || child.type !== \"element\" || child.tagName !== \"p\") {\n            children.push({\n                type: \"text\",\n                value: \"\\n\"\n            });\n        }\n        if (child.type === \"element\" && child.tagName === \"p\" && !loose) {\n            children.push(...child.children);\n        } else {\n            children.push(child);\n        }\n    }\n    const tail = results[results.length - 1];\n    // Add a final eol.\n    if (tail && (loose || tail.type !== \"element\" || tail.tagName !== \"p\")) {\n        children.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"li\",\n        properties,\n        children\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n/**\n * @param {Parents} node\n * @return {Boolean}\n */ function listLoose(node) {\n    let loose = false;\n    if (node.type === \"list\") {\n        loose = node.spread || false;\n        const children = node.children;\n        let index = -1;\n        while(!loose && ++index < children.length){\n            loose = listItemLoose(children[index]);\n        }\n    }\n    return loose;\n}\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */ function listItemLoose(node) {\n    const spread = node.spread;\n    return spread === null || spread === undefined ? node.children.length > 1 : spread;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/list.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function list(state, node) {\n    /** @type {Properties} */ const properties = {};\n    const results = state.all(node);\n    let index = -1;\n    if (typeof node.start === \"number\" && node.start !== 1) {\n        properties.start = node.start;\n    }\n    // Like GitHub, add a class for custom styling.\n    while(++index < results.length){\n        const child = results[index];\n        if (child.type === \"element\" && child.tagName === \"li\" && child.properties && Array.isArray(child.properties.className) && child.properties.className.includes(\"task-list-item\")) {\n            properties.className = [\n                \"contains-task-list\"\n            ];\n            break;\n        }\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: node.ordered ? \"ol\" : \"ul\",\n        properties,\n        children: state.wrap(results, true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function paragraph(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"p\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9wYXJhZ3JhcGguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7O0NBSUMsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxVQUFVQyxLQUFLLEVBQUVDLElBQUk7SUFDbkMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXN5c3RlbS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL3BhcmFncmFwaC5qcz9kZTE5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuUGFyYWdyYXBofSBQYXJhZ3JhcGhcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHBhcmFncmFwaGAgbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtQYXJhZ3JhcGh9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJhZ3JhcGgoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdwJyxcbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJwYXJhZ3JhcGgiLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsInByb3BlcnRpZXMiLCJjaGlsZHJlbiIsImFsbCIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/root.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Parents} HastParents\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastParents}\n *   hast node.\n */ function root(state, node) {\n    /** @type {HastRoot} */ const result = {\n        type: \"root\",\n        children: state.wrap(state.all(node))\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLEtBQUtDLEtBQUssRUFBRUMsSUFBSTtJQUM5QixxQkFBcUIsR0FDckIsTUFBTUMsU0FBUztRQUFDQyxNQUFNO1FBQVFDLFVBQVVKLE1BQU1LLElBQUksQ0FBQ0wsTUFBTU0sR0FBRyxDQUFDTDtJQUFNO0lBQ25FRCxNQUFNTyxLQUFLLENBQUNOLE1BQU1DO0lBQ2xCLE9BQU9GLE1BQU1RLFNBQVMsQ0FBQ1AsTUFBTUM7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zeXN0ZW0tZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9yb290LmpzPzA2MjIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuUGFyZW50c30gSGFzdFBhcmVudHNcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5Sb290fSBIYXN0Um9vdFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5Sb290fSBNZGFzdFJvb3RcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHJvb3RgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7TWRhc3RSb290fSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7SGFzdFBhcmVudHN9XG4gKiAgIGhhc3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJvb3Qoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtIYXN0Um9vdH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge3R5cGU6ICdyb290JywgY2hpbGRyZW46IHN0YXRlLndyYXAoc3RhdGUuYWxsKG5vZGUpKX1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJyb290Iiwic3RhdGUiLCJub2RlIiwicmVzdWx0IiwidHlwZSIsImNoaWxkcmVuIiwid3JhcCIsImFsbCIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/strong.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `strong` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Strong} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function strong(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"strong\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy9zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7O0NBSUMsR0FFRCxtREFBbUQ7QUFDbkQ7QUFFQTs7Ozs7Ozs7O0NBU0MsR0FDTSxTQUFTQSxPQUFPQyxLQUFLLEVBQUVDLElBQUk7SUFDaEMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXN5c3RlbS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL3N0cm9uZy5qcz8xNGY2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEVsZW1lbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuU3Ryb25nfSBTdHJvbmdcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4uL3N0YXRlLmpzJykuU3RhdGV9IFN0YXRlXG4gKi9cblxuLy8gTWFrZSBWUyBDb2RlIHNob3cgcmVmZXJlbmNlcyB0byB0aGUgYWJvdmUgdHlwZXMuXG4nJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHN0cm9uZ2Agbm9kZSBpbnRvIGhhc3QuXG4gKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kLlxuICogQHBhcmFtIHtTdHJvbmd9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJvbmcoc3RhdGUsIG5vZGUpIHtcbiAgLyoqIEB0eXBlIHtFbGVtZW50fSAqL1xuICBjb25zdCByZXN1bHQgPSB7XG4gICAgdHlwZTogJ2VsZW1lbnQnLFxuICAgIHRhZ05hbWU6ICdzdHJvbmcnLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBzdGF0ZS5hbGwobm9kZSlcbiAgfVxuICBzdGF0ZS5wYXRjaChub2RlLCByZXN1bHQpXG4gIHJldHVybiBzdGF0ZS5hcHBseURhdGEobm9kZSwgcmVzdWx0KVxufVxuIl0sIm5hbWVzIjpbInN0cm9uZyIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJ0YWdOYW1lIiwicHJvcGVydGllcyIsImNoaWxkcmVuIiwiYWxsIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableCell: () => (/* binding */ tableCell)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function tableCell(state, node) {\n    // Note: this function is normally not called: see `table-row` for how rows\n    // and their cells are compiled.\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"td\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90YWJsZS1jZWxsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7OztDQUlDLEdBRUQsbURBQW1EO0FBQ25EO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0EsVUFBVUMsS0FBSyxFQUFFQyxJQUFJO0lBQ25DLDJFQUEyRTtJQUMzRSxnQ0FBZ0M7SUFDaEMsb0JBQW9CLEdBQ3BCLE1BQU1DLFNBQVM7UUFDYkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFlBQVksQ0FBQztRQUNiQyxVQUFVTixNQUFNTyxHQUFHLENBQUNOO0lBQ3RCO0lBQ0FELE1BQU1RLEtBQUssQ0FBQ1AsTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVMsU0FBUyxDQUFDUixNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXN5c3RlbS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL3RhYmxlLWNlbGwuanM/ZDc2YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5FbGVtZW50fSBFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlRhYmxlQ2VsbH0gVGFibGVDZWxsXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGB0YWJsZUNlbGxgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7VGFibGVDZWxsfSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7RWxlbWVudH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gdGFibGVDZWxsKHN0YXRlLCBub2RlKSB7XG4gIC8vIE5vdGU6IHRoaXMgZnVuY3Rpb24gaXMgbm9ybWFsbHkgbm90IGNhbGxlZDogc2VlIGB0YWJsZS1yb3dgIGZvciBob3cgcm93c1xuICAvLyBhbmQgdGhlaXIgY2VsbHMgYXJlIGNvbXBpbGVkLlxuICAvKiogQHR5cGUge0VsZW1lbnR9ICovXG4gIGNvbnN0IHJlc3VsdCA9IHtcbiAgICB0eXBlOiAnZWxlbWVudCcsXG4gICAgdGFnTmFtZTogJ3RkJywgLy8gQXNzdW1lIGJvZHkgY2VsbC5cbiAgICBwcm9wZXJ0aWVzOiB7fSxcbiAgICBjaGlsZHJlbjogc3RhdGUuYWxsKG5vZGUpXG4gIH1cbiAgc3RhdGUucGF0Y2gobm9kZSwgcmVzdWx0KVxuICByZXR1cm4gc3RhdGUuYXBwbHlEYXRhKG5vZGUsIHJlc3VsdClcbn1cbiJdLCJuYW1lcyI6WyJ0YWJsZUNlbGwiLCJzdGF0ZSIsIm5vZGUiLCJyZXN1bHQiLCJ0eXBlIiwidGFnTmFtZSIsInByb3BlcnRpZXMiLCJjaGlsZHJlbiIsImFsbCIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table-row.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tableRow: () => (/* binding */ tableRow)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */ function tableRow(state, node, parent) {\n    const siblings = parent ? parent.children : undefined;\n    // Generate a body row when without parent.\n    const rowIndex = siblings ? siblings.indexOf(node) : 1;\n    const tagName = rowIndex === 0 ? \"th\" : \"td\";\n    // To do: option to use `style`?\n    const align = parent && parent.type === \"table\" ? parent.align : undefined;\n    const length = align ? align.length : node.children.length;\n    let cellIndex = -1;\n    /** @type {Array<ElementContent>} */ const cells = [];\n    while(++cellIndex < length){\n        // Note: can also be undefined.\n        const cell = node.children[cellIndex];\n        /** @type {Properties} */ const properties = {};\n        const alignValue = align ? align[cellIndex] : undefined;\n        if (alignValue) {\n            properties.align = alignValue;\n        }\n        /** @type {Element} */ let result = {\n            type: \"element\",\n            tagName,\n            properties,\n            children: []\n        };\n        if (cell) {\n            result.children = state.all(cell);\n            state.patch(cell, result);\n            result = state.applyData(cell, result);\n        }\n        cells.push(result);\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"tr\",\n        properties: {},\n        children: state.wrap(cells, true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js":
/*!***************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/table.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   table: () => (/* binding */ table)\n/* harmony export */ });\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function table(state, node) {\n    const rows = state.all(node);\n    const firstRow = rows.shift();\n    /** @type {Array<Element>} */ const tableContent = [];\n    if (firstRow) {\n        /** @type {Element} */ const head = {\n            type: \"element\",\n            tagName: \"thead\",\n            properties: {},\n            children: state.wrap([\n                firstRow\n            ], true)\n        };\n        state.patch(node.children[0], head);\n        tableContent.push(head);\n    }\n    if (rows.length > 0) {\n        /** @type {Element} */ const body = {\n            type: \"element\",\n            tagName: \"tbody\",\n            properties: {},\n            children: state.wrap(rows, true)\n        };\n        const start = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_0__.pointStart)(node.children[1]);\n        const end = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_0__.pointEnd)(node.children[node.children.length - 1]);\n        if (start && end) body.position = {\n            start,\n            end\n        };\n        tableContent.push(body);\n    }\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"table\",\n        properties: {},\n        children: state.wrap(tableContent, true)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/table.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js":
/*!**************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/text.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var trim_lines__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! trim-lines */ \"(ssr)/./node_modules/trim-lines/index.js\");\n/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */ \n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastElement | HastText}\n *   hast node.\n */ function text(state, node) {\n    /** @type {HastText} */ const result = {\n        type: \"text\",\n        value: (0,trim_lines__WEBPACK_IMPORTED_MODULE_0__.trimLines)(String(node.value))\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7Ozs7O0NBS0MsR0FFbUM7QUFFcEM7Ozs7Ozs7OztDQVNDLEdBQ00sU0FBU0MsS0FBS0MsS0FBSyxFQUFFQyxJQUFJO0lBQzlCLHFCQUFxQixHQUNyQixNQUFNQyxTQUFTO1FBQUNDLE1BQU07UUFBUUMsT0FBT04scURBQVNBLENBQUNPLE9BQU9KLEtBQUtHLEtBQUs7SUFBRTtJQUNsRUosTUFBTU0sS0FBSyxDQUFDTCxNQUFNQztJQUNsQixPQUFPRixNQUFNTyxTQUFTLENBQUNOLE1BQU1DO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc3lzdGVtLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8taGFzdC9saWIvaGFuZGxlcnMvdGV4dC5qcz9jNWIwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnaGFzdCcpLkVsZW1lbnR9IEhhc3RFbGVtZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuVGV4dH0gSGFzdFRleHRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuVGV4dH0gTWRhc3RUZXh0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbmltcG9ydCB7dHJpbUxpbmVzfSBmcm9tICd0cmltLWxpbmVzJ1xuXG4vKipcbiAqIFR1cm4gYW4gbWRhc3QgYHRleHRgIG5vZGUgaW50byBoYXN0LlxuICpcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZC5cbiAqIEBwYXJhbSB7TWRhc3RUZXh0fSBub2RlXG4gKiAgIG1kYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7SGFzdEVsZW1lbnQgfCBIYXN0VGV4dH1cbiAqICAgaGFzdCBub2RlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gdGV4dChzdGF0ZSwgbm9kZSkge1xuICAvKiogQHR5cGUge0hhc3RUZXh0fSAqL1xuICBjb25zdCByZXN1bHQgPSB7dHlwZTogJ3RleHQnLCB2YWx1ZTogdHJpbUxpbmVzKFN0cmluZyhub2RlLnZhbHVlKSl9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsidHJpbUxpbmVzIiwidGV4dCIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJ2YWx1ZSIsIlN0cmluZyIsInBhdGNoIiwiYXBwbHlEYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */ function thematicBreak(state, node) {\n    /** @type {Element} */ const result = {\n        type: \"element\",\n        tagName: \"hr\",\n        properties: {},\n        children: []\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1oYXN0L2xpYi9oYW5kbGVycy90aGVtYXRpYy1icmVhay5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Q0FJQyxHQUVELG1EQUFtRDtBQUNuRDtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLFNBQVNBLGNBQWNDLEtBQUssRUFBRUMsSUFBSTtJQUN2QyxvQkFBb0IsR0FDcEIsTUFBTUMsU0FBUztRQUNiQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsWUFBWSxDQUFDO1FBQ2JDLFVBQVUsRUFBRTtJQUNkO0lBQ0FOLE1BQU1PLEtBQUssQ0FBQ04sTUFBTUM7SUFDbEIsT0FBT0YsTUFBTVEsU0FBUyxDQUFDUCxNQUFNQztBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXN5c3RlbS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLWhhc3QvbGliL2hhbmRsZXJzL3RoZW1hdGljLWJyZWFrLmpzP2Y4MzciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5UaGVtYXRpY0JyZWFrfSBUaGVtYXRpY0JyZWFrXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9zdGF0ZS5qcycpLlN0YXRlfSBTdGF0ZVxuICovXG5cbi8vIE1ha2UgVlMgQ29kZSBzaG93IHJlZmVyZW5jZXMgdG8gdGhlIGFib3ZlIHR5cGVzLlxuJydcblxuLyoqXG4gKiBUdXJuIGFuIG1kYXN0IGB0aGVtYXRpY0JyZWFrYCBub2RlIGludG8gaGFzdC5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQuXG4gKiBAcGFyYW0ge1RoZW1hdGljQnJlYWt9IG5vZGVcbiAqICAgbWRhc3Qgbm9kZS5cbiAqIEByZXR1cm5zIHtFbGVtZW50fVxuICogICBoYXN0IG5vZGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0aGVtYXRpY0JyZWFrKHN0YXRlLCBub2RlKSB7XG4gIC8qKiBAdHlwZSB7RWxlbWVudH0gKi9cbiAgY29uc3QgcmVzdWx0ID0ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICB0YWdOYW1lOiAnaHInLFxuICAgIHByb3BlcnRpZXM6IHt9LFxuICAgIGNoaWxkcmVuOiBbXVxuICB9XG4gIHN0YXRlLnBhdGNoKG5vZGUsIHJlc3VsdClcbiAgcmV0dXJuIHN0YXRlLmFwcGx5RGF0YShub2RlLCByZXN1bHQpXG59XG4iXSwibmFtZXMiOlsidGhlbWF0aWNCcmVhayIsInN0YXRlIiwibm9kZSIsInJlc3VsdCIsInR5cGUiLCJ0YWdOYW1lIiwicHJvcGVydGllcyIsImNoaWxkcmVuIiwicGF0Y2giLCJhcHBseURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toHast: () => (/* binding */ toHast)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var _footer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./footer.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/footer.js\");\n/* harmony import */ var _state_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/state.js\");\n/**\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('./state.js').Options} Options\n */ \n\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {HastNodes}\n *   hast tree.\n */ function toHast(tree, options) {\n    const state = (0,_state_js__WEBPACK_IMPORTED_MODULE_0__.createState)(tree, options);\n    const node = state.one(tree, undefined);\n    const foot = (0,_footer_js__WEBPACK_IMPORTED_MODULE_1__.footer)(state);\n    /** @type {HastNodes} */ const result = Array.isArray(node) ? {\n        type: \"root\",\n        children: node\n    } : node || {\n        type: \"root\",\n        children: []\n    };\n    if (foot) {\n        // If there’s a footer, there were definitions, meaning block\n        // content.\n        // So `result` is a parent node.\n        (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(\"children\" in result);\n        result.children.push({\n            type: \"text\",\n            value: \"\\n\"\n        }, foot);\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js":
/*!*******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/revert.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   revert: () => (/* binding */ revert)\n/* harmony export */ });\n/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Nodes} Nodes\n * @typedef {import('mdast').Reference} Reference\n *\n * @typedef {import('./state.js').State} State\n */ // Make VS Code show references to the above types.\n\"\";\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Extract<Nodes, Reference>} node\n *   Reference node (image, link).\n * @returns {Array<ElementContent>}\n *   hast content.\n */ function revert(state, node) {\n    const subtype = node.referenceType;\n    let suffix = \"]\";\n    if (subtype === \"collapsed\") {\n        suffix += \"[]\";\n    } else if (subtype === \"full\") {\n        suffix += \"[\" + (node.label || node.identifier) + \"]\";\n    }\n    if (node.type === \"imageReference\") {\n        return [\n            {\n                type: \"text\",\n                value: \"![\" + node.alt + suffix\n            }\n        ];\n    }\n    const contents = state.all(node);\n    const head = contents[0];\n    if (head && head.type === \"text\") {\n        head.value = \"[\" + head.value;\n    } else {\n        contents.unshift({\n            type: \"text\",\n            value: \"[\"\n        });\n    }\n    const tail = contents[contents.length - 1];\n    if (tail && tail.type === \"text\") {\n        tail.value += suffix;\n    } else {\n        contents.push({\n            type: \"text\",\n            value: suffix\n        });\n    }\n    return contents;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/revert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-hast/lib/state.js":
/*!******************************************************!*\
  !*** ./node_modules/mdast-util-to-hast/lib/state.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createState: () => (/* binding */ createState),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var _handlers_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handlers/index.js */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/handlers/index.js\");\n/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').RootContent} HastRootContent\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('mdast').Parents} MdastParents\n *\n * @typedef {import('vfile').VFile} VFile\n *\n * @typedef {import('./footer.js').FootnoteBackContentTemplate} FootnoteBackContentTemplate\n * @typedef {import('./footer.js').FootnoteBackLabelTemplate} FootnoteBackLabelTemplate\n */ /**\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<HastElementContent> | HastElementContent | undefined}\n *   hast node.\n *\n * @typedef {Partial<Record<MdastNodes['type'], Handler>>} Handlers\n *   Handle nodes.\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree (default:\n *   `false`).\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` property on footnotes to prevent them from\n *   *clobbering* (default: `'user-content-'`).\n *\n *   Pass `''` for trusted markdown and when you are careful with\n *   polyfilling.\n *   You could pass a different prefix.\n *\n *   DOM clobbering is this:\n *\n *   ```html\n *   <p id=\"x\"></p>\n *   <script>alert(x) // `x` now refers to the `p#x` DOM element</script>\n *   ```\n *\n *   The above example shows that elements are made available by browsers, by\n *   their ID, on the `window` object.\n *   This is a security risk because you might be expecting some other variable\n *   at that place.\n *   It can also break polyfills.\n *   Using a prefix solves these problems.\n * @property {VFile | null | undefined} [file]\n *   Corresponding virtual file representing the input document (optional).\n * @property {FootnoteBackContentTemplate | string | null | undefined} [footnoteBackContent]\n *   Content of the backreference back to references (default: `defaultFootnoteBackContent`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackContent(_, rereferenceIndex) {\n *     const result = [{type: 'text', value: '↩'}]\n *\n *     if (rereferenceIndex > 1) {\n *       result.push({\n *         type: 'element',\n *         tagName: 'sup',\n *         properties: {},\n *         children: [{type: 'text', value: String(rereferenceIndex)}]\n *       })\n *     }\n *\n *     return result\n *   }\n *   ```\n *\n *   This content is used in the `a` element of each backreference (the `↩`\n *   links).\n * @property {FootnoteBackLabelTemplate | string | null | undefined} [footnoteBackLabel]\n *   Label to describe the backreference back to references (default:\n *   `defaultFootnoteBackLabel`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n *    return (\n *      'Back to reference ' +\n *      (referenceIndex + 1) +\n *      (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n *    )\n *   }\n *   ```\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is used in the `ariaLabel` property on each backreference\n *   (the `↩` links).\n *   It affects users of assistive technology.\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Textual label to use for the footnotes section (default: `'Footnotes'`).\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (default: `{className:\n *   ['sr-only']}`).\n *\n *   Change it to show the label and add other properties.\n *\n *   This label is typically hidden visually (assuming an `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass an empty string.\n *   You can also add different properties.\n *\n *   > **Note**: `id: 'footnote-label'` is always added, because footnote\n *   > calls use it with `aria-describedby` to provide an accessible label.\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   HTML tag name to use for the footnote label element (default: `'h2'`).\n *\n *   Change it to match your document structure.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes (optional).\n * @property {Array<MdastNodes['type']> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed)\n *   (optional).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes (optional).\n *\n * @typedef State\n *   Info passed around.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => HastElement | Type} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {Map<string, MdastDefinition>} definitionById\n *   Definitions by their identifier.\n * @property {Map<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Map<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {(node: MdastNodes, parent: MdastParents | undefined) => Array<HastElementContent> | HastElementContent | undefined} one\n *   Transform an mdast node to hast.\n * @property {Options} options\n *   Configuration.\n * @property {(from: MdastNodes, node: HastNodes) => undefined} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastRootContent>(nodes: Array<Type>, loose?: boolean | undefined) => Array<HastText | Type>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n */ \n\n\n\nconst own = {}.hasOwnProperty;\n/** @type {Options} */ const emptyOptions = {};\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {State}\n *   `state` function.\n */ function createState(tree, options) {\n    const settings = options || emptyOptions;\n    /** @type {Map<string, MdastDefinition>} */ const definitionById = new Map();\n    /** @type {Map<string, MdastFootnoteDefinition>} */ const footnoteById = new Map();\n    /** @type {Map<string, number>} */ const footnoteCounts = new Map();\n    /** @type {Handlers} */ // @ts-expect-error: the root handler returns a root.\n    // Hard to type.\n    const handlers = {\n        ..._handlers_index_js__WEBPACK_IMPORTED_MODULE_0__.handlers,\n        ...settings.handlers\n    };\n    /** @type {State} */ const state = {\n        all,\n        applyData,\n        definitionById,\n        footnoteById,\n        footnoteCounts,\n        footnoteOrder: [],\n        handlers,\n        one,\n        options: settings,\n        patch,\n        wrap\n    };\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, function(node) {\n        if (node.type === \"definition\" || node.type === \"footnoteDefinition\") {\n            const map = node.type === \"definition\" ? definitionById : footnoteById;\n            const id = String(node.identifier).toUpperCase();\n            // Mimick CM behavior of link definitions.\n            // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/9032189/lib/index.js#L20-L21>.\n            if (!map.has(id)) {\n                // @ts-expect-error: node type matches map.\n                map.set(id, node);\n            }\n        }\n    });\n    return state;\n    /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {Array<HastElementContent> | HastElementContent | undefined}\n   *   Resulting hast node.\n   */ function one(node, parent) {\n        const type = node.type;\n        const handle = state.handlers[type];\n        if (own.call(state.handlers, type) && handle) {\n            return handle(state, node, parent);\n        }\n        if (state.options.passThrough && state.options.passThrough.includes(type)) {\n            if (\"children\" in node) {\n                const { children, ...shallow } = node;\n                const result = (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(shallow);\n                // @ts-expect-error: TS doesn’t understand…\n                result.children = state.all(node);\n                // @ts-expect-error: TS doesn’t understand…\n                return result;\n            }\n            // @ts-expect-error: it’s custom.\n            return (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node);\n        }\n        const unknown = state.options.unknownHandler || defaultUnknownHandler;\n        return unknown(state, node, parent);\n    }\n    /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */ function all(parent) {\n        /** @type {Array<HastElementContent>} */ const values = [];\n        if (\"children\" in parent) {\n            const nodes = parent.children;\n            let index = -1;\n            while(++index < nodes.length){\n                const result = state.one(nodes[index], parent);\n                // To do: see if we van clean this? Can we merge texts?\n                if (result) {\n                    if (index && nodes[index - 1].type === \"break\") {\n                        if (!Array.isArray(result) && result.type === \"text\") {\n                            result.value = trimMarkdownSpaceStart(result.value);\n                        }\n                        if (!Array.isArray(result) && result.type === \"element\") {\n                            const head = result.children[0];\n                            if (head && head.type === \"text\") {\n                                head.value = trimMarkdownSpaceStart(head.value);\n                            }\n                        }\n                    }\n                    if (Array.isArray(result)) {\n                        values.push(...result);\n                    } else {\n                        values.push(result);\n                    }\n                }\n            }\n        }\n        return values;\n    }\n}\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */ function patch(from, to) {\n    if (from.position) to.position = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_3__.position)(from);\n}\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {HastElement | Type}\n *   Nothing.\n */ function applyData(from, to) {\n    /** @type {HastElement | Type} */ let result = to;\n    // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n    if (from && from.data) {\n        const hName = from.data.hName;\n        const hChildren = from.data.hChildren;\n        const hProperties = from.data.hProperties;\n        if (typeof hName === \"string\") {\n            // Transforming the node resulted in an element with a different name\n            // than wanted:\n            if (result.type === \"element\") {\n                result.tagName = hName;\n            } else {\n                /** @type {Array<HastElementContent>} */ // @ts-expect-error: assume no doctypes in `root`.\n                const children = \"children\" in result ? result.children : [\n                    result\n                ];\n                result = {\n                    type: \"element\",\n                    tagName: hName,\n                    properties: {},\n                    children\n                };\n            }\n        }\n        if (result.type === \"element\" && hProperties) {\n            Object.assign(result.properties, (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(hProperties));\n        }\n        if (\"children\" in result && result.children && hChildren !== null && hChildren !== undefined) {\n            result.children = hChildren;\n        }\n    }\n    return result;\n}\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastElement | HastText}\n *   Resulting hast node.\n */ function defaultUnknownHandler(state, node) {\n    const data = node.data || {};\n    /** @type {HastElement | HastText} */ const result = \"value\" in node && !(own.call(data, \"hProperties\") || own.call(data, \"hChildren\")) ? {\n        type: \"text\",\n        value: node.value\n    } : {\n        type: \"element\",\n        tagName: \"div\",\n        properties: {},\n        children: state.all(node)\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n}\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastRootContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | undefined} [loose=false]\n *   Whether to add line endings at start and end (default: `false`).\n * @returns {Array<HastText | Type>}\n *   Wrapped nodes.\n */ function wrap(nodes, loose) {\n    /** @type {Array<HastText | Type>} */ const result = [];\n    let index = -1;\n    if (loose) {\n        result.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n    }\n    while(++index < nodes.length){\n        if (index) result.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n        result.push(nodes[index]);\n    }\n    if (loose && nodes.length > 0) {\n        result.push({\n            type: \"text\",\n            value: \"\\n\"\n        });\n    }\n    return result;\n}\n/**\n * Trim spaces and tabs at the start of `value`.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Result.\n */ function trimMarkdownSpaceStart(value) {\n    let index = 0;\n    let code = value.charCodeAt(index);\n    while(code === 9 || code === 32){\n        index++;\n        code = value.charCodeAt(index);\n    }\n    return value.slice(index);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-hast/lib/state.js\n");

/***/ })

};
;