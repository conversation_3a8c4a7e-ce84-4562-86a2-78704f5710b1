/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app-pages-internals"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/hooks-server-context.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DYNAMIC_ERROR_CODE: function() {\n        return DYNAMIC_ERROR_CODE;\n    },\n    DynamicServerError: function() {\n        return DynamicServerError;\n    }\n});\nconst DYNAMIC_ERROR_CODE = \"DYNAMIC_SERVER_USAGE\";\nclass DynamicServerError extends Error {\n    constructor(type){\n        super(\"Dynamic server usage: \" + type);\n        this.digest = DYNAMIC_ERROR_CODE;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hooks-server-context.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/components/layout-router.js ***!
  \*******************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return OuterLayoutRouter;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _fetchserverresponse = __webpack_require__(/*! ./router-reducer/fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _infinitepromise = __webpack_require__(/*! ./infinite-promise */ \"(app-pages-browser)/./node_modules/next/dist/client/components/infinite-promise.js\");\nconst _errorboundary = __webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nconst _matchsegments = __webpack_require__(/*! ./match-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/match-segments.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ../../shared/lib/router/utils/handle-smooth-scroll */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _notfoundboundary = __webpack_require__(/*! ./not-found-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js\");\nconst _getsegmentvalue = __webpack_require__(/*! ./router-reducer/reducers/get-segment-value */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/get-segment-value.js\");\nconst _createroutercachekey = __webpack_require__(/*! ./router-reducer/create-router-cache-key */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-router-cache-key.js\");\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */ function walkAddRefetch(segmentPathToWalk, treeToRecreate) {\n    if (segmentPathToWalk) {\n        const [segment, parallelRouteKey] = segmentPathToWalk;\n        const isLast = segmentPathToWalk.length === 2;\n        if ((0, _matchsegments.matchSegment)(treeToRecreate[0], segment)) {\n            if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n                if (isLast) {\n                    const subTree = walkAddRefetch(undefined, treeToRecreate[1][parallelRouteKey]);\n                    return [\n                        treeToRecreate[0],\n                        {\n                            ...treeToRecreate[1],\n                            [parallelRouteKey]: [\n                                subTree[0],\n                                subTree[1],\n                                subTree[2],\n                                \"refetch\"\n                            ]\n                        }\n                    ];\n                }\n                return [\n                    treeToRecreate[0],\n                    {\n                        ...treeToRecreate[1],\n                        [parallelRouteKey]: walkAddRefetch(segmentPathToWalk.slice(2), treeToRecreate[1][parallelRouteKey])\n                    }\n                ];\n            }\n        }\n    }\n    return treeToRecreate;\n}\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */ function findDOMNode(instance) {\n    // Tree-shake for server bundle\n    if (false) {}\n    // Only apply strict mode warning when not in production\n    if (true) {\n        const originalConsoleError = console.error;\n        try {\n            console.error = function() {\n                for(var _len = arguments.length, messages = new Array(_len), _key = 0; _key < _len; _key++){\n                    messages[_key] = arguments[_key];\n                }\n                // Ignore strict mode warning for the findDomNode call below\n                if (!messages[0].includes(\"Warning: %s is deprecated in StrictMode.\")) {\n                    originalConsoleError(...messages);\n                }\n            };\n            return _reactdom.default.findDOMNode(instance);\n        } finally{\n            console.error = originalConsoleError;\n        }\n    }\n    return _reactdom.default.findDOMNode(instance);\n}\nconst rectProperties = [\n    \"bottom\",\n    \"height\",\n    \"left\",\n    \"right\",\n    \"top\",\n    \"width\",\n    \"x\",\n    \"y\"\n];\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */ function shouldSkipElement(element) {\n    // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n    // and will result in a situation we bail on scroll because of something like a fixed nav,\n    // even though the actual page content is offscreen\n    if ([\n        \"sticky\",\n        \"fixed\"\n    ].includes(getComputedStyle(element).position)) {\n        if (true) {\n            console.warn(\"Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:\", element);\n        }\n        return true;\n    }\n    // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n    // because `offsetParent` doesn't consider document/body\n    const rect = element.getBoundingClientRect();\n    return rectProperties.every((item)=>rect[item] === 0);\n}\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */ function topOfElementInViewport(element, viewportHeight) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.top <= viewportHeight;\n}\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */ function getHashFragmentDomNode(hashFragment) {\n    // If the hash fragment is `top` the page has to scroll to the top of the page.\n    if (hashFragment === \"top\") {\n        return document.body;\n    }\n    var _document_getElementById;\n    // If the hash fragment is an id, the page has to scroll to the element with that id.\n    return (_document_getElementById = document.getElementById(hashFragment)) != null ? _document_getElementById : document.getElementsByName(hashFragment)[0];\n}\nclass InnerScrollAndFocusHandler extends _react.default.Component {\n    componentDidMount() {\n        this.handlePotentialScroll();\n    }\n    componentDidUpdate() {\n        // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n        if (this.props.focusAndScrollRef.apply) {\n            this.handlePotentialScroll();\n        }\n    }\n    render() {\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args);\n        this.handlePotentialScroll = ()=>{\n            // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n            const { focusAndScrollRef, segmentPath } = this.props;\n            if (focusAndScrollRef.apply) {\n                // segmentPaths is an array of segment paths that should be scrolled to\n                // if the current segment path is not in the array, the scroll is not applied\n                // unless the array is empty, in which case the scroll is always applied\n                if (focusAndScrollRef.segmentPaths.length !== 0 && !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath)=>segmentPath.every((segment, index)=>(0, _matchsegments.matchSegment)(segment, scrollRefSegmentPath[index])))) {\n                    return;\n                }\n                let domNode = null;\n                const hashFragment = focusAndScrollRef.hashFragment;\n                if (hashFragment) {\n                    domNode = getHashFragmentDomNode(hashFragment);\n                }\n                // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n                // This already caused a bug where the first child was a <link/> in head.\n                if (!domNode) {\n                    domNode = findDOMNode(this);\n                }\n                // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n                if (!(domNode instanceof Element)) {\n                    return;\n                }\n                // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n                // If the element is skipped, try to select the next sibling and try again.\n                while(!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)){\n                    // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n                    if (domNode.nextElementSibling === null) {\n                        return;\n                    }\n                    domNode = domNode.nextElementSibling;\n                }\n                // State is mutated to ensure that the focus and scroll is applied only once.\n                focusAndScrollRef.apply = false;\n                focusAndScrollRef.hashFragment = null;\n                focusAndScrollRef.segmentPaths = [];\n                (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n                    // In case of hash scroll, we only need to scroll the element into view\n                    if (hashFragment) {\n                        domNode.scrollIntoView();\n                        return;\n                    }\n                    // Store the current viewport height because reading `clientHeight` causes a reflow,\n                    // and it won't change during this function.\n                    const htmlElement = document.documentElement;\n                    const viewportHeight = htmlElement.clientHeight;\n                    // If the element's top edge is already in the viewport, exit early.\n                    if (topOfElementInViewport(domNode, viewportHeight)) {\n                        return;\n                    }\n                    // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n                    // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n                    // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n                    // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n                    htmlElement.scrollTop = 0;\n                    // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n                    if (!topOfElementInViewport(domNode, viewportHeight)) {\n                        domNode.scrollIntoView();\n                    }\n                }, {\n                    // We will force layout by querying domNode position\n                    dontForceLayout: true,\n                    onlyHashChange: focusAndScrollRef.onlyHashChange\n                });\n                // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n                focusAndScrollRef.onlyHashChange = false;\n                // Set focus on the element\n                domNode.focus();\n            }\n        };\n    }\n}\nfunction ScrollAndFocusHandler(param) {\n    let { segmentPath, children } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant global layout router not mounted\");\n    }\n    return /*#__PURE__*/ _react.default.createElement(InnerScrollAndFocusHandler, {\n        segmentPath: segmentPath,\n        focusAndScrollRef: context.focusAndScrollRef\n    }, children);\n}\n_c = ScrollAndFocusHandler;\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */ function InnerLayoutRouter(param) {\n    let { parallelRouterKey, url, childNodes, initialChildNode, segmentPath, tree, // isActive,\n    cacheKey } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.GlobalLayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant global layout router not mounted\");\n    }\n    const { buildId, changeByServerResponse, tree: fullTree } = context;\n    // Read segment path from the parallel router cache node.\n    let childNode = childNodes.get(cacheKey);\n    // If initialChildNode is available this means it's the Flight / SSR case.\n    // TODO: `null` is a valid React Node, so technically we should use some other\n    // value besides `null` to indicate that the tree is partial. However, we're\n    // about to remove all the cases that lead to a partial tree, so this soon\n    // won't be an issue.\n    if (initialChildNode !== null) {\n        if (!childNode) {\n            // Add the segment's subTreeData to the cache.\n            // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n            // TODO: We should seed all the CacheNodes as soon as the Flight payload\n            // is received. We already collect them eagerly on the server, so we\n            // shouldn't need to wait until the render phase to write them into\n            // the cache. Requires refactoring the Flight response type. Then we can\n            // delete this code.\n            childNode = {\n                status: _approutercontextsharedruntime.CacheStates.READY,\n                data: null,\n                subTreeData: initialChildNode,\n                parallelRoutes: new Map()\n            };\n            childNodes.set(cacheKey, childNode);\n        } else {\n            if (childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED) {\n                // @ts-expect-error we're changing it's type!\n                childNode.status = _approutercontextsharedruntime.CacheStates.READY;\n                // @ts-expect-error\n                childNode.subTreeData = initialChildNode;\n            }\n        }\n    }\n    // When childNode is not available during rendering client-side we need to fetch it from the server.\n    if (!childNode || childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED) {\n        /**\n     * Router state with refetch marker added\n     */ // TODO-APP: remove ''\n        const refetchTree = walkAddRefetch([\n            \"\",\n            ...segmentPath\n        ], fullTree);\n        childNode = {\n            status: _approutercontextsharedruntime.CacheStates.DATA_FETCH,\n            data: (0, _fetchserverresponse.fetchServerResponse)(new URL(url, location.origin), refetchTree, context.nextUrl, buildId),\n            subTreeData: null,\n            head: childNode && childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED ? childNode.head : undefined,\n            parallelRoutes: childNode && childNode.status === _approutercontextsharedruntime.CacheStates.LAZY_INITIALIZED ? childNode.parallelRoutes : new Map()\n        };\n        /**\n     * Flight data fetch kicked off during render and put into the cache.\n     */ childNodes.set(cacheKey, childNode);\n    }\n    // This case should never happen so it throws an error. It indicates there's a bug in the Next.js.\n    if (!childNode) {\n        throw new Error(\"Child node should always exist\");\n    }\n    // This case should never happen so it throws an error. It indicates there's a bug in the Next.js.\n    if (childNode.subTreeData && childNode.data) {\n        throw new Error(\"Child node should not have both subTreeData and data\");\n    }\n    // If cache node has a data request we have to unwrap response by `use` and update the cache.\n    if (childNode.data) {\n        /**\n     * Flight response data\n     */ // When the data has not resolved yet `use` will suspend here.\n        const [flightData, overrideCanonicalUrl] = (0, _react.use)(childNode.data);\n        // segmentPath from the server does not match the layout's segmentPath\n        childNode.data = null;\n        // setTimeout is used to start a new transition during render, this is an intentional hack around React.\n        setTimeout(()=>{\n            (0, _react.startTransition)(()=>{\n                changeByServerResponse(fullTree, flightData, overrideCanonicalUrl);\n            });\n        });\n        // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n        (0, _react.use)((0, _infinitepromise.createInfinitePromise)());\n    }\n    // If cache node has no subTreeData and no data request we have to infinitely suspend as the data will likely flow in from another place.\n    // TODO-APP: double check users can't return null in a component that will kick in here.\n    if (!childNode.subTreeData) {\n        (0, _react.use)((0, _infinitepromise.createInfinitePromise)());\n    }\n    const subtree = /*#__PURE__*/ _react.default.createElement(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n        value: {\n            tree: tree[1][parallelRouterKey],\n            childNodes: childNode.parallelRoutes,\n            // TODO-APP: overriding of url for parallel routes\n            url: url\n        }\n    }, childNode.subTreeData);\n    // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n    return subtree;\n}\n_c1 = InnerLayoutRouter;\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */ function LoadingBoundary(param) {\n    let { children, loading, loadingStyles, loadingScripts, hasLoading } = param;\n    if (hasLoading) {\n        return /*#__PURE__*/ _react.default.createElement(_react.Suspense, {\n            fallback: /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, loadingStyles, loadingScripts, loading)\n        }, children);\n    }\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, children);\n}\n_c2 = LoadingBoundary;\nfunction OuterLayoutRouter(param) {\n    let { parallelRouterKey, segmentPath, initialChildNode, childPropSegment, error, errorStyles, errorScripts, templateStyles, templateScripts, loading, loadingStyles, loadingScripts, hasLoading, template, notFound, notFoundStyles, styles } = param;\n    const context = (0, _react.useContext)(_approutercontextsharedruntime.LayoutRouterContext);\n    if (!context) {\n        throw new Error(\"invariant expected layout router to be mounted\");\n    }\n    const { childNodes, tree, url } = context;\n    // Get the current parallelRouter cache node\n    let childNodesForParallelRouter = childNodes.get(parallelRouterKey);\n    // If the parallel router cache node does not exist yet, create it.\n    // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n    if (!childNodesForParallelRouter) {\n        childNodesForParallelRouter = new Map();\n        childNodes.set(parallelRouterKey, childNodesForParallelRouter);\n    }\n    // Get the active segment in the tree\n    // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n    const treeSegment = tree[1][parallelRouterKey][0];\n    // If segment is an array it's a dynamic route and we want to read the dynamic route value as the segment to get from the cache.\n    const currentChildSegmentValue = (0, _getsegmentvalue.getSegmentValue)(treeSegment);\n    /**\n   * Decides which segments to keep rendering, all segments that are not active will be wrapped in `<Offscreen>`.\n   */ // TODO-APP: Add handling of `<Offscreen>` when it's available.\n    const preservedSegments = [\n        treeSegment\n    ];\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, styles, preservedSegments.map((preservedSegment)=>{\n        const isChildPropSegment = (0, _matchsegments.matchSegment)(preservedSegment, childPropSegment);\n        const preservedSegmentValue = (0, _getsegmentvalue.getSegmentValue)(preservedSegment);\n        const cacheKey = (0, _createroutercachekey.createRouterCacheKey)(preservedSegment);\n        return(/*\n            - Error boundary\n              - Only renders error boundary if error component is provided.\n              - Rendered for each segment to ensure they have their own error state.\n            - Loading boundary\n              - Only renders suspense boundary if loading components is provided.\n              - Rendered for each segment to ensure they have their own loading state.\n              - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n          */ /*#__PURE__*/ _react.default.createElement(_approutercontextsharedruntime.TemplateContext.Provider, {\n            key: (0, _createroutercachekey.createRouterCacheKey)(preservedSegment, true),\n            value: /*#__PURE__*/ _react.default.createElement(ScrollAndFocusHandler, {\n                segmentPath: segmentPath\n            }, /*#__PURE__*/ _react.default.createElement(_errorboundary.ErrorBoundary, {\n                errorComponent: error,\n                errorStyles: errorStyles,\n                errorScripts: errorScripts\n            }, /*#__PURE__*/ _react.default.createElement(LoadingBoundary, {\n                hasLoading: hasLoading,\n                loading: loading,\n                loadingStyles: loadingStyles,\n                loadingScripts: loadingScripts\n            }, /*#__PURE__*/ _react.default.createElement(_notfoundboundary.NotFoundBoundary, {\n                notFound: notFound,\n                notFoundStyles: notFoundStyles\n            }, /*#__PURE__*/ _react.default.createElement(_redirectboundary.RedirectBoundary, null, /*#__PURE__*/ _react.default.createElement(InnerLayoutRouter, {\n                parallelRouterKey: parallelRouterKey,\n                url: url,\n                tree: tree,\n                childNodes: childNodesForParallelRouter,\n                initialChildNode: isChildPropSegment ? initialChildNode : null,\n                segmentPath: segmentPath,\n                cacheKey: cacheKey,\n                isActive: currentChildSegmentValue === preservedSegmentValue\n            }))))))\n        }, templateStyles, templateScripts, template));\n    }));\n}\n_c3 = OuterLayoutRouter;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=layout-router.js.map\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScrollAndFocusHandler\");\n$RefreshReg$(_c1, \"InnerLayoutRouter\");\n$RefreshReg$(_c2, \"LoadingBoundary\");\n$RefreshReg$(_c3, \"OuterLayoutRouter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/maybe-postpone.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/maybe-postpone.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"maybePostpone\", ({\n    enumerable: true,\n    get: function() {\n        return maybePostpone;\n    }\n}));\nfunction maybePostpone(staticGenerationStore, reason) {\n    // If we aren't performing a static generation or we aren't using PPR then\n    // we don't need to postpone.\n    if (!staticGenerationStore.isStaticGeneration || !staticGenerationStore.experimental.ppr) {\n        return;\n    }\n    if (!staticGenerationStore.postpone) {\n        throw new Error(\"Invariant: PPR is enabled but the postpone API is unavailable\");\n    }\n    // Keep track of if the postpone API has been called.\n    staticGenerationStore.postponeWasTriggered = true;\n    staticGenerationStore.postpone(\"This page needs to bail out of prerendering at this point because it used \" + reason + \". \" + \"React throws this special object to indicate where. It should not be caught by \" + \"your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error\");\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=maybe-postpone.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/maybe-postpone.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/render-from-template-context.js ***!
  \**********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return RenderFromTemplateContext;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nfunction RenderFromTemplateContext() {\n    const children = (0, _react.useContext)(_approutercontextsharedruntime.TemplateContext);\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, children);\n}\n_c = RenderFromTemplateContext;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=render-from-template-context.js.map\nvar _c;\n$RefreshReg$(_c, \"RenderFromTemplateContext\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/searchparams-bailout-proxy.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/searchparams-bailout-proxy.js ***!
  \********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createSearchParamsBailoutProxy\", ({\n    enumerable: true,\n    get: function() {\n        return createSearchParamsBailoutProxy;\n    }\n}));\nconst _staticgenerationbailout = __webpack_require__(/*! ./static-generation-bailout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\");\nfunction createSearchParamsBailoutProxy() {\n    return new Proxy({}, {\n        get (_target, prop) {\n            // React adds some properties on the object when serializing for client components\n            if (typeof prop === \"string\") {\n                (0, _staticgenerationbailout.staticGenerationBailout)(\"searchParams.\" + prop);\n            }\n        }\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=searchparams-bailout-proxy.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/searchparams-bailout-proxy.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-async-storage.external.js ***!
  \**********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"staticGenerationAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return staticGenerationAsyncStorage;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/./node_modules/next/dist/client/components/async-local-storage.js\");\nconst staticGenerationAsyncStorage = (0, _asynclocalstorage.createAsyncLocalStorage)();\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-async-storage.external.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-bailout.js ***!
  \*******************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"staticGenerationBailout\", ({\n    enumerable: true,\n    get: function() {\n        return staticGenerationBailout;\n    }\n}));\nconst _hooksservercontext = __webpack_require__(/*! ./hooks-server-context */ \"(app-pages-browser)/./node_modules/next/dist/client/components/hooks-server-context.js\");\nconst _maybepostpone = __webpack_require__(/*! ./maybe-postpone */ \"(app-pages-browser)/./node_modules/next/dist/client/components/maybe-postpone.js\");\nconst _staticgenerationasyncstorageexternal = __webpack_require__(/*! ./static-generation-async-storage.external */ \"(shared)/./node_modules/next/dist/client/components/static-generation-async-storage.external.js\");\nclass StaticGenBailoutError extends Error {\n    constructor(...args){\n        super(...args);\n        this.code = \"NEXT_STATIC_GEN_BAILOUT\";\n    }\n}\nfunction formatErrorMessage(reason, opts) {\n    const { dynamic, link } = opts || {};\n    const suffix = link ? \" See more info here: \" + link : \"\";\n    return \"Page\" + (dynamic ? ' with `dynamic = \"' + dynamic + '\"`' : \"\") + \" couldn't be rendered statically because it used `\" + reason + \"`.\" + suffix;\n}\nconst staticGenerationBailout = (reason, opts)=>{\n    const staticGenerationStore = _staticgenerationasyncstorageexternal.staticGenerationAsyncStorage.getStore();\n    if (!staticGenerationStore) return false;\n    if (staticGenerationStore.forceStatic) {\n        return true;\n    }\n    if (staticGenerationStore.dynamicShouldError) {\n        var _opts_dynamic;\n        throw new StaticGenBailoutError(formatErrorMessage(reason, {\n            ...opts,\n            dynamic: (_opts_dynamic = opts == null ? void 0 : opts.dynamic) != null ? _opts_dynamic : \"error\"\n        }));\n    }\n    const message = formatErrorMessage(reason, {\n        ...opts,\n        // this error should be caught by Next to bail out of static generation\n        // in case it's uncaught, this link provides some additional context as to why\n        link: \"https://nextjs.org/docs/messages/dynamic-server-error\"\n    });\n    (0, _maybepostpone.maybePostpone)(staticGenerationStore, reason);\n    // As this is a bailout, we don't want to revalidate, so set the revalidate\n    // to 0.\n    staticGenerationStore.revalidate = 0;\n    if (!(opts == null ? void 0 : opts.dynamic)) {\n        // we can statically prefetch pages that opt into dynamic,\n        // but not things like headers/cookies\n        staticGenerationStore.staticPrefetchBailout = true;\n    }\n    if (staticGenerationStore.isStaticGeneration) {\n        const err = new _hooksservercontext.DynamicServerError(message);\n        staticGenerationStore.dynamicUsageDescription = reason;\n        staticGenerationStore.dynamicUsageStack = err.stack;\n        throw err;\n    }\n    return false;\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-bailout.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-bailout.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js ***!
  \*****************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return StaticGenerationSearchParamsBailoutProvider;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _searchparamsbailoutproxy = __webpack_require__(/*! ./searchparams-bailout-proxy */ \"(app-pages-browser)/./node_modules/next/dist/client/components/searchparams-bailout-proxy.js\");\nfunction StaticGenerationSearchParamsBailoutProvider(param) {\n    let { Component, propsForComponent, isStaticGeneration } = param;\n    if (isStaticGeneration) {\n        const searchParams = (0, _searchparamsbailoutproxy.createSearchParamsBailoutProxy)();\n        return /*#__PURE__*/ _react.default.createElement(Component, {\n            searchParams: searchParams,\n            ...propsForComponent\n        });\n    }\n    return /*#__PURE__*/ _react.default.createElement(Component, propsForComponent);\n}\n_c = StaticGenerationSearchParamsBailoutProvider;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=static-generation-searchparams-bailout-provider.js.map\nvar _c;\n$RefreshReg$(_c, \"StaticGenerationSearchParamsBailoutProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js ***!
  \********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleSmoothScroll\", ({\n    enumerable: true,\n    get: function() {\n        return handleSmoothScroll;\n    }\n}));\nfunction handleSmoothScroll(fn, options) {\n    if (options === void 0) options = {};\n    // if only the hash is changed, we don't need to disable smooth scrolling\n    // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n    if (options.onlyHashChange) {\n        fn();\n        return;\n    }\n    const htmlElement = document.documentElement;\n    const existing = htmlElement.style.scrollBehavior;\n    htmlElement.style.scrollBehavior = \"auto\";\n    if (!options.dontForceLayout) {\n        // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n        // Otherwise it will not pickup the change in scrollBehavior\n        // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n        htmlElement.getClientRects();\n    }\n    fn();\n    htmlElement.style.scrollBehavior = existing;\n} //# sourceMappingURL=handle-smooth-scroll.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fjinglv%2FPycharmProjects%2Fai-system%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);