"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _components_chat_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/chat/sidebar */ \"(app-pages-browser)/./src/components/chat/sidebar.tsx\");\n/* harmony import */ var _components_chat_message__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/chat/message */ \"(app-pages-browser)/./src/components/chat/message.tsx\");\n/* harmony import */ var _components_chat_chat_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/chat-input */ \"(app-pages-browser)/./src/components/chat/chat-input.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_dev_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/dev-utils */ \"(app-pages-browser)/./src/lib/dev-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 文档查看相关状态\n    const [viewingDocument, setViewingDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDocumentPanelOpen, setIsDocumentPanelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingDocument, setLoadingDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 滚动到底部\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    // 加载对话列表\n    const loadConversations = async ()=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.getConversations();\n            setConversations(data);\n        } catch (error) {\n            console.error(\"加载对话列表失败:\", error);\n        }\n    };\n    // 加载消息\n    const loadMessages = async (conversationId)=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.getConversationMessages(conversationId);\n            setMessages(data);\n        } catch (error) {\n            console.error(\"加载消息失败:\", error);\n            setMessages([]);\n        }\n    };\n    // 创建新对话\n    const handleNewConversation = async ()=>{\n        try {\n            setIsLoading(true);\n            const conversation = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.createConversation();\n            setConversations((prev)=>[\n                    conversation,\n                    ...prev\n                ]);\n            setCurrentConversation(conversation);\n            setMessages([]);\n        } catch (error) {\n            console.error(\"创建对话失败:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 选择对话\n    const handleSelectConversation = async (id)=>{\n        const conversation = conversations.find((c)=>c.id === id);\n        if (conversation) {\n            setCurrentConversation(conversation);\n            await loadMessages(id);\n        }\n    };\n    // 删除对话\n    const handleDeleteConversation = async (id)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.deleteConversation(id);\n            setConversations((prev)=>prev.filter((c)=>c.id !== id));\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n        } catch (error) {\n            console.error(\"删除对话失败:\", error);\n        }\n    };\n    // 查看文档内容\n    const handleViewDocument = async (attachmentId)=>{\n        setLoadingDocument(true);\n        try {\n            const response = await fetch(\"/api/chat/files/\".concat(attachmentId, \"/content\"));\n            if (response.ok) {\n                const documentData = await response.json();\n                setViewingDocument(documentData);\n                setIsDocumentPanelOpen(true);\n            } else {\n                console.error(\"获取文档内容失败:\", response.statusText);\n            }\n        } catch (error) {\n            console.error(\"获取文档内容失败:\", error);\n        } finally{\n            setLoadingDocument(false);\n        }\n    };\n    // 关闭文档查看面板\n    const handleCloseDocumentPanel = ()=>{\n        setIsDocumentPanelOpen(false);\n        setViewingDocument(null);\n    };\n    // 发送消息\n    const handleSendMessage = async (content, files)=>{\n        if (!currentConversation || isStreaming) return;\n        try {\n            setIsStreaming(true);\n            // 添加用户消息到界面\n            const userMessage = {\n                id: Date.now(),\n                role: \"user\",\n                content,\n                status: \"completed\",\n                created_at: new Date().toISOString(),\n                files: files === null || files === void 0 ? void 0 : files.map((file)=>({\n                        name: file.name,\n                        size: file.size,\n                        type: file.type\n                    }))\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // 创建临时助手消息\n            const tempAssistantMessage = {\n                id: Date.now() + 1,\n                role: \"assistant\",\n                content: \"\",\n                status: \"processing\",\n                created_at: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    tempAssistantMessage\n                ]);\n            setStreamingMessageId(tempAssistantMessage.id);\n            // 开始流式对话\n            let assistantContent = \"\";\n            let chunkCount = 0;\n            let lastUpdateTime = Date.now();\n            try {\n                for await (const chunk of _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.streamChat(currentConversation.id, content, files)){\n                    var _chunk_content;\n                    chunkCount++;\n                    // 调试日志\n                    console.log(\"\\uD83D\\uDCE6 收到SSE chunk:\", {\n                        type: chunk.type,\n                        content: chunk.content,\n                        contentLength: (_chunk_content = chunk.content) === null || _chunk_content === void 0 ? void 0 : _chunk_content.length,\n                        chunkCount,\n                        totalContent: assistantContent.length\n                    });\n                    if (chunk.type === \"content\" && chunk.content) {\n                        assistantContent += chunk.content;\n                        // 优化更新频率 - 避免过于频繁的重新渲染\n                        const now = Date.now();\n                        const shouldUpdate = now - lastUpdateTime > 100 || // 至少间隔100ms\n                        chunk.content.includes(\"\\n\") || // 遇到换行立即更新\n                        chunk.content.includes(\"。\") || // 遇到句号立即更新\n                        chunk.content.includes(\"！\") || // 遇到感叹号立即更新\n                        chunk.content.includes(\"？\") // 遇到问号立即更新\n                        ;\n                        if (shouldUpdate) {\n                            // 实时更新消息内容\n                            setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                        ...msg,\n                                        content: assistantContent\n                                    } : msg));\n                            lastUpdateTime = now;\n                            // 滚动到底部\n                            setTimeout(()=>scrollToBottom(), 50);\n                        }\n                    } else if (chunk.type === \"complete\") {\n                        // 确保最终内容完整更新\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                    ...msg,\n                                    id: chunk.message_id || msg.id,\n                                    status: \"completed\",\n                                    processing_time: chunk.processing_time,\n                                    content: assistantContent.trim() // 确保使用最终内容并去除多余空格\n                                } : msg));\n                        console.log(\"✅ 流式响应完成，共收到 \".concat(chunkCount, \" 个chunk，最终内容长度: \").concat(assistantContent.length));\n                        break;\n                    } else if (chunk.type === \"error\") {\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                    ...msg,\n                                    content: \"错误: \".concat(chunk.error),\n                                    status: \"failed\"\n                                } : msg));\n                        console.error(\"流式响应错误:\", chunk.error);\n                        break;\n                    }\n                }\n            } catch (streamError) {\n                console.error(\"流式连接错误:\", streamError);\n                setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                            ...msg,\n                            content: \"连接错误: \".concat(streamError),\n                            status: \"failed\"\n                        } : msg));\n            }\n            // 重新加载对话列表以更新消息计数\n            await loadConversations();\n        } catch (error) {\n            console.error(\"发送消息失败:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === streamingMessageId ? {\n                        ...msg,\n                        content: \"发送失败: \".concat(error),\n                        status: \"failed\"\n                    } : msg));\n        } finally{\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        }\n    };\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (0,_lib_dev_utils__WEBPACK_IMPORTED_MODULE_8__.initDevelopmentUtils)();\n        loadConversations();\n    }, []);\n    // 自动滚动到底部\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n                conversations: conversations,\n                currentConversationId: currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id,\n                onSelectConversation: handleSelectConversation,\n                onNewConversation: handleNewConversation,\n                onDeleteConversation: handleDeleteConversation,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col \".concat(isDocumentPanelOpen ? \"mr-96\" : \"\", \" transition-all duration-300\"),\n                children: currentConversation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200/60 p-6 bg-white/80 backdrop-blur-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-xl text-gray-900\",\n                                        children: currentConversation.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: [\n                                            messages.length,\n                                            \" 条消息\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                            className: \"flex-1 bg-gray-50/50\",\n                            children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2 text-gray-900\",\n                                            children: \"开始对话\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"发送消息开始与AI助手对话\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: [\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_message__WEBPACK_IMPORTED_MODULE_3__.Message, {\n                                            message: message,\n                                            isStreaming: isStreaming && message.id === streamingMessageId,\n                                            onViewDocument: handleViewDocument,\n                                            loadingDocument: loadingDocument\n                                        }, message.id, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 21\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_input__WEBPACK_IMPORTED_MODULE_4__.ChatInput, {\n                            onSendMessage: handleSendMessage,\n                            disabled: isStreaming,\n                            placeholder: \"输入您的消息...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeScreen, {\n                    onNewConversation: handleNewConversation\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            isDocumentPanelOpen && viewingDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DocumentPanel, {\n                document: viewingDocument,\n                onClose: handleCloseDocumentPanel\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"76CdPefeCckIIFZAxW6UQKcL7Qg=\");\n_c = HomePage;\nfunction WelcomeScreen(param) {\n    let { onNewConversation } = param;\n    const features = [\n        {\n            icon: _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"智能对话\",\n            description: \"基于先进的AI模型，提供自然流畅的对话体验\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"实时响应\",\n            description: \"支持流式输出，实时查看AI的思考过程\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"多Agent协作\",\n            description: \"集成Autogen框架，支持多智能体协同工作\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex items-center justify-center p-8 bg-gray-50/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-10 w-10 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold mb-6 gradient-text\",\n                            children: \"你好，我是 Liangxi AI\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                            children: \"心有灵犀一点通，一个功能完善的AI对话系统，具备现代化的界面设计和强大的AI对话能力\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 rounded-2xl gemini-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 text-gray-900\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                    onClick: onNewConversation,\n                    size: \"lg\",\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-full text-lg shadow-sm\",\n                    children: \"开始对话\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n            lineNumber: 366,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n        lineNumber: 365,\n        columnNumber: 5\n    }, this);\n}\n_c1 = WelcomeScreen;\nvar _c, _c1;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c1, \"WelcomeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});