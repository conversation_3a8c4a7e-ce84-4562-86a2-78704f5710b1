"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/chat/message.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/message.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Message: function() { return /* binding */ Message; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/gemini-markdown */ \"(app-pages-browser)/./src/components/ui/gemini-markdown.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Message auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Message(param) {\n    let { message, isStreaming = false } = param;\n    _s();\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    const [viewingFile, setViewingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingFile, setLoadingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleCopy = async ()=>{\n        try {\n            await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(message.content);\n        // 这里可以添加一个toast通知\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    const handleViewFile = async (attachmentId)=>{\n        setLoadingFile(attachmentId);\n        try {\n            const response = await fetch(\"/api/chat/files/\".concat(attachmentId, \"/content\"));\n            if (response.ok) {\n                const fileData = await response.json();\n                setViewingFile(fileData);\n            } else {\n                console.error(\"获取文件内容失败:\", response.statusText);\n            }\n        } catch (error) {\n            console.error(\"获取文件内容失败:\", error);\n        } finally{\n            setLoadingFile(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex gap-4 p-6 chat-message max-w-5xl mx-auto\", isUser ? \"flex-row-reverse\" : \"flex-row\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center\", isUser ? \"bg-blue-600 text-white\" : \"bg-gradient-to-br from-blue-500 to-purple-600 text-white\"),\n                        children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 19\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 40\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-1 min-w-0\", isUser ? \"text-right\" : \"text-left\"),\n                        children: [\n                            message.files && message.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mb-3 flex flex-wrap gap-2\", isUser ? \"justify-end\" : \"justify-start\"),\n                                children: message.files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 p-2 rounded-lg border max-w-xs\", isUser ? \"bg-blue-50 border-blue-200 text-blue-900\" : \"bg-gray-50 border-gray-200 text-gray-900\"),\n                                        children: [\n                                            file.type.startsWith(\"image/\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium truncate\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs opacity-70\",\n                                                        children: [\n                                                            (file.size / 1024).toFixed(1),\n                                                            \" KB\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"h-6 w-6 p-0\",\n                                                        onClick: ()=>handleViewFile(file.id),\n                                                        disabled: loadingFile === file.id,\n                                                        title: \"查看文件内容\",\n                                                        children: loadingFile === file.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    file.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"h-6 w-6 p-0\",\n                                                        onClick: ()=>window.open(file.url, \"_blank\"),\n                                                        title: \"下载文件\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(isUser ? \"bg-blue-600 text-white p-4 rounded-2xl ml-auto max-w-2xl shadow-sm inline-block\" : \"bg-transparent text-gray-900 w-full\", isStreaming && \"animate-pulse\"),\n                                children: [\n                                    isAssistant ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__.GeminiMarkdown, {\n                                        content: message.content,\n                                        className: \"leading-relaxed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"whitespace-pre-wrap leading-relaxed\",\n                                        children: message.content\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 mt-3 text-xs opacity-70\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.1s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.2s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"正在输入...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 mt-2 text-xs text-gray-500\", isUser ? \"justify-end\" : \"justify-start\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.formatTime)(message.created_at)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 11\n                                    }, this),\n                                    message.processing_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        size: 12\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            message.processing_time.toFixed(2),\n                                                            \"s\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    message.model_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: message.model_name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 hover:bg-gray-100\",\n                                        onClick: handleCopy,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 12\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 9\n                            }, this),\n                            message.status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 text-xs text-red-400 flex items-center gap-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"⚠️ 消息发送失败\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 53,\n                columnNumber: 5\n            }, this),\n            viewingFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-4xl max-h-[80vh] w-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: viewingFile.filename\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                viewingFile.file_type,\n                                                \" • \",\n                                                (viewingFile.file_size / 1024).toFixed(1),\n                                                \" KB\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setViewingFile(null),\n                                    className: \"h-8 w-8 p-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto p-4\",\n                            children: viewingFile.extracted_text ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"提取的文本内容：\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-4 whitespace-pre-wrap font-mono text-sm\",\n                                        children: viewingFile.extracted_text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-500 py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"无法预览此文件类型的内容\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-2\",\n                                        children: \"请下载文件查看完整内容\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-2 p-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setViewingFile(null),\n                                    children: \"关闭\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                viewingFile.extracted_text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(viewingFile.extracted_text),\n                                    children: \"复制内容\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Message, \"oUOcyyOcJu4NhXqsH7bPRy5FXwU=\");\n_c = Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/message.tsx\n"));

/***/ })

});