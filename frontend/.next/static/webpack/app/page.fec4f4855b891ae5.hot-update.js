"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,MessageCircle,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _components_chat_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/chat/sidebar */ \"(app-pages-browser)/./src/components/chat/sidebar.tsx\");\n/* harmony import */ var _components_chat_message__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/chat/message */ \"(app-pages-browser)/./src/components/chat/message.tsx\");\n/* harmony import */ var _components_chat_chat_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/chat-input */ \"(app-pages-browser)/./src/components/chat/chat-input.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_dev_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/dev-utils */ \"(app-pages-browser)/./src/lib/dev-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 滚动到底部\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    // 加载对话列表\n    const loadConversations = async ()=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.getConversations();\n            setConversations(data);\n        } catch (error) {\n            console.error(\"加载对话列表失败:\", error);\n        }\n    };\n    // 加载消息\n    const loadMessages = async (conversationId)=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.getConversationMessages(conversationId);\n            setMessages(data);\n        } catch (error) {\n            console.error(\"加载消息失败:\", error);\n            setMessages([]);\n        }\n    };\n    // 创建新对话\n    const handleNewConversation = async ()=>{\n        try {\n            setIsLoading(true);\n            const conversation = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.createConversation();\n            setConversations((prev)=>[\n                    conversation,\n                    ...prev\n                ]);\n            setCurrentConversation(conversation);\n            setMessages([]);\n        } catch (error) {\n            console.error(\"创建对话失败:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 选择对话\n    const handleSelectConversation = async (id)=>{\n        const conversation = conversations.find((c)=>c.id === id);\n        if (conversation) {\n            setCurrentConversation(conversation);\n            await loadMessages(id);\n        }\n    };\n    // 删除对话\n    const handleDeleteConversation = async (id)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.deleteConversation(id);\n            setConversations((prev)=>prev.filter((c)=>c.id !== id));\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n        } catch (error) {\n            console.error(\"删除对话失败:\", error);\n        }\n    };\n    // 发送消息\n    const handleSendMessage = async (content)=>{\n        if (!currentConversation || isStreaming) return;\n        try {\n            setIsStreaming(true);\n            // 添加用户消息到界面\n            const userMessage = {\n                id: Date.now(),\n                role: \"user\",\n                content,\n                status: \"completed\",\n                created_at: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // 创建临时助手消息\n            const tempAssistantMessage = {\n                id: Date.now() + 1,\n                role: \"assistant\",\n                content: \"\",\n                status: \"processing\",\n                created_at: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    tempAssistantMessage\n                ]);\n            setStreamingMessageId(tempAssistantMessage.id);\n            // 开始流式对话\n            let assistantContent = \"\";\n            let chunkCount = 0;\n            let lastUpdateTime = Date.now();\n            try {\n                for await (const chunk of _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.streamChat(currentConversation.id, content)){\n                    var _chunk_content;\n                    chunkCount++;\n                    // 调试日志\n                    console.log(\"\\uD83D\\uDCE6 收到SSE chunk:\", {\n                        type: chunk.type,\n                        content: chunk.content,\n                        contentLength: (_chunk_content = chunk.content) === null || _chunk_content === void 0 ? void 0 : _chunk_content.length,\n                        chunkCount,\n                        totalContent: assistantContent.length\n                    });\n                    if (chunk.type === \"content\" && chunk.content) {\n                        assistantContent += chunk.content;\n                        // 优化更新频率 - 避免过于频繁的重新渲染\n                        const now = Date.now();\n                        const shouldUpdate = now - lastUpdateTime > 100 || // 至少间隔100ms\n                        chunk.content.includes(\"\\n\") || // 遇到换行立即更新\n                        chunk.content.includes(\"。\") || // 遇到句号立即更新\n                        chunk.content.includes(\"！\") || // 遇到感叹号立即更新\n                        chunk.content.includes(\"？\") // 遇到问号立即更新\n                        ;\n                        if (shouldUpdate) {\n                            // 实时更新消息内容\n                            setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                        ...msg,\n                                        content: assistantContent\n                                    } : msg));\n                            lastUpdateTime = now;\n                            // 滚动到底部\n                            setTimeout(()=>scrollToBottom(), 50);\n                        }\n                    } else if (chunk.type === \"complete\") {\n                        // 确保最终内容完整更新\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                    ...msg,\n                                    id: chunk.message_id || msg.id,\n                                    status: \"completed\",\n                                    processing_time: chunk.processing_time,\n                                    content: assistantContent.trim() // 确保使用最终内容并去除多余空格\n                                } : msg));\n                        console.log(\"✅ 流式响应完成，共收到 \".concat(chunkCount, \" 个chunk，最终内容长度: \").concat(assistantContent.length));\n                        break;\n                    } else if (chunk.type === \"error\") {\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                    ...msg,\n                                    content: \"错误: \".concat(chunk.error),\n                                    status: \"failed\"\n                                } : msg));\n                        console.error(\"流式响应错误:\", chunk.error);\n                        break;\n                    }\n                }\n            } catch (streamError) {\n                console.error(\"流式连接错误:\", streamError);\n                setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                            ...msg,\n                            content: \"连接错误: \".concat(streamError),\n                            status: \"failed\"\n                        } : msg));\n            }\n            // 重新加载对话列表以更新消息计数\n            await loadConversations();\n        } catch (error) {\n            console.error(\"发送消息失败:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === streamingMessageId ? {\n                        ...msg,\n                        content: \"发送失败: \".concat(error),\n                        status: \"failed\"\n                    } : msg));\n        } finally{\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        }\n    };\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (0,_lib_dev_utils__WEBPACK_IMPORTED_MODULE_8__.initDevelopmentUtils)();\n        loadConversations();\n    }, []);\n    // 自动滚动到底部\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n                conversations: conversations,\n                currentConversationId: currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id,\n                onSelectConversation: handleSelectConversation,\n                onNewConversation: handleNewConversation,\n                onDeleteConversation: handleDeleteConversation,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: currentConversation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200/60 p-6 bg-white/80 backdrop-blur-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-xl text-gray-900\",\n                                        children: currentConversation.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: [\n                                            messages.length,\n                                            \" 条消息\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                            className: \"flex-1 bg-gray-50/50\",\n                            children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2 text-gray-900\",\n                                            children: \"开始对话\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"发送消息开始与AI助手对话\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: [\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_message__WEBPACK_IMPORTED_MODULE_3__.Message, {\n                                            message: message,\n                                            isStreaming: isStreaming && message.id === streamingMessageId\n                                        }, message.id, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 21\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_input__WEBPACK_IMPORTED_MODULE_4__.ChatInput, {\n                            onSendMessage: handleSendMessage,\n                            disabled: isStreaming,\n                            placeholder: \"输入您的消息...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeScreen, {\n                    onNewConversation: handleNewConversation\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"HDyxeYo1gra5rKcd2M9Ce5+HVqA=\");\n_c = HomePage;\nfunction WelcomeScreen(param) {\n    let { onNewConversation } = param;\n    const features = [\n        {\n            icon: _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"智能对话\",\n            description: \"基于先进的AI模型，提供自然流畅的对话体验\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"实时响应\",\n            description: \"支持流式输出，实时查看AI的思考过程\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"多Agent协作\",\n            description: \"集成Autogen框架，支持多智能体协同工作\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex items-center justify-center p-8 bg-gray-50/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_MessageCircle_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-10 w-10 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold mb-6 gradient-text\",\n                            children: \"你好，我是 Liangxi AI\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                            children: \"心有灵犀一点通，一个功能完善的AI对话系统，具备现代化的界面设计和强大的AI对话能力\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 rounded-2xl gemini-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 text-gray-900\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                    onClick: onNewConversation,\n                    size: \"lg\",\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-full text-lg shadow-sm\",\n                    children: \"开始对话\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n            lineNumber: 321,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n        lineNumber: 320,\n        columnNumber: 5\n    }, this);\n}\n_c1 = WelcomeScreen;\nvar _c, _c1;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c1, \"WelcomeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});