"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/chat/message.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/message.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Message: function() { return /* binding */ Message; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/gemini-markdown */ \"(app-pages-browser)/./src/components/ui/gemini-markdown.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Message auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Message(param) {\n    let { message, isStreaming = false } = param;\n    _s();\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    const [viewingFile, setViewingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingFile, setLoadingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleCopy = async ()=>{\n        try {\n            await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(message.content);\n        // 这里可以添加一个toast通知\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    const handleViewFile = async (attachmentId)=>{\n        setLoadingFile(attachmentId);\n        try {\n            const response = await fetch(\"/api/chat/files/\".concat(attachmentId, \"/content\"));\n            if (response.ok) {\n                const fileData = await response.json();\n                setViewingFile(fileData);\n            } else {\n                console.error(\"获取文件内容失败:\", response.statusText);\n            }\n        } catch (error) {\n            console.error(\"获取文件内容失败:\", error);\n        } finally{\n            setLoadingFile(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex gap-4 p-6 chat-message max-w-5xl mx-auto\", isUser ? \"flex-row-reverse\" : \"flex-row\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center\", isUser ? \"bg-blue-600 text-white\" : \"bg-gradient-to-br from-blue-500 to-purple-600 text-white\"),\n                        children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 19\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 40\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-1 min-w-0\", isUser ? \"text-right\" : \"text-left\"),\n                        children: [\n                            message.files && message.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mb-3 flex flex-wrap gap-2\", isUser ? \"justify-end\" : \"justify-start\"),\n                                children: message.files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 p-2 rounded-lg border max-w-xs\", isUser ? \"bg-blue-50 border-blue-200 text-blue-900\" : \"bg-gray-50 border-gray-200 text-gray-900\"),\n                                        children: [\n                                            file.type.startsWith(\"image/\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium truncate\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs opacity-70\",\n                                                        children: [\n                                                            (file.size / 1024).toFixed(1),\n                                                            \" KB\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"h-6 w-6 p-0\",\n                                                        onClick: ()=>handleViewFile(file.id),\n                                                        disabled: loadingFile === file.id,\n                                                        title: \"查看文件内容\",\n                                                        children: loadingFile === file.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    file.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"h-6 w-6 p-0\",\n                                                        onClick: ()=>window.open(file.url, \"_blank\"),\n                                                        title: \"下载文件\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(isUser ? \"bg-blue-600 text-white p-4 rounded-2xl ml-auto max-w-2xl shadow-sm inline-block\" : \"bg-transparent text-gray-900 w-full\", isStreaming && \"animate-pulse\"),\n                                children: [\n                                    isAssistant ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__.GeminiMarkdown, {\n                                        content: message.content,\n                                        className: \"leading-relaxed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"whitespace-pre-wrap leading-relaxed\",\n                                        children: message.content\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 mt-3 text-xs opacity-70\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.1s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.2s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"正在输入...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 mt-2 text-xs text-gray-500\", isUser ? \"justify-end\" : \"justify-start\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.formatTime)(message.created_at)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 11\n                                    }, this),\n                                    message.processing_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        size: 12\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            message.processing_time.toFixed(2),\n                                                            \"s\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    message.model_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: message.model_name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 hover:bg-gray-100\",\n                                        onClick: handleCopy,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 12\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 9\n                            }, this),\n                            message.status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 text-xs text-red-400 flex items-center gap-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"⚠️ 消息发送失败\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 51,\n                columnNumber: 5\n            }, this),\n            viewingFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-4xl max-h-[80vh] w-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: viewingFile.filename\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                viewingFile.file_type,\n                                                \" • \",\n                                                (viewingFile.file_size / 1024).toFixed(1),\n                                                \" KB\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setViewingFile(null),\n                                    className: \"h-8 w-8 p-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto p-4\",\n                            children: viewingFile.extracted_text ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"提取的文本内容：\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-4 whitespace-pre-wrap font-mono text-sm\",\n                                        children: viewingFile.extracted_text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-500 py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"无法预览此文件类型的内容\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-2\",\n                                        children: \"请下载文件查看完整内容\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-2 p-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setViewingFile(null),\n                                    children: \"关闭\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                viewingFile.extracted_text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(viewingFile.extracted_text),\n                                    children: \"复制内容\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Message, \"oUOcyyOcJu4NhXqsH7bPRy5FXwU=\");\n_c = Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/message.tsx\n"));

/***/ })

});