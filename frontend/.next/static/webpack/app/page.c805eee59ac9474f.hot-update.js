"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/chat/message.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/message.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Message: function() { return /* binding */ Message; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/gemini-markdown */ \"(app-pages-browser)/./src/components/ui/gemini-markdown.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Message auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Message(param) {\n    let { message, isStreaming = false } = param;\n    _s();\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    const [viewingFile, setViewingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingFile, setLoadingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleCopy = async ()=>{\n        try {\n            await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(message.content);\n        // 这里可以添加一个toast通知\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    const handleViewFile = async (attachmentId)=>{\n        setLoadingFile(attachmentId);\n        try {\n            const response = await fetch(\"/api/chat/files/\".concat(attachmentId, \"/content\"));\n            if (response.ok) {\n                const fileData = await response.json();\n                setViewingFile(fileData);\n            } else {\n                console.error(\"获取文件内容失败:\", response.statusText);\n            }\n        } catch (error) {\n            console.error(\"获取文件内容失败:\", error);\n        } finally{\n            setLoadingFile(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex gap-4 p-6 chat-message max-w-5xl mx-auto\", isUser ? \"flex-row-reverse\" : \"flex-row\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center\", isUser ? \"bg-blue-600 text-white\" : \"bg-gradient-to-br from-blue-500 to-purple-600 text-white\"),\n                children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 19\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 40\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-1 min-w-0\", isUser ? \"text-right\" : \"text-left\"),\n                children: [\n                    message.files && message.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mb-3 flex flex-wrap gap-2\", isUser ? \"justify-end\" : \"justify-start\"),\n                        children: message.files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 p-2 rounded-lg border max-w-xs\", isUser ? \"bg-blue-50 border-blue-200 text-blue-900\" : \"bg-gray-50 border-gray-200 text-gray-900\"),\n                                children: [\n                                    file.type.startsWith(\"image/\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-medium truncate\",\n                                                children: file.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-70\",\n                                                children: [\n                                                    (file.size / 1024).toFixed(1),\n                                                    \" KB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    file.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-6 w-6 p-0\",\n                                        onClick: ()=>window.open(file.url, \"_blank\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(isUser ? \"bg-blue-600 text-white p-4 rounded-2xl ml-auto max-w-2xl shadow-sm inline-block\" : \"bg-transparent text-gray-900 w-full\", isStreaming && \"animate-pulse\"),\n                        children: [\n                            isAssistant ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__.GeminiMarkdown, {\n                                content: message.content,\n                                className: \"leading-relaxed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"whitespace-pre-wrap leading-relaxed\",\n                                children: message.content\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 mt-3 text-xs opacity-70\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-current rounded-full animate-bounce\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0.1s\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0.2s\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"正在输入...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 mt-2 text-xs text-gray-500\", isUser ? \"justify-end\" : \"justify-start\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.formatTime)(message.created_at)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            message.processing_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                size: 12\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    message.processing_time.toFixed(2),\n                                                    \"s\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            message.model_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400\",\n                                        children: message.model_name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 hover:bg-gray-100\",\n                                onClick: handleCopy,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 12\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    message.status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-xs text-red-400 flex items-center gap-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"⚠️ 消息发送失败\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(Message, \"oUOcyyOcJu4NhXqsH7bPRy5FXwU=\");\n_c = Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/message.tsx\n"));

/***/ })

});