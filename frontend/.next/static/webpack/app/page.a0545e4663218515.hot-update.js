"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Copy,FileText,MessageCircle,Sparkles,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Copy,FileText,MessageCircle,Sparkles,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Copy,FileText,MessageCircle,Sparkles,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Copy,FileText,MessageCircle,Sparkles,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Copy,FileText,MessageCircle,Sparkles,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Copy,FileText,MessageCircle,Sparkles,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Copy,FileText,MessageCircle,Sparkles,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _components_chat_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/chat/sidebar */ \"(app-pages-browser)/./src/components/chat/sidebar.tsx\");\n/* harmony import */ var _components_chat_message__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/chat/message */ \"(app-pages-browser)/./src/components/chat/message.tsx\");\n/* harmony import */ var _components_chat_chat_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/chat-input */ \"(app-pages-browser)/./src/components/chat/chat-input.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_dev_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/dev-utils */ \"(app-pages-browser)/./src/lib/dev-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentConversation, setCurrentConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 文档查看相关状态\n    const [viewingDocument, setViewingDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDocumentPanelOpen, setIsDocumentPanelOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingDocument, setLoadingDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 滚动到底部\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    // 加载对话列表\n    const loadConversations = async ()=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.getConversations();\n            setConversations(data);\n        } catch (error) {\n            console.error(\"加载对话列表失败:\", error);\n        }\n    };\n    // 加载消息\n    const loadMessages = async (conversationId)=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.getConversationMessages(conversationId);\n            setMessages(data);\n        } catch (error) {\n            console.error(\"加载消息失败:\", error);\n            setMessages([]);\n        }\n    };\n    // 创建新对话\n    const handleNewConversation = async ()=>{\n        try {\n            setIsLoading(true);\n            const conversation = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.createConversation();\n            setConversations((prev)=>[\n                    conversation,\n                    ...prev\n                ]);\n            setCurrentConversation(conversation);\n            setMessages([]);\n        } catch (error) {\n            console.error(\"创建对话失败:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 选择对话\n    const handleSelectConversation = async (id)=>{\n        const conversation = conversations.find((c)=>c.id === id);\n        if (conversation) {\n            setCurrentConversation(conversation);\n            await loadMessages(id);\n        }\n    };\n    // 删除对话\n    const handleDeleteConversation = async (id)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.deleteConversation(id);\n            setConversations((prev)=>prev.filter((c)=>c.id !== id));\n            if ((currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id) === id) {\n                setCurrentConversation(null);\n                setMessages([]);\n            }\n        } catch (error) {\n            console.error(\"删除对话失败:\", error);\n        }\n    };\n    // 查看文档内容\n    const handleViewDocument = async (attachmentId)=>{\n        setLoadingDocument(true);\n        try {\n            const response = await fetch(\"/api/chat/files/\".concat(attachmentId, \"/content\"));\n            if (response.ok) {\n                const documentData = await response.json();\n                setViewingDocument(documentData);\n                setIsDocumentPanelOpen(true);\n            } else {\n                console.error(\"获取文档内容失败:\", response.statusText);\n            }\n        } catch (error) {\n            console.error(\"获取文档内容失败:\", error);\n        } finally{\n            setLoadingDocument(false);\n        }\n    };\n    // 关闭文档查看面板\n    const handleCloseDocumentPanel = ()=>{\n        setIsDocumentPanelOpen(false);\n        setViewingDocument(null);\n    };\n    // 发送消息\n    const handleSendMessage = async (content, files)=>{\n        if (!currentConversation || isStreaming) return;\n        try {\n            setIsStreaming(true);\n            // 添加用户消息到界面\n            const userMessage = {\n                id: Date.now(),\n                role: \"user\",\n                content,\n                status: \"completed\",\n                created_at: new Date().toISOString(),\n                files: files === null || files === void 0 ? void 0 : files.map((file)=>({\n                        name: file.name,\n                        size: file.size,\n                        type: file.type\n                    }))\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // 创建临时助手消息\n            const tempAssistantMessage = {\n                id: Date.now() + 1,\n                role: \"assistant\",\n                content: \"\",\n                status: \"processing\",\n                created_at: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    tempAssistantMessage\n                ]);\n            setStreamingMessageId(tempAssistantMessage.id);\n            // 开始流式对话\n            let assistantContent = \"\";\n            let chunkCount = 0;\n            let lastUpdateTime = Date.now();\n            try {\n                for await (const chunk of _lib_api__WEBPACK_IMPORTED_MODULE_7__.apiClient.streamChat(currentConversation.id, content, files)){\n                    var _chunk_content;\n                    chunkCount++;\n                    // 调试日志\n                    console.log(\"\\uD83D\\uDCE6 收到SSE chunk:\", {\n                        type: chunk.type,\n                        content: chunk.content,\n                        contentLength: (_chunk_content = chunk.content) === null || _chunk_content === void 0 ? void 0 : _chunk_content.length,\n                        chunkCount,\n                        totalContent: assistantContent.length\n                    });\n                    if (chunk.type === \"content\" && chunk.content) {\n                        assistantContent += chunk.content;\n                        // 优化更新频率 - 避免过于频繁的重新渲染\n                        const now = Date.now();\n                        const shouldUpdate = now - lastUpdateTime > 100 || // 至少间隔100ms\n                        chunk.content.includes(\"\\n\") || // 遇到换行立即更新\n                        chunk.content.includes(\"。\") || // 遇到句号立即更新\n                        chunk.content.includes(\"！\") || // 遇到感叹号立即更新\n                        chunk.content.includes(\"？\") // 遇到问号立即更新\n                        ;\n                        if (shouldUpdate) {\n                            // 实时更新消息内容\n                            setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                        ...msg,\n                                        content: assistantContent\n                                    } : msg));\n                            lastUpdateTime = now;\n                            // 滚动到底部\n                            setTimeout(()=>scrollToBottom(), 50);\n                        }\n                    } else if (chunk.type === \"complete\") {\n                        // 确保最终内容完整更新\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                    ...msg,\n                                    id: chunk.message_id || msg.id,\n                                    status: \"completed\",\n                                    processing_time: chunk.processing_time,\n                                    content: assistantContent.trim() // 确保使用最终内容并去除多余空格\n                                } : msg));\n                        console.log(\"✅ 流式响应完成，共收到 \".concat(chunkCount, \" 个chunk，最终内容长度: \").concat(assistantContent.length));\n                        break;\n                    } else if (chunk.type === \"error\") {\n                        setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                                    ...msg,\n                                    content: \"错误: \".concat(chunk.error),\n                                    status: \"failed\"\n                                } : msg));\n                        console.error(\"流式响应错误:\", chunk.error);\n                        break;\n                    }\n                }\n            } catch (streamError) {\n                console.error(\"流式连接错误:\", streamError);\n                setMessages((prev)=>prev.map((msg)=>msg.id === tempAssistantMessage.id ? {\n                            ...msg,\n                            content: \"连接错误: \".concat(streamError),\n                            status: \"failed\"\n                        } : msg));\n            }\n            // 重新加载对话列表以更新消息计数\n            await loadConversations();\n        } catch (error) {\n            console.error(\"发送消息失败:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === streamingMessageId ? {\n                        ...msg,\n                        content: \"发送失败: \".concat(error),\n                        status: \"failed\"\n                    } : msg));\n        } finally{\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        }\n    };\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (0,_lib_dev_utils__WEBPACK_IMPORTED_MODULE_8__.initDevelopmentUtils)();\n        loadConversations();\n    }, []);\n    // 自动滚动到底部\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n                conversations: conversations,\n                currentConversationId: currentConversation === null || currentConversation === void 0 ? void 0 : currentConversation.id,\n                onSelectConversation: handleSelectConversation,\n                onNewConversation: handleNewConversation,\n                onDeleteConversation: handleDeleteConversation,\n                isLoading: isLoading\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col \".concat(isDocumentPanelOpen ? \"mr-96\" : \"\", \" transition-all duration-300\"),\n                children: currentConversation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200/60 p-6 bg-white/80 backdrop-blur-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-xl text-gray-900\",\n                                        children: currentConversation.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: [\n                                            messages.length,\n                                            \" 条消息\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                            className: \"flex-1 bg-gray-50/50\",\n                            children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2 text-gray-900\",\n                                            children: \"开始对话\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"发送消息开始与AI助手对话\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-6\",\n                                children: [\n                                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_message__WEBPACK_IMPORTED_MODULE_3__.Message, {\n                                            message: message,\n                                            isStreaming: isStreaming && message.id === streamingMessageId,\n                                            onViewDocument: handleViewDocument,\n                                            loadingDocument: loadingDocument\n                                        }, message.id, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 21\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: messagesEndRef\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_input__WEBPACK_IMPORTED_MODULE_4__.ChatInput, {\n                            onSendMessage: handleSendMessage,\n                            disabled: isStreaming,\n                            placeholder: \"输入您的消息...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeScreen, {\n                    onNewConversation: handleNewConversation\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            isDocumentPanelOpen && viewingDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DocumentPanel, {\n                document: viewingDocument,\n                onClose: handleCloseDocumentPanel\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"76CdPefeCckIIFZAxW6UQKcL7Qg=\");\n_c = HomePage;\nfunction WelcomeScreen(param) {\n    let { onNewConversation } = param;\n    const features = [\n        {\n            icon: _barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"智能对话\",\n            description: \"基于先进的AI模型，提供自然流畅的对话体验\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"实时响应\",\n            description: \"支持流式输出，实时查看AI的思考过程\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"多Agent协作\",\n            description: \"集成Autogen框架，支持多智能体协同工作\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex items-center justify-center p-8 bg-gray-50/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-10 w-10 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold mb-6 gradient-text\",\n                            children: \"你好，我是 Liangxi AI\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                            children: \"心有灵犀一点通，一个功能完善的AI对话系统，具备现代化的界面设计和强大的AI对话能力\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 rounded-2xl gemini-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3 text-gray-900\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                    onClick: onNewConversation,\n                    size: \"lg\",\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-full text-lg shadow-sm\",\n                    children: \"开始对话\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n            lineNumber: 366,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n        lineNumber: 365,\n        columnNumber: 5\n    }, this);\n}\n_c1 = WelcomeScreen;\n// 文档查看面板组件\nfunction DocumentPanel(param) {\n    let { document, onClose } = param;\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed right-0 top-0 h-full w-96 bg-white border-l border-gray-200 shadow-lg z-40 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: document.filename\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    document.file_type,\n                                    \" • \",\n                                    (document.file_size / 1024).toFixed(1),\n                                    \" KB\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: onClose,\n                        className: \"h-8 w-8 p-0 ml-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: document.original_content ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap text-sm font-mono text-gray-800 leading-relaxed\",\n                            children: document.original_content\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-medium mb-2\",\n                                children: \"无法预览此文件\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"此文件类型暂不支持内容预览\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 15\n                            }, this),\n                            document.extracted_text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-800\",\n                                    children: \"已提取部分文本内容，可在聊天中查看\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 434,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 p-4 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: onClose,\n                            className: \"flex-1\",\n                            children: \"关闭\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        document.original_content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            size: \"sm\",\n                            onClick: ()=>copyToClipboard(document.original_content),\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Copy_FileText_MessageCircle_Sparkles_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 15\n                                }, this),\n                                \"复制\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/app/page.tsx\",\n        lineNumber: 414,\n        columnNumber: 5\n    }, this);\n}\n_c2 = DocumentPanel;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c1, \"WelcomeScreen\");\n$RefreshReg$(_c2, \"DocumentPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});