"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/chat/message.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/message.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Message: function() { return /* binding */ Message; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,Eye,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/gemini-markdown */ \"(app-pages-browser)/./src/components/ui/gemini-markdown.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Message auto */ \n\n\n\n\n\n\n\nfunction Message(param) {\n    let { message, isStreaming = false, onViewDocument, loadingDocument = false } = param;\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    const handleCopy = async ()=>{\n        try {\n            await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(message.content);\n        // 这里可以添加一个toast通知\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    const handleViewFile = (attachmentId)=>{\n        if (onViewDocument) {\n            onViewDocument(attachmentId);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex gap-4 p-6 chat-message max-w-5xl mx-auto\", isUser ? \"flex-row-reverse\" : \"flex-row\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center\", isUser ? \"bg-blue-600 text-white\" : \"bg-gradient-to-br from-blue-500 to-purple-600 text-white\"),\n                        children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 19\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 40\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-1 min-w-0\", isUser ? \"text-right\" : \"text-left\"),\n                        children: [\n                            message.files && message.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mb-3 flex flex-wrap gap-2\", isUser ? \"justify-end\" : \"justify-start\"),\n                                children: message.files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 p-2 rounded-lg border max-w-xs\", isUser ? \"bg-blue-50 border-blue-200 text-blue-900\" : \"bg-gray-50 border-gray-200 text-gray-900\"),\n                                        children: [\n                                            file.type.startsWith(\"image/\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium truncate\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs opacity-70\",\n                                                        children: [\n                                                            (file.size / 1024).toFixed(1),\n                                                            \" KB\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"h-6 w-6 p-0\",\n                                                        onClick: ()=>handleViewFile(file.id),\n                                                        disabled: loadingFile === file.id,\n                                                        title: \"查看文件内容\",\n                                                        children: loadingFile === file.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    file.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"h-6 w-6 p-0\",\n                                                        onClick: ()=>window.open(file.url, \"_blank\"),\n                                                        title: \"下载文件\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(isUser ? \"bg-blue-600 text-white p-4 rounded-2xl ml-auto max-w-2xl shadow-sm inline-block\" : \"bg-transparent text-gray-900 w-full\", isStreaming && \"animate-pulse\"),\n                                children: [\n                                    isAssistant ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__.GeminiMarkdown, {\n                                        content: message.content,\n                                        className: \"leading-relaxed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"whitespace-pre-wrap leading-relaxed\",\n                                        children: message.content\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 mt-3 text-xs opacity-70\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.1s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.2s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"正在输入...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 mt-2 text-xs text-gray-500\", isUser ? \"justify-end\" : \"justify-start\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.formatTime)(message.created_at)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 11\n                                    }, this),\n                                    message.processing_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        size: 12\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            message.processing_time.toFixed(2),\n                                                            \"s\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    message.model_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: message.model_name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 hover:bg-gray-100\",\n                                        onClick: handleCopy,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 12\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 9\n                            }, this),\n                            message.status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 text-xs text-red-400 flex items-center gap-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"⚠️ 消息发送失败\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 45,\n                columnNumber: 5\n            }, this),\n            viewingFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-4xl max-h-[80vh] w-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: viewingFile.filename\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                viewingFile.file_type,\n                                                \" • \",\n                                                (viewingFile.file_size / 1024).toFixed(1),\n                                                \" KB\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setViewingFile(null),\n                                    className: \"h-8 w-8 p-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto p-4\",\n                            children: viewingFile.extracted_text ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"提取的文本内容：\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-4 whitespace-pre-wrap font-mono text-sm\",\n                                        children: viewingFile.extracted_text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-500 py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_Eye_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"无法预览此文件类型的内容\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-2\",\n                                        children: \"请下载文件查看完整内容\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-2 p-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setViewingFile(null),\n                                    children: \"关闭\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                viewingFile.extracted_text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(viewingFile.extracted_text),\n                                    children: \"复制内容\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/message.tsx\n"));

/***/ })

});