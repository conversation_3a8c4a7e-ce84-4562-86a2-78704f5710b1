"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/chat/message.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/message.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Message: function() { return /* binding */ Message; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/gemini-markdown */ \"(app-pages-browser)/./src/components/ui/gemini-markdown.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Message auto */ \n\n\n\n\n\n\n\nfunction Message(param) {\n    let { message, isStreaming = false, onViewDocument, loadingDocument = false } = param;\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    const handleCopy = async ()=>{\n        try {\n            await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(message.content);\n        // 这里可以添加一个toast通知\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    const handleViewFile = (attachmentId)=>{\n        if (onViewDocument) {\n            onViewDocument(attachmentId);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex gap-4 p-6 chat-message max-w-5xl mx-auto\", isUser ? \"flex-row-reverse\" : \"flex-row\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center\", isUser ? \"bg-blue-600 text-white\" : \"bg-gradient-to-br from-blue-500 to-purple-600 text-white\"),\n                    children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 19\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 40\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-1 min-w-0\", isUser ? \"text-right\" : \"text-left\"),\n                    children: [\n                        (message.files && message.files.length > 0 || message.attachments && message.attachments.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mb-3 flex flex-wrap gap-2\", isUser ? \"justify-end\" : \"justify-start\"),\n                            children: [\n                                message.files && message.files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 p-3 rounded-lg border max-w-xs\", isUser ? \"bg-blue-50 border-blue-200 text-blue-900\" : \"bg-gray-50 border-gray-200 text-gray-900\"),\n                                        children: [\n                                            file.type.startsWith(\"image/\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium truncate\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs opacity-70\",\n                                                        children: [\n                                                            (file.size / 1024).toFixed(1),\n                                                            \" KB • 上传中...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, \"file-\".concat(index), true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)),\n                                message.attachments && message.attachments.map((attachment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 p-3 rounded-lg border max-w-xs cursor-pointer transition-all hover:shadow-md\", isUser ? \"bg-blue-50 border-blue-200 text-blue-900 hover:bg-blue-100\" : \"bg-gray-50 border-gray-200 text-gray-900 hover:bg-gray-100\", loadingDocument && \"opacity-50 cursor-wait\"),\n                                        onClick: ()=>handleViewFile(attachment.id),\n                                        title: \"点击查看文档内容\",\n                                        children: [\n                                            attachment.file_type.startsWith(\"image/\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium truncate\",\n                                                        children: attachment.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs opacity-70\",\n                                                        children: [\n                                                            (attachment.file_size / 1024).toFixed(1),\n                                                            \" KB • 点击查看\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            loadingDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border border-gray-400 border-t-transparent rounded-full animate-spin flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, this),\n                                            attachment.file_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"h-6 w-6 p-0 opacity-60 hover:opacity-100\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    window.open(attachment.file_url, \"_blank\");\n                                                },\n                                                title: \"下载文件\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, \"attachment-\".concat(attachment.id), true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(isUser ? \"bg-blue-600 text-white p-4 rounded-2xl ml-auto max-w-2xl shadow-sm inline-block\" : \"bg-transparent text-gray-900 w-full\", isStreaming && \"animate-pulse\"),\n                            children: [\n                                isAssistant ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__.GeminiMarkdown, {\n                                    content: message.content,\n                                    className: \"leading-relaxed\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"whitespace-pre-wrap leading-relaxed\",\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1 mt-3 text-xs opacity-70\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1 h-1 bg-current rounded-full animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.1s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.2s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: \"正在输入...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 mt-2 text-xs text-gray-500\", isUser ? \"justify-end\" : \"justify-start\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.formatTime)(message.created_at)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 11\n                                }, this),\n                                message.processing_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        message.processing_time.toFixed(2),\n                                                        \"s\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                message.model_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400\",\n                                            children: message.model_name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 hover:bg-gray-100\",\n                                    onClick: handleCopy,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        size: 12\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 9\n                        }, this),\n                        message.status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 text-xs text-red-400 flex items-center gap-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"⚠️ 消息发送失败\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n            lineNumber: 45,\n            columnNumber: 5\n        }, this)\n    }, void 0, false);\n}\n_c = Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/message.tsx\n"));

/***/ })

});