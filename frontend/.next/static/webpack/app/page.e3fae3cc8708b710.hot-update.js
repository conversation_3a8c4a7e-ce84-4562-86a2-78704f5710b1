"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/chat/message.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/message.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Message: function() { return /* binding */ Message; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Clock,Copy,Download,FileText,Image,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/gemini-markdown */ \"(app-pages-browser)/./src/components/ui/gemini-markdown.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Message auto */ \n\n\n\n\n\n\n\nfunction Message(param) {\n    let { message, isStreaming = false, onViewDocument, loadingDocument = false } = param;\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    const handleCopy = async ()=>{\n        try {\n            await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(message.content);\n        // 这里可以添加一个toast通知\n        } catch (error) {\n            console.error(\"复制失败:\", error);\n        }\n    };\n    const handleViewFile = (attachmentId)=>{\n        if (onViewDocument) {\n            onViewDocument(attachmentId);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex gap-4 p-6 chat-message max-w-5xl mx-auto\", isUser ? \"flex-row-reverse\" : \"flex-row\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center\", isUser ? \"bg-blue-600 text-white\" : \"bg-gradient-to-br from-blue-500 to-purple-600 text-white\"),\n                        children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 19\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 40\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-1 min-w-0\", isUser ? \"text-right\" : \"text-left\"),\n                        children: [\n                            message.files && message.files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mb-3 flex flex-wrap gap-2\", isUser ? \"justify-end\" : \"justify-start\"),\n                                children: message.files.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 p-3 rounded-lg border max-w-xs cursor-pointer transition-all hover:shadow-md\", isUser ? \"bg-blue-50 border-blue-200 text-blue-900 hover:bg-blue-100\" : \"bg-gray-50 border-gray-200 text-gray-900 hover:bg-gray-100\", loadingDocument && \"opacity-50 cursor-wait\"),\n                                        onClick: ()=>handleViewFile(file.id),\n                                        title: \"点击查看文档内容\",\n                                        children: [\n                                            file.type.startsWith(\"image/\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium truncate\",\n                                                        children: file.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs opacity-70\",\n                                                        children: [\n                                                            (file.size / 1024).toFixed(1),\n                                                            \" KB • 点击查看\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            loadingDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border border-gray-400 border-t-transparent rounded-full animate-spin flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this),\n                                            file.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                className: \"h-6 w-6 p-0 opacity-60 hover:opacity-100\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    window.open(file.url, \"_blank\");\n                                                },\n                                                title: \"下载文件\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(isUser ? \"bg-blue-600 text-white p-4 rounded-2xl ml-auto max-w-2xl shadow-sm inline-block\" : \"bg-transparent text-gray-900 w-full\", isStreaming && \"animate-pulse\"),\n                                children: [\n                                    isAssistant ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_gemini_markdown__WEBPACK_IMPORTED_MODULE_3__.GeminiMarkdown, {\n                                        content: message.content,\n                                        className: \"leading-relaxed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"whitespace-pre-wrap leading-relaxed\",\n                                        children: message.content\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 mt-3 text-xs opacity-70\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.1s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-1 h-1 bg-current rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: \"0.2s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: \"正在输入...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-2 mt-2 text-xs text-gray-500\", isUser ? \"justify-end\" : \"justify-start\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.formatTime)(message.created_at)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 11\n                                    }, this),\n                                    message.processing_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        size: 12\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            message.processing_time.toFixed(2),\n                                                            \"s\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    message.model_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: message.model_name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 hover:bg-gray-100\",\n                                        onClick: handleCopy,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            size: 12\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 9\n                            }, this),\n                            message.status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 text-xs text-red-400 flex items-center gap-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"⚠️ 消息发送失败\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 45,\n                columnNumber: 5\n            }, this),\n            viewingFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-4xl max-h-[80vh] w-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: viewingFile.filename\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                viewingFile.file_type,\n                                                \" • \",\n                                                (viewingFile.file_size / 1024).toFixed(1),\n                                                \" KB\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setViewingFile(null),\n                                    className: \"h-8 w-8 p-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto p-4\",\n                            children: viewingFile.extracted_text ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"提取的文本内容：\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-4 whitespace-pre-wrap font-mono text-sm\",\n                                        children: viewingFile.extracted_text\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-500 py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Clock_Copy_Download_FileText_Image_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"无法预览此文件类型的内容\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-2\",\n                                        children: \"请下载文件查看完整内容\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-2 p-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setViewingFile(null),\n                                    children: \"关闭\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                viewingFile.extracted_text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.copyToClipboard)(viewingFile.extracted_text),\n                                    children: \"复制内容\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/message.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/message.tsx\n"));

/***/ })

});