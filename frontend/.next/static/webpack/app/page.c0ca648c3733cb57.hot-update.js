"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/chat/sidebar.tsx":
/*!*****************************************!*\
  !*** ./src/components/chat/sidebar.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Plus,Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\nfunction Sidebar(param) {\n    let { conversations, currentConversationId, onSelectConversation, onNewConversation, onDeleteConversation, isLoading = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 h-full gemini-sidebar flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-semibold gradient-text\",\n                                children: \"Liangxi AI\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"hover:bg-gray-100/80\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5 text-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: onNewConversation,\n                        className: \"w-full bg-blue-600 hover:bg-blue-700 text-white rounded-full py-3 shadow-sm\",\n                        disabled: isLoading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this),\n                            \"新建对话\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3\",\n                    children: conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500 py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-3 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"暂无对话\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 text-gray-400\",\n                                children: '点击\"新建对话\"开始聊天'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ConversationItem, {\n                                conversation: conversation,\n                                isActive: conversation.id === currentConversationId,\n                                onSelect: ()=>onSelectConversation(conversation.id),\n                                onDelete: ()=>onDeleteConversation(conversation.id)\n                            }, conversation.id, false, {\n                                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200/60 text-xs text-gray-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"共 \",\n                                conversations.length,\n                                \" 个对话\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-400\",\n                            children: \"v1.0.0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_c = Sidebar;\nfunction ConversationItem(param) {\n    let { conversation, isActive, onSelect, onDelete } = param;\n    const handleDelete = (e)=>{\n        e.stopPropagation();\n        if (confirm(\"确定要删除这个对话吗？\")) {\n            onDelete();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group relative p-3 rounded-xl cursor-pointer transition-all duration-200\", \"hover:bg-white/60\", isActive && \"bg-white/80 shadow-sm border border-blue-200/50\"),\n        onClick: onSelect,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"font-medium text-sm truncate\", isActive ? \"text-gray-900\" : \"text-gray-700\"),\n                            children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.truncateText)(conversation.title, 30)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mt-1 text-xs text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.formatTime)(conversation.updated_at)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                conversation.message_count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                conversation.message_count,\n                                                \" 条消息\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    className: \"opacity-0 group-hover:opacity-100 transition-all duration-200 h-7 w-7 p-0 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg flex-shrink-0\",\n                    onClick: handleDelete,\n                    title: \"删除对话\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Plus_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/PycharmProjects/ai-system/frontend/src/components/chat/sidebar.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ConversationItem;\nvar _c, _c1;\n$RefreshReg$(_c, \"Sidebar\");\n$RefreshReg$(_c1, \"ConversationItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/sidebar.tsx\n"));

/***/ })

});