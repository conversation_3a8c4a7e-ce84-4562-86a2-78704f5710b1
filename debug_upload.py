#!/usr/bin/env python3
"""
调试文件上传问题的脚本
"""

import asyncio
import sys
import os
sys.path.append('backend')

from fastapi import UploadFile
from io import BytesIO
from services.file_service import file_service
from models.chat import Message, Conversation, MessageRole
from tortoise import Tortoise

async def test_file_service():
    """测试文件服务"""
    
    # 初始化数据库
    await Tortoise.init(
        db_url="sqlite://backend/db.sqlite3",
        modules={"models": ["models.chat"]}
    )
    
    try:
        # 创建测试文件内容
        test_content = """这是一个测试PDF文档。

# 测试标题

这个文档用于测试文件上传功能。

## 功能列表
1. 文件上传
2. 文本提取
3. AI分析

测试完成。
""".encode('utf-8')
        
        print(f"📝 创建测试内容: {len(test_content)} bytes")
        
        # 创建模拟的UploadFile对象
        file_obj = BytesIO(test_content)
        upload_file = UploadFile(
            filename="test_document.txt",
            file=file_obj,
            content_type="text/plain"
        )
        
        print(f"📁 创建UploadFile对象: {upload_file.filename}")
        
        # 测试验证和读取
        try:
            content, file_type = await file_service.validate_and_read_file(upload_file)
            print(f"✅ 文件验证成功: {len(content)} bytes, 类型: {file_type}")
        except Exception as e:
            print(f"❌ 文件验证失败: {e}")
            return
        
        # 测试保存文件
        try:
            file_path = await file_service.save_file_content(content, upload_file.filename, file_type)
            print(f"✅ 文件保存成功: {file_path}")
            
            # 检查文件是否真的存在
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ 文件确实存在: {file_path} (大小: {file_size} bytes)")
            else:
                print(f"❌ 文件不存在: {file_path}")
                return
                
        except Exception as e:
            print(f"❌ 文件保存失败: {e}")
            return
        
        # 测试文本提取
        try:
            extracted_text = await file_service.extract_text_content(file_path, file_type)
            if extracted_text:
                print(f"✅ 文本提取成功: {len(extracted_text)} 字符")
                print(f"📄 提取的内容预览: {extracted_text[:100]}...")
            else:
                print("⚠️ 未能提取到文本内容")
        except Exception as e:
            print(f"❌ 文本提取失败: {e}")
        
        # 测试完整的文件附件创建流程
        try:
            # 创建测试对话和消息
            conversation = await Conversation.create(title="测试对话")
            message = await Message.create(
                conversation=conversation,
                role=MessageRole.USER,
                content="测试消息"
            )
            
            print(f"✅ 创建测试消息: ID={message.id}")
            
            # 重新创建UploadFile对象（因为之前的已经被读取过了）
            file_obj2 = BytesIO(test_content)
            upload_file2 = UploadFile(
                filename="test_document.txt",
                file=file_obj2,
                content_type="text/plain"
            )
            
            # 创建文件附件
            attachment = await file_service.create_file_attachment(message, upload_file2)
            print(f"✅ 文件附件创建成功: ID={attachment.id}")
            print(f"📎 附件信息: {attachment.filename} ({attachment.file_size} bytes)")
            
        except Exception as e:
            print(f"❌ 文件附件创建失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 清理测试文件
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
                print(f"🗑️ 已清理测试文件: {file_path}")
        except:
            pass
            
    finally:
        await Tortoise.close_connections()

async def test_pdf_upload():
    """测试PDF文件上传"""
    
    # 创建一个简单的PDF内容（实际上是文本，但模拟PDF）
    pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"
    
    print(f"📝 创建模拟PDF内容: {len(pdf_content)} bytes")
    
    # 创建模拟的UploadFile对象
    file_obj = BytesIO(pdf_content)
    upload_file = UploadFile(
        filename="test_document.pdf",
        file=file_obj,
        content_type="application/pdf"
    )
    
    try:
        content, file_type = await file_service.validate_and_read_file(upload_file)
        print(f"✅ PDF验证成功: {len(content)} bytes, 类型: {file_type}")
        
        file_path = await file_service.save_file_content(content, upload_file.filename, file_type)
        print(f"✅ PDF保存成功: {file_path}")
        
        # 检查文件
        if os.path.exists(file_path):
            print(f"✅ PDF文件确实存在: {file_path}")
            
            # 清理
            os.unlink(file_path)
            print(f"🗑️ 已清理PDF文件: {file_path}")
        else:
            print(f"❌ PDF文件不存在: {file_path}")
            
    except Exception as e:
        print(f"❌ PDF测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("🚀 开始调试文件上传功能")
    print("="*50)
    
    await test_file_service()
    
    print("\n" + "="*50)
    print("🧪 测试PDF上传")
    
    await test_pdf_upload()
    
    print("\n" + "="*50)
    print("✅ 调试完成")

if __name__ == "__main__":
    asyncio.run(main())
