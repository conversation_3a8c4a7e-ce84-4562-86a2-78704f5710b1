#!/usr/bin/env python3
"""
测试文档查看功能
"""

import requests
import json

def test_document_viewer():
    """测试文档查看功能"""
    
    print("🔄 检查后端服务...")
    try:
        response = requests.get('http://localhost:8000/api/chat/health', timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端: {e}")
        return False
    
    print("\n🔄 创建对话...")
    try:
        response = requests.post(
            'http://localhost:8000/api/chat/conversations',
            json={'title': '文档查看测试'},
            timeout=10
        )
        
        conversation = response.json()
        conversation_id = conversation['id']
        print(f"✅ 对话创建成功: ID={conversation_id}")
    except Exception as e:
        print(f"❌ 创建对话异常: {e}")
        return False
    
    print("\n🔄 上传测试文档...")
    
    # 创建一个包含丰富内容的测试文档
    test_document = """# 测试文档

这是一个用于测试文档查看功能的示例文档。

## 功能说明

### 1. 文档上传
- 支持多种文件格式
- 自动提取文本内容
- 保存原始文件

### 2. 文档查看
- 点击文档可以在右侧面板查看
- 显示原始文件内容
- 支持复制功能

### 3. 代码示例

```python
def hello_world():
    print("Hello, World!")
    return "success"

# 调用函数
result = hello_world()
print(f"结果: {result}")
```

```javascript
function greet(name) {
    return `Hello, ${name}!`;
}

console.log(greet("World"));
```

### 4. 数据表格

| 功能 | 状态 | 说明 |
|------|------|------|
| 文件上传 | ✅ | 已完成 |
| 内容提取 | ✅ | 已完成 |
| 文档查看 | 🔄 | 测试中 |
| 复制功能 | 🔄 | 测试中 |

### 5. 列表示例

#### 技术栈
1. **后端**: Python + FastAPI
2. **前端**: React + TypeScript
3. **数据库**: SQLite + Tortoise ORM
4. **AI**: Autogen Framework

#### 特性
- 🚀 高性能
- 🎨 现代化UI
- 📱 响应式设计
- 🔒 安全可靠

### 6. 引用内容

> 这是一个引用示例。
> 
> 文档查看功能可以让用户方便地查看上传文档的完整内容，
> 而不需要下载文件到本地。

### 7. 总结

这个测试文档包含了各种常见的文档元素：
- 标题和子标题
- 代码块
- 表格
- 列表
- 引用
- 特殊字符和emoji

通过这个文档可以全面测试文档查看功能的显示效果。

---

**测试完成时间**: 2024年12月
**版本**: v1.0.0
"""
    
    try:
        files = {
            'files': ('test_document.md', test_document.encode('utf-8'), 'text/markdown')
        }
        
        data = {
            'message': '请分析这个测试文档',
            'conversation_id': conversation_id
        }
        
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            stream=True,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 文档上传成功")
            
            # 等待响应完成
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        if data_str == '[DONE]':
                            break
                        
                        try:
                            chunk = json.loads(data_str)
                            if chunk.get('type') == 'content':
                                print(".", end='', flush=True)
                        except:
                            pass
            
            print("\n✅ AI响应完成")
            
        else:
            print(f"❌ 文档上传失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 文档上传异常: {e}")
        return False
    
    print("\n🔄 获取附件信息...")
    try:
        response = requests.get(
            f'http://localhost:8000/api/chat/conversations/{conversation_id}/messages',
            timeout=10
        )
        
        if response.status_code == 200:
            messages = response.json()
            print(f"✅ 获取到 {len(messages)} 条消息")
            
            # 查找有附件的消息
            attachment_id = None
            for msg in messages:
                if msg.get('attachments'):
                    attachment = msg['attachments'][0]
                    attachment_id = attachment['id']
                    print(f"📎 找到附件:")
                    print(f"    ID: {attachment_id}")
                    print(f"    文件名: {attachment['filename']}")
                    print(f"    大小: {attachment['file_size']} bytes")
                    print(f"    类型: {attachment['file_type']}")
                    break
            
            if attachment_id:
                print(f"\n🔄 测试文档内容API...")
                response = requests.get(
                    f'http://localhost:8000/api/chat/files/{attachment_id}/content',
                    timeout=10
                )
                
                if response.status_code == 200:
                    file_data = response.json()
                    print("✅ 文档内容API测试成功")
                    print(f"📄 文件名: {file_data['filename']}")
                    print(f"📄 文件类型: {file_data['file_type']}")
                    print(f"📄 文件大小: {file_data['file_size']} bytes")
                    
                    if file_data.get('original_content'):
                        content_length = len(file_data['original_content'])
                        print(f"📄 原始内容长度: {content_length} 字符")
                        print(f"📄 内容预览 (前200字符):")
                        print("-" * 50)
                        print(file_data['original_content'][:200] + "...")
                        print("-" * 50)
                        
                        # 验证内容完整性
                        if "# 测试文档" in file_data['original_content']:
                            print("✅ 文档内容完整")
                        else:
                            print("⚠️ 文档内容可能不完整")
                        
                        return True
                    else:
                        print("❌ 未获取到原始内容")
                        return False
                else:
                    print(f"❌ 文档内容API失败: {response.status_code}")
                    print(f"错误信息: {response.text}")
                    return False
            else:
                print("❌ 未找到附件")
                return False
        else:
            print(f"❌ 获取消息失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_file_types():
    """测试多种文件类型"""
    
    print("\n🔄 测试多种文件类型...")
    
    # 测试文件类型
    test_files = [
        {
            'name': 'test.txt',
            'content': '这是一个纯文本文件。\n包含多行内容。\n测试完成。',
            'type': 'text/plain'
        },
        {
            'name': 'config.json',
            'content': '{\n  "name": "test",\n  "version": "1.0.0",\n  "description": "测试配置文件"\n}',
            'type': 'application/json'
        },
        {
            'name': 'data.csv',
            'content': 'name,age,city\n张三,25,北京\n李四,30,上海\n王五,28,广州',
            'type': 'text/csv'
        }
    ]
    
    try:
        # 创建对话
        response = requests.post(
            'http://localhost:8000/api/chat/conversations',
            json={'title': '多文件类型测试'},
            timeout=10
        )
        conversation_id = response.json()['id']
        
        for test_file in test_files:
            print(f"\n📄 测试文件: {test_file['name']}")
            
            files = {
                'files': (test_file['name'], test_file['content'].encode('utf-8'), test_file['type'])
            }
            
            data = {
                'message': f'请分析这个{test_file["name"]}文件',
                'conversation_id': conversation_id
            }
            
            response = requests.post(
                'http://localhost:8000/api/chat/stream-with-files',
                files=files,
                data=data,
                stream=True,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"✅ {test_file['name']} 上传成功")
                
                # 等待响应完成
                for line in response.iter_lines():
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            data_str = line[6:]
                            if data_str == '[DONE]':
                                break
            else:
                print(f"❌ {test_file['name']} 上传失败")
        
        print("\n✅ 多文件类型测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 多文件类型测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试文档查看功能")
    print("="*60)
    
    success1 = test_document_viewer()
    success2 = test_multiple_file_types()
    
    print("\n" + "="*60)
    print("测试结果汇总:")
    print(f"文档查看功能: {'✅' if success1 else '❌'}")
    print(f"多文件类型支持: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
        print("\n📝 使用说明:")
        print("1. 上传文档后，在聊天界面中点击文档附件")
        print("2. 页面右侧会出现文档查看面板")
        print("3. 面板中显示文档的原始内容")
        print("4. 可以复制文档内容或关闭面板")
    else:
        print("\n❌ 部分测试失败，请检查日志")
