# AI系统 - 后端

基于 FastAPI + Tortoise-ORM 构建的高性能AI对话系统后端。

## 特性

- 🚀 **FastAPI** - 高性能异步Web框架
- 🗄️ **Tortoise-ORM** - 异步ORM，支持多种数据库
- 🤖 **Autogen集成** - 支持多Agent协作（可选）
- 📡 **SSE流式输出** - 实时对话体验
- 🔒 **类型安全** - 完整的Pydantic模型
- 📚 **自动文档** - Swagger/OpenAPI文档

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置环境

复制 `.env.example` 为 `.env` 并配置：

```bash
# 基础配置
DEBUG=true
DATABASE_URL=sqlite://db.sqlite3

# AI模型API密钥（可选）
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

### 启动服务

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

服务将在 http://localhost:8000 启动

## API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 主要API端点

### 对话管理
- `POST /api/chat/conversations` - 创建新对话
- `GET /api/chat/conversations` - 获取对话列表
- `GET /api/chat/conversations/{id}/messages` - 获取对话消息
- `DELETE /api/chat/conversations/{id}` - 删除对话

### 聊天功能
- `POST /api/chat/send` - 发送消息（非流式）
- `GET /api/chat/stream/{conversation_id}` - SSE流式聊天

### 系统
- `GET /health` - 健康检查
- `GET /api/chat/health` - 聊天服务健康检查

## 项目结构

```
backend/
├── api/                # API路由模块
│   └── chat.py        # 聊天相关API
├── services/          # 业务逻辑
│   └── ai_service.py  # AI服务
├── models.py          # 数据模型
├── config.py          # 配置管理
├── main.py           # 应用入口
└── requirements.txt   # 依赖列表
```

## 数据模型

### Conversation (对话)
- id: 对话ID
- title: 对话标题
- created_at: 创建时间
- updated_at: 更新时间

### Message (消息)
- id: 消息ID
- conversation_id: 所属对话
- role: 角色 (user/assistant/system)
- content: 消息内容
- status: 状态 (pending/processing/completed/failed)
- model_name: 使用的模型名称
- processing_time: 处理时间

## AI模型集成

### 支持的模型
- OpenAI GPT系列
- Anthropic Claude系列
- 其他兼容OpenAI API的模型

### Autogen集成
如果安装了 `autogen-agentchat`，系统将支持：
- 多Agent协作
- 复杂对话流程
- 专业化Agent角色

### 模拟模式
如果未配置AI模型API密钥，系统将运行在模拟模式下，返回预设的响应。

## 开发说明

- 使用异步编程提高性能
- 支持CORS跨域请求
- 完整的错误处理
- 结构化日志记录
- 数据库自动迁移
