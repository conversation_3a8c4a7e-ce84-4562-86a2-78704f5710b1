#!/usr/bin/env python3
"""
测试最终修复的文件上传功能
"""

import requests
import json

def test_final_fix():
    """测试最终修复"""
    
    print("🔄 检查后端服务...")
    try:
        response = requests.get('http://localhost:8000/api/chat/health', timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端: {e}")
        return False
    
    print("\n🔄 创建对话...")
    try:
        response = requests.post(
            'http://localhost:8000/api/chat/conversations',
            json={'title': '最终修复测试'},
            timeout=10
        )
        
        if response.status_code == 200:
            conversation = response.json()
            conversation_id = conversation['id']
            print(f"✅ 对话创建成功: ID={conversation_id}")
        else:
            print(f"❌ 创建对话失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ 创建对话异常: {e}")
        return False
    
    # 测试1: 单个小文件
    print("\n🔄 测试单个小文件...")
    small_content = "Hello World!\n这是一个小测试文件。\n包含中文内容。\n测试完成。"
    
    try:
        files = {
            'files': ('small.txt', small_content.encode('utf-8'), 'text/plain')
        }
        
        data = {
            'message': '请分析这个小文件',
            'conversation_id': conversation_id
        }
        
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            stream=True,
            timeout=30
        )
        
        print(f"📡 HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 小文件上传成功")
            
            # 读取响应
            ai_response = ""
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        if data_str == '[DONE]':
                            print("\n✅ 小文件响应完成")
                            break
                        
                        try:
                            chunk = json.loads(data_str)
                            if chunk.get('type') == 'content':
                                content = chunk.get('content', '')
                                ai_response += content
                                print(".", end='', flush=True)
                            elif chunk.get('type') == 'error':
                                print(f"\n❌ 错误: {chunk.get('error')}")
                                return False
                        except:
                            pass
            
            if ai_response:
                print(f"\n📄 AI响应长度: {len(ai_response)} 字符")
        else:
            print(f"❌ 小文件上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 小文件测试异常: {e}")
        return False
    
    # 测试2: 多个文件
    print("\n\n🔄 测试多个文件...")
    
    try:
        # 准备多个文件
        file1_content = "第一个文件的内容。\n包含一些测试数据。"
        file2_content = "第二个文件的内容。\n包含不同的测试数据。"
        
        files = [
            ('files', ('file1.txt', file1_content.encode('utf-8'), 'text/plain')),
            ('files', ('file2.txt', file2_content.encode('utf-8'), 'text/plain'))
        ]
        
        data = {
            'message': '请分析这两个文件',
            'conversation_id': conversation_id
        }
        
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            stream=True,
            timeout=30
        )
        
        print(f"📡 HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 多文件上传成功")
            
            # 读取响应
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        if data_str == '[DONE]':
                            print("\n✅ 多文件响应完成")
                            break
                        
                        try:
                            chunk = json.loads(data_str)
                            if chunk.get('type') == 'content':
                                print(".", end='', flush=True)
                            elif chunk.get('type') == 'error':
                                print(f"\n❌ 错误: {chunk.get('error')}")
                                return False
                        except:
                            pass
        else:
            print(f"❌ 多文件上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 多文件测试异常: {e}")
        return False
    
    # 测试3: 中等大小文件
    print("\n\n🔄 测试中等大小文件...")
    medium_content = "这是一个中等大小的测试文件。\n" * 1000  # 约30KB
    
    try:
        files = {
            'files': ('medium.txt', medium_content.encode('utf-8'), 'text/plain')
        }
        
        data = {
            'message': '请分析这个中等文件',
            'conversation_id': conversation_id
        }
        
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            stream=True,
            timeout=30
        )
        
        print(f"📡 HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 中等文件上传成功")
            
            # 只读取前几行响应
            line_count = 0
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        if data_str == '[DONE]':
                            print("\n✅ 中等文件响应完成")
                            break
                        
                        try:
                            chunk = json.loads(data_str)
                            if chunk.get('type') == 'content':
                                print(".", end='', flush=True)
                            elif chunk.get('type') == 'error':
                                print(f"\n❌ 错误: {chunk.get('error')}")
                                return False
                        except:
                            pass
                        
                        line_count += 1
                        if line_count > 20:  # 限制输出
                            print("\n... (响应继续)")
                            break
        else:
            print(f"❌ 中等文件上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 中等文件测试异常: {e}")
        return False
    
    # 检查最终结果
    print("\n\n🔄 检查上传结果...")
    try:
        response = requests.get(
            f'http://localhost:8000/api/chat/conversations/{conversation_id}/messages',
            timeout=10
        )
        
        if response.status_code == 200:
            messages = response.json()
            print(f"✅ 获取到 {len(messages)} 条消息")
            
            attachment_count = 0
            for msg in messages:
                if msg.get('attachments'):
                    attachment_count += len(msg['attachments'])
                    for att in msg['attachments']:
                        print(f"📎 附件: {att['filename']} ({att['file_size']} bytes)")
                        
                        # 检查文件是否存在
                        import os
                        file_path = f"backend/{att['file_path']}"
                        if os.path.exists(file_path):
                            print(f"    ✅ 文件存在: {file_path}")
                        else:
                            print(f"    ❌ 文件不存在: {file_path}")
            
            print(f"📊 总附件数: {attachment_count}")
            return attachment_count >= 4  # 应该有4个文件（1+2+1）
        else:
            print(f"❌ 获取消息失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查结果异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试最终修复的文件上传功能")
    print("="*60)
    
    success = test_final_fix()
    
    print("\n" + "="*60)
    if success:
        print("🎉 测试成功！文件上传功能已修复")
    else:
        print("❌ 测试失败，需要进一步调试")
