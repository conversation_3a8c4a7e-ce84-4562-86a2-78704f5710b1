#!/usr/bin/env python3
"""
测试新的文件上传方法
"""

import requests
import json

def test_new_approach():
    """测试新的文件上传方法"""
    
    print("🔄 检查后端服务...")
    try:
        response = requests.get('http://localhost:8000/api/chat/health', timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端: {e}")
        return False
    
    print("\n🔄 创建对话...")
    try:
        response = requests.post(
            'http://localhost:8000/api/chat/conversations',
            json={'title': '新方法测试'},
            timeout=10
        )
        
        if response.status_code == 200:
            conversation = response.json()
            conversation_id = conversation['id']
            print(f"✅ 对话创建成功: ID={conversation_id}")
        else:
            print(f"❌ 创建对话失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ 创建对话异常: {e}")
        return False
    
    # 测试1: 小文件
    print("\n🔄 测试小文件...")
    small_content = "Hello World!\n这是一个小测试文件。\n包含中文内容。"
    
    try:
        files = {
            'files': ('small.txt', small_content.encode('utf-8'), 'text/plain')
        }
        
        data = {
            'message': '请分析这个小文件',
            'conversation_id': conversation_id
        }
        
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            stream=True,
            timeout=30
        )
        
        print(f"📡 HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 小文件上传成功")
            
            # 读取部分响应
            content_received = False
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        if data_str == '[DONE]':
                            print("\n✅ 小文件响应完成")
                            break
                        
                        try:
                            chunk = json.loads(data_str)
                            if chunk.get('type') == 'content':
                                if not content_received:
                                    print("📄 开始接收AI响应...")
                                    content_received = True
                                print(".", end='', flush=True)
                            elif chunk.get('type') == 'error':
                                print(f"\n❌ 错误: {chunk.get('error')}")
                                return False
                        except:
                            pass
        else:
            print(f"❌ 小文件上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 小文件测试异常: {e}")
        return False
    
    # 测试2: 中等文件
    print("\n\n🔄 测试中等文件...")
    medium_content = "这是一个中等大小的测试文件。\n" * 500  # 约15KB
    
    try:
        files = {
            'files': ('medium.txt', medium_content.encode('utf-8'), 'text/plain')
        }
        
        data = {
            'message': '请分析这个中等文件',
            'conversation_id': conversation_id
        }
        
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            stream=True,
            timeout=30
        )
        
        print(f"📡 HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 中等文件上传成功")
            
            # 读取部分响应
            content_received = False
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        if data_str == '[DONE]':
                            print("\n✅ 中等文件响应完成")
                            break
                        
                        try:
                            chunk = json.loads(data_str)
                            if chunk.get('type') == 'content':
                                if not content_received:
                                    print("📄 开始接收AI响应...")
                                    content_received = True
                                print(".", end='', flush=True)
                            elif chunk.get('type') == 'error':
                                print(f"\n❌ 错误: {chunk.get('error')}")
                                return False
                        except:
                            pass
        else:
            print(f"❌ 中等文件上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 中等文件测试异常: {e}")
        return False
    
    # 测试3: 大文件（应该被拒绝）
    print("\n\n🔄 测试大文件（应该被拒绝）...")
    large_content = "A" * (11 * 1024 * 1024)  # 11MB
    
    try:
        files = {
            'files': ('large.txt', large_content.encode('utf-8'), 'text/plain')
        }
        
        data = {
            'message': '测试大文件',
            'conversation_id': conversation_id
        }
        
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            stream=True,
            timeout=30
        )
        
        print(f"📡 HTTP状态: {response.status_code}")
        
        if response.status_code == 413:
            print("✅ 大文件被正确拒绝 (413 Payload Too Large)")
        elif response.status_code != 200:
            print(f"✅ 大文件被拒绝 (状态码: {response.status_code})")
        else:
            # 检查是否在流中有错误
            error_found = False
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        if data_str == '[DONE]':
                            break
                        
                        try:
                            chunk = json.loads(data_str)
                            if chunk.get('type') == 'error':
                                error_msg = chunk.get('error', '')
                                if '超过限制' in error_msg or 'too large' in error_msg.lower():
                                    print("✅ 大文件在处理过程中被正确拒绝")
                                    error_found = True
                                    break
                        except:
                            pass
            
            if not error_found:
                print("⚠️ 大文件没有被拒绝，这可能是个问题")
                return False
            
    except Exception as e:
        print(f"❌ 大文件测试异常: {e}")
        return False
    
    # 检查最终结果
    print("\n\n🔄 检查上传结果...")
    try:
        response = requests.get(
            f'http://localhost:8000/api/chat/conversations/{conversation_id}/messages',
            timeout=10
        )
        
        if response.status_code == 200:
            messages = response.json()
            print(f"✅ 获取到 {len(messages)} 条消息")
            
            attachment_count = 0
            for msg in messages:
                if msg.get('attachments'):
                    attachment_count += len(msg['attachments'])
                    for att in msg['attachments']:
                        print(f"📎 附件: {att['filename']} ({att['file_size']} bytes)")
                        
                        # 检查文件是否存在
                        import os
                        file_path = f"backend/{att['file_path']}"
                        if os.path.exists(file_path):
                            print(f"    ✅ 文件存在: {file_path}")
                        else:
                            print(f"    ❌ 文件不存在: {file_path}")
            
            print(f"📊 总附件数: {attachment_count}")
            return attachment_count >= 2  # 应该有小文件和中等文件
        else:
            print(f"❌ 获取消息失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查结果异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试新的文件上传方法")
    print("="*50)
    
    success = test_new_approach()
    
    print("\n" + "="*50)
    if success:
        print("✅ 测试成功！新方法有效")
    else:
        print("❌ 测试失败")
