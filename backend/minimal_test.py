#!/usr/bin/env python3
"""
最小化的文件上传测试
"""

import requests
import tempfile
import os

def minimal_test():
    """最小化测试"""
    
    print("🔄 检查后端服务...")
    try:
        response = requests.get('http://localhost:8000/api/chat/health', timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端: {e}")
        return False
    
    print("\n🔄 创建对话...")
    try:
        response = requests.post(
            'http://localhost:8000/api/chat/conversations',
            json={'title': '最小测试'},
            timeout=10
        )
        
        if response.status_code == 200:
            conversation = response.json()
            conversation_id = conversation['id']
            print(f"✅ 对话创建成功: ID={conversation_id}")
        else:
            print(f"❌ 创建对话失败: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"❌ 创建对话异常: {e}")
        return False
    
    print("\n🔄 创建测试文件...")
    test_content = "Hello World!\n这是一个简单的测试文件。\n"
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
    
    print(f"📝 临时文件: {temp_file_path}")
    
    try:
        print("\n🔄 上传文件...")
        
        with open(temp_file_path, 'rb') as f:
            files = {
                'files': ('test.txt', f, 'text/plain')
            }
            
            data = {
                'message': '请分析这个文件',
                'conversation_id': conversation_id
            }
            
            response = requests.post(
                'http://localhost:8000/api/chat/stream-with-files',
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"📡 HTTP状态: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 文件上传成功")
                
                # 读取响应头部
                response_text = response.text[:500]  # 只读取前500字符
                print(f"📄 响应预览: {response_text}")
                
                return True
            else:
                print(f"❌ 文件上传失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file_path)
            print(f"🗑️ 已清理临时文件")
        except:
            pass

def test_large_file():
    """测试大文件（应该被拒绝）"""
    
    print("\n" + "="*30)
    print("🧪 测试大文件上传（应该被拒绝）")
    
    # 创建对话
    try:
        response = requests.post(
            'http://localhost:8000/api/chat/conversations',
            json={'title': '大文件测试'},
            timeout=10
        )
        conversation = response.json()
        conversation_id = conversation['id']
    except Exception as e:
        print(f"❌ 创建对话失败: {e}")
        return False
    
    # 创建大文件内容（11MB）
    large_content = "A" * (11 * 1024 * 1024)
    
    try:
        files = {
            'files': ('large.txt', large_content.encode('utf-8'), 'text/plain')
        }
        
        data = {
            'message': '测试大文件',
            'conversation_id': conversation_id
        }
        
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"📡 HTTP状态: {response.status_code}")
        
        if response.status_code == 413:
            print("✅ 大文件被正确拒绝 (413 Payload Too Large)")
            return True
        elif response.status_code != 200:
            print(f"✅ 大文件被拒绝 (状态码: {response.status_code})")
            return True
        else:
            print("⚠️ 大文件没有被拒绝，这可能是个问题")
            return False
            
    except Exception as e:
        print(f"❌ 大文件测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始最小化文件上传测试")
    print("="*50)
    
    success1 = minimal_test()
    success2 = test_large_file()
    
    print("\n" + "="*50)
    if success1 and success2:
        print("✅ 所有测试通过")
    else:
        print("❌ 部分测试失败")
        print(f"小文件测试: {'✅' if success1 else '❌'}")
        print(f"大文件测试: {'✅' if success2 else '❌'}")
