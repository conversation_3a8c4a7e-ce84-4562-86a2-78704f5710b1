<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="16">
            <item index="0" class="java.lang.String" itemvalue="pip" />
            <item index="1" class="java.lang.String" itemvalue="pydantic-settings" />
            <item index="2" class="java.lang.String" itemvalue="openai" />
            <item index="3" class="java.lang.String" itemvalue="anyio" />
            <item index="4" class="java.lang.String" itemvalue="rich" />
            <item index="5" class="java.lang.String" itemvalue="httpx" />
            <item index="6" class="java.lang.String" itemvalue="email-validator" />
            <item index="7" class="java.lang.String" itemvalue="fastapi" />
            <item index="8" class="java.lang.String" itemvalue="pydantic" />
            <item index="9" class="java.lang.String" itemvalue="tortoise-orm" />
            <item index="10" class="java.lang.String" itemvalue="aiofiles" />
            <item index="11" class="java.lang.String" itemvalue="cryptography" />
            <item index="12" class="java.lang.String" itemvalue="aerich" />
            <item index="13" class="java.lang.String" itemvalue="uvicorn" />
            <item index="14" class="java.lang.String" itemvalue="python-multipart" />
            <item index="15" class="java.lang.String" itemvalue="python-jose" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>