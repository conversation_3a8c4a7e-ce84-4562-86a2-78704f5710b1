"""
聊天API模块

该模块提供了完整的聊天功能API接口，包括：
1. 对话管理 - 创建、获取、删除对话
2. 消息管理 - 获取对话消息历史
3. 非流式聊天 - 使用agent.run方法，等待完整响应
4. 流式聊天 - 使用agent.run_stream方法，实时返回响应片段
5. 健康检查 - 检查服务状态

作者: jinglv
时间: 2025年1月
"""

import json
from typing import List

# FastAPI相关导入
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import StreamingResponse

# 项目内部导入
from models.chat import Conversation, MessageRole
from schemas.chat import ConversationResponse, MessageResponse, ChatRequest
from services.ai_service import ai_service
from services.file_service import file_service

# 创建聊天路由器
router = APIRouter(prefix="/chat", tags=["聊天"])


# ==================== 对话管理接口 ====================

@router.post("/conversations", response_model=ConversationResponse)
async def create_conversation(title: str = "新对话"):
    """
    创建新对话

    创建一个新的对话会话，可以指定标题。
    如果不指定标题，会使用默认值"新对话"，后续会根据第一条消息自动设置标题。

    参数:
        title: 对话标题，默认为"新对话"

    返回:
        ConversationResponse: 创建的对话信息

    异常:
        HTTPException: 创建失败时返回500错误
    """
    try:
        conversation = await ai_service.create_conversation(title)
        return ConversationResponse(
            id=conversation.id,
            title=conversation.title,
            created_at=conversation.created_at.isoformat(),
            updated_at=conversation.updated_at.isoformat(),
            message_count=0  # 新创建的对话消息数量为0
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建对话失败: {str(e)}")


@router.get("/conversations", response_model=List[ConversationResponse])
async def get_conversations():
    """
    获取对话列表

    获取所有对话的列表，包括对话的基本信息和消息数量。
    对话按更新时间倒序排列。

    返回:
        List[ConversationResponse]: 对话列表

    异常:
        HTTPException: 获取失败时返回500错误
    """
    try:
        # 获取所有对话并预加载相关消息
        conversations = await Conversation.all().prefetch_related("messages")
        return [
            ConversationResponse(
                id=conv.id,
                title=conv.title,
                created_at=conv.created_at.isoformat(),
                updated_at=conv.updated_at.isoformat(),
                message_count=len(conv.messages)  # 计算消息数量
            )
            for conv in conversations
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取对话列表失败: {str(e)}")


# ==================== 消息管理接口 ====================

@router.get("/conversations/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_conversation_messages(conversation_id: int):
    """
    获取对话消息历史

    获取指定对话的所有消息，按创建时间正序排列。
    包括用户消息和AI助手的回复消息。

    参数:
        conversation_id: 对话ID

    返回:
        List[MessageResponse]: 消息列表

    异常:
        HTTPException: 获取失败时返回500错误
    """
    try:
        # 获取对话的所有消息，包含文件附件
        messages = await ai_service.get_conversation_messages(conversation_id)

        result = []
        for msg in messages:
            # 获取消息的文件附件
            attachments = await msg.attachments.all()
            attachment_responses = []

            for attachment in attachments:
                attachment_responses.append({
                    "id": attachment.id,
                    "filename": attachment.filename,
                    "file_size": attachment.file_size,
                    "file_type": attachment.file_type,
                    "file_url": file_service.get_file_url(attachment),
                    "created_at": attachment.created_at.isoformat()
                })

            result.append(MessageResponse(
                id=msg.id,
                role=msg.role.value,  # 转换枚举为字符串
                content=msg.content,
                status=msg.status.value,  # 转换枚举为字符串
                created_at=msg.created_at.isoformat(),  # 转换为ISO格式
                model_name=msg.model_name,
                processing_time=msg.processing_time,
                tokens_used=msg.tokens_used,
                attachments=attachment_responses
            ))

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取消息失败: {str(e)}")


# ==================== 聊天功能接口 ====================

@router.post("/send")
async def send_message(request: ChatRequest):
    """
    发送聊天消息（非流式）- 使用 agent.run 方法

    这是非流式聊天接口，对应用户示例：
    result = await agent.run(task="请写一首七言绝句")
    print(result)

    该接口会等待AI完成整个响应后一次性返回完整结果。
    适用于API调用、批量处理等需要完整结果的场景。

    参数:
        request: 聊天请求，包含消息内容和可选的对话ID

    返回:
        dict: 包含响应信息的字典：
            - conversation_id: 对话ID
            - message_id: AI响应消息ID
            - content: AI响应内容
            - processing_time: 处理时间
            - status: 处理状态

    异常:
        HTTPException: 发送失败时返回500错误
    """
    try:
        # 如果没有指定对话ID，创建新对话
        if request.conversation_id is None:
            conversation = await ai_service.create_conversation()
            conversation_id = conversation.id
        else:
            conversation_id = request.conversation_id

        # 发送用户消息到数据库
        user_message = await ai_service.send_message(
            conversation_id,
            request.message,
            MessageRole.USER
        )

        # 使用非流式方法生成AI响应
        result = await ai_service.generate_response(conversation_id, request.message)

        # 返回响应结果
        return {
            "conversation_id": conversation_id,
            "message_id": result["message_id"],
            "content": result["content"],
            "processing_time": result["processing_time"],
            "status": result["status"]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送消息失败: {str(e)}")


@router.post("/send-stream")
async def send_message_stream_fallback(request: ChatRequest):
    """发送聊天消息（使用流式但收集完整响应）- 兼容旧版本"""
    try:
        # 如果没有指定对话ID，创建新对话
        if request.conversation_id is None:
            conversation = await ai_service.create_conversation()
            conversation_id = conversation.id
        else:
            conversation_id = request.conversation_id

        # 发送用户消息
        user_message = await ai_service.send_message(
            conversation_id,
            request.message,
            MessageRole.USER
        )

        # 生成AI响应（流式但收集完整内容）
        response_content = ""
        async for chunk in ai_service.generate_response_stream(conversation_id, request.message):
            if chunk["type"] == "content":
                response_content += chunk["content"]
            elif chunk["type"] == "complete":
                break

        return {
            "conversation_id": conversation_id,
            "user_message_id": user_message.id,
            "response": response_content.strip()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"发送消息失败: {str(e)}")


@router.get("/stream/{conversation_id}")
async def chat_stream(conversation_id: int, message: str):
    """SSE流式聊天接口 - 使用 agent.run_stream 方法"""

    #  生成SSE数据，函数定义
    async def generate_stream():
        try:
            async for chunk in ai_service.generate_response_stream(conversation_id, message):
                # 格式化SSE数据
                data = json.dumps(chunk, ensure_ascii=False)
                yield f"data: {data}\n\n"

                # 如果是完成或错误，结束流
                if chunk["type"] in ["complete", "error"]:
                    break
        except Exception as e:
            error_data = json.dumps({
                "type": "error",
                "error": str(e)
            }, ensure_ascii=False)
            yield f"data: {error_data}\n\n"
        # 发送结束信号
        yield "data: [DONE]\n\n"


@router.post("/stream-with-files")
async def chat_stream_with_files(
    message: str = Form(...),
    conversation_id: int = Form(...),
    files: List[UploadFile] = File(default=[])
):
    """支持文件上传的SSE流式聊天接口"""

    async def generate_stream():
        try:
            # 发送用户消息到数据库
            user_message = await ai_service.send_message(
                conversation_id,
                message,
                MessageRole.USER
            )

            # 处理文件上传
            file_contents = []
            if files:
                for file in files:
                    try:
                        # 创建文件附件记录
                        attachment = await file_service.create_file_attachment(user_message, file)

                        # 如果有提取的文本内容，添加到消息中
                        if attachment.extracted_text:
                            file_contents.append(f"\n\n[文件: {attachment.filename}]\n{attachment.extracted_text}")

                    except Exception as e:
                        print(f"处理文件 {file.filename} 失败: {e}")
                        file_contents.append(f"\n\n[文件: {file.filename}] - 处理失败: {str(e)}")

            # 将文件内容添加到消息中
            enhanced_message = message
            if file_contents:
                enhanced_message += "\n\n" + "\n".join(file_contents)

            # 生成AI响应
            async for chunk in ai_service.generate_response_stream(conversation_id, enhanced_message):
                # 格式化SSE数据
                data = json.dumps(chunk, ensure_ascii=False)
                yield f"data: {data}\n\n"

                # 如果是完成或错误，结束流
                if chunk["type"] in ["complete", "error"]:
                    break

        except Exception as e:
            error_data = json.dumps({
                "type": "error",
                "error": str(e)
            }, ensure_ascii=False)
            yield f"data: {error_data}\n\n"

        # 发送结束信号
        yield "data: [DONE]\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@router.delete("/conversations/{conversation_id}")
async def delete_conversation(conversation_id: int):
    """删除对话"""
    try:
        conversation = await ai_service.get_conversation(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在")

        await conversation.delete()
        return {"message": "对话删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除对话失败: {str(e)}")


@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "chat-api",
        "agent_loaded": bool(ai_service.agent)
    }
