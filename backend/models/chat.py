"""
聊天相关数据模型

该模块定义了聊天系统的所有数据模型，包括：
1. 枚举类型 - 消息角色和状态
2. 对话模型 - 存储对话会话信息
3. 消息模型 - 存储聊天消息内容
4. 智能体会话模型 - 存储智能体会话配置（预留功能）

使用Tortoise ORM作为数据库ORM框架。

作者: jinglv
时间: 2025年1月
"""

from enum import Enum

from tortoise import fields
from tortoise.models import Model


# ==================== 枚举类型定义 ====================

class MessageRole(str, Enum):
    """
    消息角色枚举

    定义了聊天系统中不同类型的消息角色：
    - USER: 用户消息
    - ASSISTANT: AI助手消息
    - SYSTEM: 系统消息（用于设置AI行为）
    """
    USER = "user"  # 用户角色
    ASSISTANT = "assistant"  # AI助手角色
    SYSTEM = "system"  # 系统角色


class MessageStatus(str, Enum):
    """
    消息状态枚举

    定义了消息的处理状态：
    - PENDING: 待处理（预留状态）
    - PROCESSING: 处理中（AI正在生成响应）
    - COMPLETED: 已完成（消息处理完成）
    - FAILED: 处理失败（出现错误）
    """
    PENDING = "pending"  # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 处理失败


# ==================== 数据模型定义 ====================

class Conversation(Model):
    """
    对话会话模型

    存储聊天对话的基本信息，一个对话可以包含多条消息。
    对话会根据第一条用户消息自动设置标题。

    字段说明:
        id: 主键，自动递增
        title: 对话标题，默认为"新对话"
        created_at: 创建时间，自动设置
        updated_at: 更新时间，自动更新
        is_active: 是否激活，用于软删除
        messages: 关联的消息列表（反向关联）
    """

    # 主键字段
    id = fields.IntField(pk=True, index=True, description="主键")  # 主键
    # 基本信息字段
    title = fields.CharField(max_length=200, default="新对话", description="对话标题")  # 对话标题
    # 时间戳字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")  # 创建时间
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")  # 更新时间
    # 状态字段
    is_active = fields.BooleanField(default=True, description="是否激活")  # 是否激活
    # 关联字段 - 反向关联到消息
    messages: fields.ReverseRelation["Message"]

    class Meta:
        """模型元数据配置"""
        table = "conversations"  # 数据库表名
        ordering = ["-updated_at"]  # 默认按更新时间倒序排列

    def __str__(self):
        """字符串表示"""
        return f"Conversation({self.id}): {self.title}"


class Message(Model):
    """
    消息模型

    存储聊天消息的详细信息，包括用户消息和AI助手的回复。
    每条消息都属于一个对话，并记录处理状态和元数据。

    字段说明:
        id: 主键，自动递增
        conversation: 所属对话，外键关联
        role: 消息角色（用户/助手/系统）
        content: 消息内容
        status: 处理状态
        model_name: 使用的AI模型名称（仅AI消息）
        tokens_used: 消耗的token数量（仅AI消息）
        processing_time: 处理时间（仅AI消息）
        created_at: 创建时间
        updated_at: 更新时间
    """

    # 主键字段
    id = fields.IntField(pk=True)
    # 关联字段 - 外键关联到对话
    conversation = fields.ForeignKeyField(
        "models.Conversation",  # 关联的模型
        related_name="messages",  # 反向关联名称
        on_delete=fields.CASCADE  # 级联删除
    )
    # 基本信息字段
    role = fields.CharEnumField(MessageRole, max_length=20)  # 消息角色
    content = fields.TextField()  # 消息内容
    status = fields.CharEnumField(MessageStatus, max_length=20, default=MessageStatus.COMPLETED)  # 处理状态
    # AI相关元数据字段（仅对AI消息有效）
    model_name = fields.CharField(max_length=100, null=True)  # 使用的AI模型名称
    tokens_used = fields.IntField(null=True)  # 消耗的token数量
    processing_time = fields.FloatField(null=True)  # 处理时间（秒）
    # 时间戳字段
    created_at = fields.DatetimeField(auto_now_add=True)  # 创建时间
    updated_at = fields.DatetimeField(auto_now=True)  # 更新时间

    class Meta:
        """模型元数据配置"""
        table = "messages"  # 数据库表名
        ordering = ["created_at"]  # 默认按创建时间正序排列

    def __str__(self):
        """字符串表示"""
        return f"Message({self.id}): {self.role} - {self.content[:50]}..."


class AgentSession(Model):
    """
    智能体会话模型（预留功能）

    用于存储智能体会话的配置信息，支持多智能体协作场景。
    当前版本暂未使用，为未来扩展预留。

    字段说明:
        id: 主键，自动递增
        conversation: 所属对话，外键关联
        agent_name: 智能体名称
        agent_config: 智能体配置信息（JSON格式）
        is_active: 是否激活
        created_at: 创建时间
        updated_at: 更新时间
    """

    # 主键字段
    id = fields.IntField(pk=True)

    # 关联字段 - 外键关联到对话
    conversation = fields.ForeignKeyField(
        "models.Conversation",  # 关联的模型
        related_name="agent_sessions",  # 反向关联名称
        on_delete=fields.CASCADE  # 级联删除
    )
    # 智能体配置字段
    agent_name = fields.CharField(max_length=100)  # 智能体名称
    agent_config = fields.JSONField(default=dict)  # 智能体配置信息（JSON格式）
    # 状态和时间戳字段
    is_active = fields.BooleanField(default=True)  # 是否激活
    created_at = fields.DatetimeField(auto_now_add=True)  # 创建时间
    updated_at = fields.DatetimeField(auto_now=True)  # 更新时间

    class Meta:
        """模型元数据配置"""
        table = "agent_sessions"  # 数据库表名

    def __str__(self):
        """字符串表示"""
        return f"AgentSession({self.id}): {self.agent_name}"
