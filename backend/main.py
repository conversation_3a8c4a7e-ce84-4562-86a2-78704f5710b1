"""
AI聊天系统主应用

这是一个基于FastAPI和Autogen的AI聊天系统，提供：
1. 非流式聊天 - 使用agent.run方法，等待完整响应
2. 流式聊天 - 使用agent.run_stream方法，实时返回响应片段
3. 对话管理 - 创建、获取、删除对话
4. 消息历史 - 查看对话的消息记录
5. 健康检查 - 监控服务状态

技术栈:
- FastAPI: Web框架
- Tortoise ORM: 数据库ORM
- Autogen: AI智能体框架
- SQLite: 数据库（可配置其他数据库）

作者: jinglv
时间: 2025年1月
"""

# FastAPI相关导入
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from tortoise.contrib.fastapi import register_tortoise
from contextlib import asynccontextmanager
import uvicorn

# 项目内部导入
from config import settings, TORTOISE_ORM
from api.chat import router as chat_router


# ==================== 应用生命周期管理 ====================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理

    管理应用的启动和关闭过程，包括：
    - 启动时：打印应用信息和配置
    - 关闭时：清理资源
    """
    # 启动时执行
    print(f"🚀 {settings.app_name} v{settings.app_version} 正在启动...")
    print(f"📊 数据库: {settings.database_url}")
    print(f"🔧 调试模式: {settings.debug}")

    yield  # 应用运行期间

    # 关闭时执行
    print("👋 应用正在关闭...")


# ==================== FastAPI应用创建 ====================

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,  # 应用标题
    version=settings.app_version,  # 应用版本
    description="一个功能完善的AI对话系统，支持流式和非流式聊天",  # 应用描述
    lifespan=lifespan  # 生命周期管理
)

# ==================== 中间件配置 ====================

# 配置CORS中间件，允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,  # 允许的源地址
    allow_credentials=True,  # 允许携带凭证
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)

# ==================== 路由注册 ====================

# 注册聊天相关路由
app.include_router(chat_router, prefix=settings.api_prefix)

# ==================== 数据库配置 ====================

# 注册Tortoise ORM
register_tortoise(
    app,
    config=TORTOISE_ORM,  # 数据库配置
    generate_schemas=True,  # 自动生成数据库表
    add_exception_handlers=True,  # 添加异常处理器
)


# ==================== 基础路由 ====================

@app.get("/")
async def root():
    """
    根路径接口

    返回应用的基本信息和可用的API端点
    """
    return {
        "message": f"欢迎使用 {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs",  # API文档地址
        "api": settings.api_prefix  # API前缀
    }


@app.get("/health")
async def health_check():
    """
    健康检查接口

    用于监控服务的运行状态
    """
    return {
        "status": "healthy",
        "app": settings.app_name,
        "version": settings.app_version
    }


# ==================== 全局异常处理 ====================

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """
    全局异常处理器

    捕获所有未处理的异常，统一返回错误响应。
    在调试模式下会打印详细的错误信息。

    参数:
        request: 请求对象
        exc: 异常对象

    返回:
        JSONResponse: 错误响应
    """
    # 在调试模式下打印详细错误信息
    if settings.debug:
        import traceback
        print(f"❌ 未处理的异常: {exc}")
        print(traceback.format_exc())

    # 返回统一的错误响应
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "detail": str(exc) if settings.debug else "请联系管理员"
        }
    )


# ==================== 应用启动 ====================

if __name__ == "__main__":
    """
    应用启动入口

    使用uvicorn启动FastAPI应用
    """
    uvicorn.run(
        "main:app",  # 应用模块和实例
        host="0.0.0.0",  # 监听所有网络接口
        port=8000,  # 监听端口
        reload=settings.debug,  # 调试模式下启用自动重载
        log_level="info"  # 日志级别
    )
