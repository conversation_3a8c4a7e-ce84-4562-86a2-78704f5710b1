#!/usr/bin/env python3
"""
数据库表创建脚本

用于创建缺失的数据库表，特别是 file_attachments 表。
这个脚本会检查并创建所有必要的数据库表。

使用方法:
    python create_tables.py

作者: jinglv
时间: 2025年1月
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tortoise import Tortoise
from config import TORTOISE_ORM


async def check_table_exists(connection, table_name: str) -> bool:
    """检查表是否存在"""
    try:
        # SQLite查询表是否存在
        result = await connection.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
            [table_name]
        )
        return len(result[1]) > 0
    except Exception as e:
        print(f"检查表 {table_name} 时出错: {e}")
        return False


async def create_missing_tables():
    """创建缺失的数据库表"""
    print("=" * 60)
    print("数据库表创建脚本")
    print("=" * 60)
    
    try:
        # 初始化Tortoise ORM
        print("🔄 初始化数据库连接...")
        await Tortoise.init(config=TORTOISE_ORM)
        
        # 获取数据库连接
        connection = Tortoise.get_connection("default")
        
        # 检查现有表
        print("\n📋 检查现有表...")
        tables_to_check = [
            "conversations",
            "messages", 
            "file_attachments",
            "agent_sessions"
        ]
        
        existing_tables = []
        missing_tables = []
        
        for table in tables_to_check:
            exists = await check_table_exists(connection, table)
            if exists:
                existing_tables.append(table)
                print(f"✅ 表 {table} 已存在")
            else:
                missing_tables.append(table)
                print(f"❌ 表 {table} 不存在")
        
        if not missing_tables:
            print("\n🎉 所有表都已存在，无需创建新表")
            return
        
        print(f"\n🔧 需要创建的表: {', '.join(missing_tables)}")
        
        # 生成数据库表结构
        print("\n🔄 生成数据库表结构...")
        await Tortoise.generate_schemas()
        
        print("✅ 数据库表创建完成")
        
        # 再次检查表是否创建成功
        print("\n🔍 验证表创建结果...")
        for table in missing_tables:
            exists = await check_table_exists(connection, table)
            if exists:
                print(f"✅ 表 {table} 创建成功")
            else:
                print(f"❌ 表 {table} 创建失败")
        
    except Exception as e:
        print(f"❌ 创建数据库表时发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        print("\n🔚 数据库连接已关闭")


async def show_table_info():
    """显示数据库表信息"""
    print("\n" + "=" * 60)
    print("数据库表信息")
    print("=" * 60)
    
    try:
        await Tortoise.init(config=TORTOISE_ORM)
        connection = Tortoise.get_connection("default")
        
        # 获取所有表
        result = await connection.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
        )
        
        tables = [row[0] for row in result[1]]
        
        print(f"📊 数据库中共有 {len(tables)} 个表:")
        for table in tables:
            print(f"  - {table}")
            
            # 获取表结构
            try:
                schema_result = await connection.execute_query(f"PRAGMA table_info({table})")
                columns = schema_result[1]
                print(f"    字段数: {len(columns)}")
                for col in columns:
                    col_name, col_type = col[1], col[2]
                    print(f"      {col_name}: {col_type}")
                print()
            except Exception as e:
                print(f"    获取表结构失败: {e}")
                
    except Exception as e:
        print(f"❌ 获取表信息时发生错误: {e}")
    finally:
        await Tortoise.close_connections()


async def main():
    """主函数"""
    print("数据库表管理工具")
    print("用于创建和检查数据库表")
    
    # 创建缺失的表
    await create_missing_tables()
    
    # 显示表信息
    await show_table_info()
    
    print("\n" + "=" * 60)
    print("操作完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
