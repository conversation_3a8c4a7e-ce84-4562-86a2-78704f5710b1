"""
应用配置模块

该模块定义了应用的所有配置项，包括：
1. 应用基础配置 - 名称、版本、调试模式
2. 数据库配置 - 数据库连接URL
3. API配置 - API前缀、CORS设置
4. AI模型配置 - OpenAI API密钥、模型设置
5. Autogen配置 - 智能体配置列表
6. Tortoise ORM配置 - 数据库ORM设置

配置支持从环境变量和.env文件中读取。

作者: jinglv
时间: 2025年1月
"""

from typing import Optional

# 尝试导入新版本的pydantic_settings，如果失败则使用旧版本
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """
    应用配置类

    定义了应用的所有配置项，支持从环境变量和.env文件中读取配置。
    配置项包括应用基础信息、数据库连接、API设置、AI模型配置等。
    """

    # ==================== 应用基础配置 ====================
    app_name: str = "AI系统"  # 应用名称
    app_version: str = "1.0.0"  # 应用版本
    debug: bool = True  # 调试模式，生产环境应设为False

    # ==================== 数据库配置 ====================
    database_url: str = "sqlite://db.sqlite3"  # 数据库连接URL

    # ==================== API配置 ====================
    api_prefix: str = "/api"  # API路径前缀
    cors_origins: list = ["http://localhost:3000", "http://127.0.0.1:3000"]  # 允许的CORS源

    # ==================== AI模型配置 ====================
    openai_api_key: Optional[str] = None  # OpenAI API密钥
    openai_base_url: Optional[str] = None  # OpenAI API基础URL（可选，用于代理）
    default_model: str = "gpt-3.5-turbo"  # 默认使用的AI模型

    # ==================== Autogen配置 ====================
    autogen_config_list: list = []  # Autogen智能体配置列表

    class Config:
        """Pydantic配置"""
        env_file = ".env"  # 从.env文件读取环境变量
        case_sensitive = False  # 环境变量名不区分大小写

    def __init__(self, **kwargs):
        """
        初始化配置

        在初始化时动态构建Autogen配置列表
        """
        super().__init__(**kwargs)

        # 如果配置了OpenAI API密钥，动态构建Autogen配置
        if self.openai_api_key:
            self.autogen_config_list.append({
                "models": self.default_model,
                "api_key": self.openai_api_key,
                "base_url": self.openai_base_url,
                "api_type": "openai"
            })


# ==================== 全局配置实例 ====================
# 创建全局设置实例，供整个应用使用
settings = Settings()

# ==================== Tortoise ORM配置 ====================
# Tortoise ORM数据库配置
TORTOISE_ORM = {
    # 数据库连接配置
    "connections": {
        "default": settings.database_url  # 使用配置中的数据库URL
    },
    # 应用和模型配置
    "apps": {
        "models": {
            # 模型模块列表
            "models": [
                "models.chat",  # 聊天相关模型
                "aerich.models"  # 数据库迁移模型
            ],
            "default_connection": "default",  # 默认数据库连接
        },
    },
}
