"""
文件处理服务模块

该模块提供文件上传、存储、内容提取等功能，包括：
1. 文件上传和存储
2. 文件类型检测和验证
3. 文本内容提取（PDF、Word、图片OCR等）
4. 文件元数据管理

作者: jinglv
时间: 2025年1月
"""

import os
import uuid
import mimetypes
from typing import List, Optional, Tuple
from pathlib import Path

try:
    import aiofiles
except ImportError:
    aiofiles = None

from fastapi import UploadFile, HTTPException

try:
    from PIL import Image
except ImportError:
    Image = None

try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

try:
    from docx import Document
except ImportError:
    Document = None

from models.chat import FileAttachment, Message


class FileService:
    """文件处理服务类"""
    
    def __init__(self, upload_dir: str = "uploads"):
        """
        初始化文件服务
        
        Args:
            upload_dir: 文件上传目录
        """
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
        
        # 支持的文件类型
        self.supported_types = {
            'image': ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'document': ['application/pdf', 'text/plain', 'text/markdown'],
            'office': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'code': ['text/javascript', 'text/typescript', 'text/html', 'text/css', 'application/json']
        }
        
        # 文件大小限制（10MB）
        self.max_file_size = 10 * 1024 * 1024
    
    def validate_file(self, file: UploadFile) -> bool:
        """
        验证文件是否符合要求
        
        Args:
            file: 上传的文件
            
        Returns:
            bool: 是否通过验证
            
        Raises:
            HTTPException: 验证失败时抛出异常
        """
        # 检查文件大小
        if file.size and file.size > self.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"文件大小超过限制 ({self.max_file_size / 1024 / 1024:.1f}MB)"
            )
        
        # 检查文件类型
        file_type = file.content_type
        if not file_type:
            # 尝试从文件名推断类型
            file_type, _ = mimetypes.guess_type(file.filename or "")
        
        # 检查是否为支持的类型
        all_supported = []
        for types in self.supported_types.values():
            all_supported.extend(types)
        
        # 特殊处理一些常见的代码文件
        if file.filename:
            ext = Path(file.filename).suffix.lower()
            code_extensions = ['.py', '.js', '.ts', '.tsx', '.jsx', '.html', '.css', '.md', '.txt', '.json', '.csv']
            if ext in code_extensions:
                return True
        
        if file_type not in all_supported:
            raise HTTPException(
                status_code=415,
                detail=f"不支持的文件类型: {file_type}"
            )
        
        return True
    
    async def save_file(self, file: UploadFile) -> Tuple[str, str]:
        """
        保存上传的文件

        Args:
            file: 上传的文件

        Returns:
            Tuple[str, str]: (文件路径, 文件类型)
        """
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        file_ext = Path(file.filename or "").suffix
        filename = f"{file_id}{file_ext}"
        file_path = self.upload_dir / filename

        # 保存文件
        if aiofiles:
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)
        else:
            # 如果aiofiles不可用，使用同步方式
            content = await file.read()
            with open(file_path, 'wb') as f:
                f.write(content)

        return str(file_path), file.content_type or ""
    
    async def extract_text_content(self, file_path: str, file_type: str) -> Optional[str]:
        """
        从文件中提取文本内容
        
        Args:
            file_path: 文件路径
            file_type: 文件类型
            
        Returns:
            Optional[str]: 提取的文本内容
        """
        try:
            if file_type.startswith('text/') or file_path.endswith(('.py', '.js', '.ts', '.html', '.css', '.md', '.txt', '.json', '.csv')):
                # 文本文件直接读取
                if aiofiles:
                    async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                        return await f.read()
                else:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        return f.read()

            elif file_type == 'application/pdf' and PyPDF2:
                # PDF文件提取文本
                return await self._extract_pdf_text(file_path)

            elif file_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' and Document:
                # Word文档提取文本
                return await self._extract_docx_text(file_path)

            elif file_type.startswith('image/'):
                # 图片文件 - 返回基本信息，实际OCR可以后续添加
                return await self._get_image_info(file_path)
            
        except Exception as e:
            print(f"提取文件内容失败: {e}")
            return None
        
        return None
    
    async def _extract_pdf_text(self, file_path: str) -> str:
        """从PDF文件提取文本"""
        if not PyPDF2:
            return "PDF文件（需要安装PyPDF2库来提取文本内容）"

        text = ""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            print(f"PDF文本提取失败: {e}")
            return "PDF文件（文本提取失败）"
        return text.strip()

    async def _extract_docx_text(self, file_path: str) -> str:
        """从Word文档提取文本"""
        if not Document:
            return "Word文档（需要安装python-docx库来提取文本内容）"

        text = ""
        try:
            doc = Document(file_path)
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
        except Exception as e:
            print(f"Word文档文本提取失败: {e}")
            return "Word文档（文本提取失败）"
        return text.strip()

    async def _get_image_info(self, file_path: str) -> str:
        """获取图片基本信息"""
        if not Image:
            return "图片文件（需要安装Pillow库来获取详细信息）"

        try:
            with Image.open(file_path) as img:
                return f"图片信息: 尺寸 {img.size[0]}x{img.size[1]}, 格式 {img.format}, 模式 {img.mode}"
        except Exception as e:
            print(f"获取图片信息失败: {e}")
            return "图片文件"
    
    async def create_file_attachment(
        self, 
        message: Message, 
        file: UploadFile
    ) -> FileAttachment:
        """
        创建文件附件记录
        
        Args:
            message: 关联的消息
            file: 上传的文件
            
        Returns:
            FileAttachment: 创建的文件附件记录
        """
        # 验证文件
        self.validate_file(file)
        
        # 保存文件
        file_path, file_type = await self.save_file(file)
        
        # 提取文本内容
        extracted_text = await self.extract_text_content(file_path, file_type)
        
        # 创建数据库记录
        attachment = await FileAttachment.create(
            message=message,
            filename=file.filename or "unknown",
            file_path=file_path,
            file_size=file.size or 0,
            file_type=file_type,
            extracted_text=extracted_text
        )
        
        return attachment
    
    async def get_file_url(self, attachment: FileAttachment) -> str:
        """
        获取文件访问URL
        
        Args:
            attachment: 文件附件记录
            
        Returns:
            str: 文件访问URL
        """
        # 这里可以返回静态文件服务的URL
        # 实际部署时需要配置静态文件服务
        return f"/files/{Path(attachment.file_path).name}"


# 全局文件服务实例
file_service = FileService()
