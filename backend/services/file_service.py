"""
文件处理服务模块

该模块提供文件上传、存储、内容提取等功能，包括：
1. 文件上传和存储
2. 文件类型检测和验证
3. 文本内容提取（PDF、Word、图片OCR等）
4. 文件元数据管理

作者: jinglv
时间: 2025年1月
"""

import mimetypes
import uuid
from pathlib import Path
from typing import Optional, Tuple

try:
    import aiofiles
except ImportError:
    aiofiles = None

from fastapi import UploadFile, HTTPException

try:
    from PIL import Image
except ImportError:
    Image = None

try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

try:
    from docx import Document
except ImportError:
    Document = None

from models.chat import FileAttachment, Message


class FileService:
    """文件处理服务类"""

    def __init__(self, upload_dir: str = "uploads"):
        """
        初始化文件服务
        
        Args:
            upload_dir: 文件上传目录
        """
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)

        # 支持的文件类型
        self.supported_types = {
            'image': ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'document': ['application/pdf', 'text/plain', 'text/markdown'],
            'office': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            'code': ['text/javascript', 'text/typescript', 'text/html', 'text/css', 'application/json']
        }

        # 文件大小限制（10MB）
        self.max_file_size = 10 * 1024 * 1024

    async def validate_and_read_file(self, file: UploadFile) -> Tuple[bytes, str]:
        """
        验证文件并读取内容

        Args:
            file: 上传的文件

        Returns:
            Tuple[bytes, str]: (文件内容, 文件类型)

        Raises:
            HTTPException: 验证失败时抛出异常
        """
        content = None
        try:
            # 检查文件对象状态
            print(f"📖 开始读取文件: {file.filename}")
            print(f"🔍 文件对象状态: {type(file)}")

            # 尝试多种方式读取文件内容
            try:
                # 方法1: 直接读取
                content = await file.read()
                print(f"✅ 方法1成功: 直接读取 {len(content)} bytes")
            except Exception as e1:
                print(f"⚠️ 方法1失败: {e1}")

                try:
                    # 方法2: 检查文件是否有file属性
                    if hasattr(file, 'file') and file.file:
                        file.file.seek(0)
                        content = file.file.read()
                        if isinstance(content, str):
                            content = content.encode('utf-8')
                        print(f"✅ 方法2成功: 通过file属性读取 {len(content)} bytes")
                    else:
                        raise Exception("文件对象没有file属性")
                except Exception as e2:
                    print(f"⚠️ 方法2失败: {e2}")

                    try:
                        # 方法3: 尝试重新打开文件流
                        if hasattr(file, '_file') and file._file:
                            file._file.seek(0)
                            content = file._file.read()
                            if isinstance(content, str):
                                content = content.encode('utf-8')
                            print(f"✅ 方法3成功: 通过_file属性读取 {len(content)} bytes")
                        else:
                            raise Exception("无法访问文件流")
                    except Exception as e3:
                        print(f"❌ 方法3失败: {e3}")
                        raise Exception(f"所有读取方法都失败: {e1}, {e2}, {e3}")

            if content is None:
                raise Exception("文件内容为None")

            file_size = len(content)
            print(f"📖 最终读取文件内容成功: {file_size} bytes")

            if file_size == 0:
                raise HTTPException(
                    status_code=400,
                    detail="文件内容为空"
                )

            if file_size > self.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件大小超过限制 ({self.max_file_size / 1024 / 1024:.1f}MB)"
                )

        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            print(f"❌ 文件读取异常: {e}")
            import traceback
            traceback.print_exc()
            raise HTTPException(
                status_code=400,
                detail=f"文件读取失败: {str(e)}"
            )

        # 检查文件类型
        file_type = file.content_type
        if not file_type:
            # 尝试从文件名推断类型
            file_type, _ = mimetypes.guess_type(file.filename or "")

        print(f"🔍 文件类型: {file_type}")

        # 检查是否为支持的类型
        all_supported = []
        for types in self.supported_types.values():
            all_supported.extend(types)

        # 特殊处理一些常见的代码文件
        if file.filename:
            ext = Path(file.filename).suffix.lower()
            code_extensions = ['.py', '.js', '.ts', '.tsx', '.jsx', '.html', '.css', '.md', '.txt', '.json', '.csv']
            if ext in code_extensions:
                print(f"✅ 代码文件类型验证通过: {ext}")
                return content, file_type or 'text/plain'

        if file_type not in all_supported:
            raise HTTPException(
                status_code=415,
                detail=f"不支持的文件类型: {file_type}"
            )

        print(f"✅ 文件类型验证通过: {file_type}")
        return content, file_type

    async def save_file_content(self, content: bytes, filename: str, file_type: str) -> str:
        """
        保存文件内容到磁盘

        Args:
            content: 文件内容
            filename: 原始文件名
            file_type: 文件类型

        Returns:
            str: 保存的文件路径
        """
        try:
            # 生成唯一文件名
            file_id = str(uuid.uuid4())
            file_ext = Path(filename or "").suffix
            unique_filename = f"{file_id}{file_ext}"
            file_path = self.upload_dir / unique_filename

            # 确保上传目录存在
            self.upload_dir.mkdir(parents=True, exist_ok=True)

            # 保存文件内容到磁盘
            try:
                if aiofiles:
                    async with aiofiles.open(file_path, 'wb') as f:
                        await f.write(content)
                else:
                    # 如果aiofiles不可用，使用同步方式
                    with open(file_path, 'wb') as f:
                        f.write(content)

                print(f"💾 文件写入磁盘成功: {file_path}")
            except Exception as write_error:
                print(f"❌ 文件写入失败: {write_error}")
                raise Exception(f"文件写入失败: {write_error}")

            # 验证文件是否成功保存
            if not file_path.exists():
                raise Exception(f"文件保存后不存在: {file_path}")

            saved_size = file_path.stat().st_size
            if saved_size == 0:
                raise Exception(f"保存的文件大小为0: {file_path}")

            if saved_size != len(content):
                print(f"⚠️ 文件大小不匹配: 原始={len(content)}, 保存={saved_size}")

            print(f"✅ 文件保存成功: {file_path} (大小: {saved_size} bytes)")
            return str(file_path)

        except Exception as e:
            print(f"❌ 文件保存失败: {e}")
            # 清理可能创建的文件
            try:
                if 'file_path' in locals() and Path(file_path).exists():
                    Path(file_path).unlink()
                    print(f"🗑️ 已清理失败的文件: {file_path}")
            except:
                pass
            raise HTTPException(status_code=500, detail=f"文件保存失败: {str(e)}")

    async def extract_text_content(self, file_path: str, file_type: str) -> Optional[str]:
        """
        从文件中提取文本内容
        
        Args:
            file_path: 文件路径
            file_type: 文件类型
            
        Returns:
            Optional[str]: 提取的文本内容
        """
        try:
            # 检查文件是否存在
            if not Path(file_path).exists():
                print(f"❌ 文件不存在: {file_path}")
                return None

            print(f"🔍 开始提取文件内容: {file_path} (类型: {file_type})")

            if file_type.startswith('text/') or file_path.endswith(
                    ('.py', '.js', '.ts', '.html', '.css', '.md', '.txt', '.json', '.csv')):
                # 文本文件直接读取
                try:
                    if aiofiles:
                        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                            content = await f.read()
                    else:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                    print(f"✅ 文本文件读取成功，共 {len(content)} 字符")
                    return content
                except UnicodeDecodeError:
                    # 如果UTF-8解码失败，尝试其他编码
                    try:
                        with open(file_path, 'r', encoding='gbk') as f:
                            content = f.read()
                        print(f"✅ 文本文件读取成功（GBK编码），共 {len(content)} 字符")
                        return content
                    except Exception as e:
                        print(f"❌ 文本文件读取失败: {e}")
                        return None

            elif file_type == 'application/pdf' and PyPDF2:
                # PDF文件提取文本
                return await self._extract_pdf_text(file_path)

            elif file_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' and Document:
                # Word文档提取文本
                return await self._extract_docx_text(file_path)

            elif file_type.startswith('image/'):
                # 图片文件 - 返回基本信息，实际OCR可以后续添加
                return await self._get_image_info(file_path)
            else:
                print(f"⚠️ 不支持的文件类型: {file_type}")
                return f"文件类型 {file_type} 暂不支持内容提取"

        except Exception as e:
            print(f"❌ 提取文件内容失败: {e}")
            return None

    async def _extract_pdf_text(self, file_path: str) -> str:
        """从PDF文件提取文本"""
        if not PyPDF2:
            return "PDF文件（需要安装PyPDF2库来提取文本内容）"

        text = ""
        try:
            # 检查文件是否存在
            if not Path(file_path).exists():
                return f"PDF文件不存在: {file_path}"

            print(f"🔍 开始提取PDF文本: {file_path}")

            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                page_count = len(pdf_reader.pages)
                print(f"📄 PDF页数: {page_count}")

                for i, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text += page_text + "\n"
                        print(f"✅ 已处理第 {i+1}/{page_count} 页")
                    except Exception as page_error:
                        print(f"⚠️ 第 {i+1} 页提取失败: {page_error}")
                        continue

            if text.strip():
                print(f"✅ PDF文本提取成功，共 {len(text)} 字符")
            else:
                print("⚠️ PDF文本提取为空")
                return "PDF文件（未能提取到文本内容）"

        except Exception as e:
            print(f"❌ PDF文本提取失败: {e}")
            return f"PDF文件（文本提取失败: {str(e)}）"
        return text.strip()

    async def _extract_docx_text(self, file_path: str) -> str:
        """从Word文档提取文本"""
        if not Document:
            return "Word文档（需要安装python-docx库来提取文本内容）"

        text = ""
        try:
            doc = Document(file_path)
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
        except Exception as e:
            print(f"Word文档文本提取失败: {e}")
            return "Word文档（文本提取失败）"
        return text.strip()

    async def _get_image_info(self, file_path: str) -> str:
        """获取图片基本信息"""
        if not Image:
            return "图片文件（需要安装Pillow库来获取详细信息）"

        try:
            with Image.open(file_path) as img:
                return f"图片信息: 尺寸 {img.size[0]}x{img.size[1]}, 格式 {img.format}, 模式 {img.mode}"
        except Exception as e:
            print(f"获取图片信息失败: {e}")
            return "图片文件"

    async def create_file_attachment(
            self,
            message: Message,
            file: UploadFile
    ) -> FileAttachment:
        """
        创建文件附件记录

        Args:
            message: 关联的消息
            file: 上传的文件

        Returns:
            FileAttachment: 创建的文件附件记录
        """
        file_path = None
        try:
            print(f"🔄 开始处理文件: {file.filename}")

            # 验证文件并读取内容
            content, file_type = await self.validate_and_read_file(file)
            print(f"✅ 文件验证通过: {file.filename} (类型: {file_type})")

            # 保存文件内容
            file_path = await self.save_file_content(content, file.filename or "unknown", file_type)
            print(f"✅ 文件保存成功: {file_path}")

            # 提取文本内容
            extracted_text = await self.extract_text_content(file_path, file_type)
            if extracted_text:
                print(f"✅ 文本内容提取成功，长度: {len(extracted_text)} 字符")
            else:
                print("⚠️ 未能提取到文本内容")

            # 获取实际文件大小
            actual_file_size = Path(file_path).stat().st_size

            # 创建数据库记录
            attachment = await FileAttachment.create(
                message=message,
                filename=file.filename or "unknown",
                file_path=file_path,
                file_size=actual_file_size,
                file_type=file_type,
                extracted_text=extracted_text
            )

            print(f"✅ 文件附件记录创建成功: ID={attachment.id}")
            return attachment

        except Exception as e:
            print(f"❌ 创建文件附件失败: {e}")
            # 如果文件已保存但数据库记录创建失败，尝试清理文件
            try:
                if file_path and Path(file_path).exists():
                    Path(file_path).unlink()
                    print(f"🗑️ 已清理临时文件: {file_path}")
            except Exception as cleanup_error:
                print(f"⚠️ 清理临时文件失败: {cleanup_error}")
            raise

    async def get_file_url(self, attachment: FileAttachment) -> str:
        """
        获取文件访问URL
        
        Args:
            attachment: 文件附件记录
            
        Returns:
            str: 文件访问URL
        """
        # 这里可以返回静态文件服务的URL
        # 实际部署时需要配置静态文件服务
        return f"/files/{Path(attachment.file_path).name}"


# 全局文件服务实例
file_service = FileService()
