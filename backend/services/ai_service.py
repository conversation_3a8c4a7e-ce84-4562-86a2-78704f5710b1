"""
AI服务模块

该模块提供了与Autogen智能体的交互功能，支持：
1. 非流式输出 - 使用 agent.run() 方法等待完整响应
2. 流式输出 - 使用 agent.run_stream() 方法实时返回响应片段
3. 对话管理 - 创建、获取对话和消息历史
4. 自动标题生成 - 根据首条消息自动设置对话标题

作者: jinglv
时间: 2025年1月
"""

import time
from typing import AsyncGenerator, Dict, List, Optional, Any, Union

# Autogen相关导入
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient

# 项目内部导入
from config import settings
from models.chat import Conversation, Message, MessageRole, MessageStatus


class AIService:
    """
    AI服务类，负责处理与Autogen智能体的交互

    该类提供了完整的AI对话功能，包括：
    - 智能体初始化和配置
    - 非流式响应生成（使用agent.run方法）
    - 流式响应生成（使用agent.run_stream方法）
    - 对话和消息管理
    - 自动标题生成

    属性:
        agent: Autogen助手智能体实例
        group_chats: 群组聊天会话字典（预留功能）
    """

    def __init__(self):
        """
        初始化AI服务

        创建AI服务实例并初始化智能体
        """
        self.agent: str  # Autogen助手智能体
        self.group_chats: Dict[int, RoundRobinGroupChat] = {}  # 群组聊天会话（预留）
        self._initialize_agents()  # 初始化智能体

    def _initialize_agents(self):
        """
        初始化AI智能体

        根据配置创建OpenAI模型客户端和Autogen助手智能体。
        如果没有配置API密钥，将输出警告信息。

        异常处理:
            如果初始化失败，会打印错误信息但不会抛出异常
        """
        try:
            # 创建模型客户端列表
            model_clients = []

            # 检查是否配置了OpenAI API密钥
            if settings.openai_api_key:
                # 创建OpenAI聊天完成客户端
                model_client = OpenAIChatCompletionClient(
                    model=settings.default_model,  # 使用配置的默认模型
                    api_key=settings.openai_api_key,  # API密钥
                    base_url=settings.openai_base_url,  # API基础URL（可选）
                    model_info={
                        "vision": False,  # 不支持视觉功能
                        "function_calling": True,  # 支持函数调用
                        "json_output": True,  # 支持JSON输出
                        "family": ModelFamily.UNKNOWN,  # 模型家族
                        "structured_output": True,  # 支持结构化输出
                        "multiple_system_messages": True,  # 支持多个系统消息
                    }
                )
                model_clients.append(model_client)

            # 如果没有可用的模型客户端，使用模拟模式
            if not model_clients:
                print("警告: 未配置AI模型API密钥，将使用模拟模式")
                return

            # 创建Autogen助手智能体
            self.agent = AssistantAgent(
                name="chat_assistant",  # 智能体名称
                model_client=model_clients[0],  # 使用第一个模型客户端
                system_message="""你是一个有用的AI助手，能够回答各种问题并提供帮助。请用中文回复。

你具备以下能力：
1. 文本分析和理解
2. 图片内容识别和描述
3. 文档内容分析（PDF、Word等）
4. 代码分析和解释
5. 数据文件处理（CSV、JSON等）

当用户上传文件时，你会收到文件的内容信息，请根据文件类型和内容提供相应的分析和帮助。""",  # 系统提示
                model_client_stream=True  # 启用流式输出支持
            )
            print(f"✅ Autogen Agent初始化成功，使用模型: {settings.default_model}")
        except Exception as e:
            print(f"初始化AI Agents失败: {e}")

    async def create_conversation(self, title: str = "新对话") -> Conversation:
        """
        创建新对话

        参数:
            title: 对话标题，默认为"新对话"

        返回:
            Conversation: 创建的对话实例
        """
        conversation = await Conversation.create(title=title)
        return conversation

    async def get_conversation(self, conversation_id: int) -> Optional[Conversation]:
        """
        根据ID获取对话

        参数:
            conversation_id: 对话ID

        返回:
            Optional[Conversation]: 对话实例，如果不存在则返回None
        """
        return await Conversation.get_or_none(id=conversation_id)

    async def get_conversation_messages(self, conversation_id: int) -> List[Message]:
        """
        获取对话的消息历史

        参数:
            conversation_id: 对话ID

        返回:
            List[Message]: 按创建时间排序的消息列表
        """
        return await Message.filter(conversation_id=conversation_id).order_by("created_at")

    async def send_message(
            self,
            conversation_id: int,
            content: str,
            role: MessageRole = MessageRole.USER
    ) -> Message:
        """
        发送消息到指定对话

        参数:
            conversation_id: 对话ID
            content: 消息内容
            role: 消息角色，默认为用户角色

        返回:
            Message: 创建的消息实例

        异常:
            ValueError: 当对话不存在时抛出
        """
        # 检查对话是否存在
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError(f"对话 {conversation_id} 不存在")

        # 创建并保存消息
        user_message = await Message.create(
            conversation=conversation,
            role=role,
            content=content,
            status=MessageStatus.COMPLETED  # 用户消息直接标记为完成
        )

        # 如果是用户消息，检查是否需要自动设置对话标题
        if role == MessageRole.USER:
            await self._auto_set_conversation_title(conversation, content)

        return user_message

    async def _auto_set_conversation_title(self, conversation: Conversation, user_message: str):
        """
        根据第一条用户消息自动设置对话标题

        当对话标题为默认值"新对话"且消息数量较少时，
        会自动使用用户的第一条消息作为对话标题。

        参数:
            conversation: 对话实例
            user_message: 用户消息内容
        """
        # 检查是否是第一条用户消息（消息数量<=1且标题为默认值）
        message_count = await Message.filter(conversation=conversation).count()
        if message_count <= 1 and conversation.title == "新对话":
            # 生成简洁的标题（限制长度为30个字符）
            title = user_message.strip()
            if len(title) > 30:
                title = title[:30] + "..."

            # 清理标题：移除换行符和多余空格
            title = " ".join(title.split())

            # 更新对话标题并保存
            conversation.title = title
            await conversation.save()
            print(f"✅ 自动设置对话标题: {title}")

    async def generate_response(
            self,
            conversation_id: int,
            user_message: str
    ) -> Optional[Dict[str, Union[str, int, float, Any]]]:
        """
        生成非流式响应 - 使用 agent.run 方法

        这是非流式输出的实现，对应用户示例：
        result = await agent.run(task="请写一首七言绝句")
        print(result)

        该方法会等待智能体完成整个响应后一次性返回完整结果。
        适用于API调用、批量处理等需要完整结果的场景。

        参数:
            conversation_id: 对话ID
            user_message: 用户消息内容

        返回:
            Optional[Dict]: 包含响应信息的字典，包括：
                - message_id: 消息ID
                - conversation_id: 对话ID
                - content: 响应内容
                - processing_time: 处理时间
                - status: 处理状态

        异常:
            ValueError: 当对话不存在时抛出
        """
        # 验证对话是否存在
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError(f"对话 {conversation_id} 不存在")

        # 保存用户消息到数据库
        await self.send_message(conversation_id, user_message, MessageRole.USER)

        # 检查是否需要自动设置对话标题
        await self._auto_set_conversation_title(conversation, user_message)

        # 创建助手消息记录，初始状态为处理中
        assistant_message = await Message.create(
            conversation=conversation,
            role=MessageRole.ASSISTANT,
            content="",  # 初始内容为空
            status=MessageStatus.PROCESSING  # 标记为处理中
        )

        # 记录开始时间用于计算处理时长
        start_time = time.time()
        try:
            # 检查智能体是否已初始化
            if self.agent:
                # 使用 agent.run 方法进行非流式调用
                # 这里会等待智能体完成整个响应
                result = await self.agent.run(task=user_message)

                if result:
                    # 将结果转换为字符串
                    response_content = str(result)

                    # 计算处理时间
                    processing_time = time.time() - start_time

                    # 更新助手消息的信息
                    assistant_message.content = response_content
                    assistant_message.status = MessageStatus.COMPLETED
                    assistant_message.processing_time = processing_time
                    assistant_message.model_name = settings.default_model

                    # 如果结果包含token使用信息，记录token消耗
                    if isinstance(result, TextMessage) and result.models_usage is not None:
                        assistant_message.tokens_used = result.models_usage.completion_tokens

                    # 保存到数据库
                    await assistant_message.save()

                    # 返回响应结果
                    return {
                        "message_id": assistant_message.id,
                        "conversation_id": conversation_id,
                        "content": response_content,
                        "processing_time": processing_time,
                        "status": "completed"
                    }
        except Exception as autogen_error:
            print(f"Autogen非流式调用失败: {autogen_error}")

    async def generate_response_stream(
            self,
            conversation_id: int,
            user_message: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        生成流式响应 - 使用 agent.run_stream 方法

        这是流式输出的实现，对应用户示例：
        async def chat_stream():
            res = agent.run_stream(task="请写一首五言绝句")
            async for chunk in res:
                if isinstance(chunk, ModelClientStreamingChunkEvent):
                    print(chunk.content, end="", flush=True)

        该方法会实时返回智能体的响应片段，提供类似打字机的效果。
        适用于聊天界面、实时交互等需要即时反馈的场景。

        参数:
            conversation_id: 对话ID
            user_message: 用户消息内容

        生成:
            Dict[str, Any]: 流式响应数据，包括：
                - type: "content" | "complete" | "error"
                - content: 内容片段（仅当type为"content"时）
                - message_id: 消息ID
                - conversation_id: 对话ID
                - processing_time: 处理时间（仅当type为"complete"时）
                - error: 错误信息（仅当type为"error"时）

        异常:
            ValueError: 当对话不存在时抛出
        """
        # 验证对话是否存在
        conversation = await self.get_conversation(conversation_id)
        if not conversation:
            raise ValueError(f"对话 {conversation_id} 不存在")

        # 保存用户消息到数据库
        await self.send_message(conversation_id, user_message, MessageRole.USER)

        # 检查是否需要自动设置对话标题
        await self._auto_set_conversation_title(conversation, user_message)

        # 创建助手消息记录，初始状态为处理中
        assistant_message = await Message.create(
            conversation=conversation,
            role=MessageRole.ASSISTANT,
            content="",  # 初始内容为空，会逐步更新
            status=MessageStatus.PROCESSING  # 标记为处理中
        )
        # 记录开始时间用于计算处理时长
        start_time = time.time()
        try:
            # 获取对话的消息历史（用于上下文，预留功能）
            messages = await self.get_conversation_messages(conversation_id)

            # 构建对话历史（预留功能，当前版本暂不使用）
            chat_history = []
            for msg in messages[:-1]:  # 排除刚创建的assistant_message
                if msg.role != MessageRole.SYSTEM:
                    chat_history.append({
                        "role": msg.role.value,
                        "content": msg.content
                    })
            # 添加当前用户消息到历史
            chat_history.append({
                "role": "user",
                "content": user_message
            })

            # 用于累积响应内容
            response_content = ""

            # 尝试使用Autogen的真正流式输出
            if self.agent:
                try:
                    # 使用 agent.run_stream 方法进行真正的流式调用
                    # 这里会返回一个异步生成器，实时产生响应片段
                    stream_response = self.agent.run_stream(task=user_message)

                    # 遍历流式响应的每个片段
                    async for chunk in stream_response:
                        # 检查是否是流式内容事件
                        if isinstance(chunk, ModelClientStreamingChunkEvent):
                            # 处理流式内容块
                            content = chunk.content
                            if content:
                                # 累积响应内容
                                response_content += content

                                # 更新数据库中的消息内容
                                assistant_message.content = response_content
                                # 注意：这里注释掉频繁的数据库保存以提高性能
                                # await assistant_message.save()

                                # 向客户端发送内容片段
                                yield {
                                    "type": "content",
                                    "content": content,
                                    "message_id": assistant_message.id,
                                    "conversation_id": conversation_id
                                }
                        elif isinstance(chunk, TextMessage) and chunk.models_usage is not None:
                            # 如果包含token使用信息，记录token消耗
                            assistant_message.tokens_used = chunk.models_usage.completion_tokens

                    # 流式响应完成后的处理
                    processing_time = time.time() - start_time
                    assistant_message.status = MessageStatus.COMPLETED
                    assistant_message.processing_time = processing_time
                    assistant_message.model_name = settings.default_model

                    # 最终保存完整的消息到数据库
                    await assistant_message.save()

                    # 发送完成信号
                    yield {
                        "type": "complete",
                        "message_id": assistant_message.id,
                        "conversation_id": conversation_id,
                        "processing_time": processing_time
                    }
                    return
                except Exception as autogen_error:
                    print(f"Autogen流式调用失败: {autogen_error}")
        except Exception as e:
            # 处理流式响应过程中的错误
            assistant_message.status = MessageStatus.FAILED
            assistant_message.content = f"处理消息时发生错误: {str(e)}"
            await assistant_message.save()

            # 向客户端发送错误信息
            yield {
                "type": "error",
                "error": str(e),
                "message_id": assistant_message.id,
                "conversation_id": conversation_id
            }


# 创建全局AI服务实例
# 这个实例会在模块导入时自动初始化，供整个应用使用
ai_service = AIService()
