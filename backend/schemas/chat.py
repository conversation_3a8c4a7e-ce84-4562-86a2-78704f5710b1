# @Time：2025/6/9 10:56
# @Author：jinglv
# ==================== 数据模型定义 ====================
from typing import Optional

from pydantic import BaseModel, Field


class ChatRequest(BaseModel):
    """
    聊天请求模型

    用于接收客户端发送的聊天请求数据

    属性:
        message: 用户发送的消息内容
        conversation_id: 对话ID，可选，如果不提供则创建新对话
    """
    message: str = Field(..., description="用户发送的消息内容")
    conversation_id: Optional[int] = Field(None, description="对话ID，可选，如果不提供则创建新对话")


class ChatResponse(BaseModel):
    """
    聊天响应模型

    用于返回聊天响应数据（预留模型，当前未使用）

    属性:
        message_id: 消息ID
        conversation_id: 对话ID
        content: 消息内容
        role: 消息角色
        created_at: 创建时间
    """
    message_id: int = Field(..., description="消息ID")
    conversation_id: int = Field(..., description="对话ID")
    content: str = Field(..., description="消息内容")
    role: str = Field(..., description="消息角色")
    created_at: str = Field(..., description="创建时间")


class ConversationResponse(BaseModel):
    """
    对话响应模型

    用于返回对话信息

    属性:
        id: 对话ID
        title: 对话标题
        created_at: 创建时间
        updated_at: 更新时间
        message_count: 消息数量
    """
    id: int = Field(..., description="对话ID")
    title: str = Field(..., description="对话标题")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")
    message_count: int = Field(..., description="消息数量")


class MessageResponse(BaseModel):
    """
    消息响应模型

    用于返回消息详细信息

    属性:
        id: 消息ID
        role: 消息角色（user/assistant/system）
        content: 消息内容
        status: 消息状态（pending/processing/completed/failed）
        created_at: 创建时间
        model_name: 使用的AI模型名称（可选）
        processing_time: 处理时间（可选）
        tokens_used: 使用的token数量（可选）
    """
    id: int = Field(..., description="消息ID")
    role: str = Field(..., description="消息角色（user/assistant/system）")
    content: str = Field(..., description="消息内容")
    status: str = Field(..., description="消息状态（pending/processing/completed/failed）")
    created_at: str = Field(..., description="创建时间")
    model_name: Optional[str] = Field(None, description="使用的AI模型名称（可选）")
    processing_time: Optional[float] = Field(None, description="处理时间（可选）")
    tokens_used: Optional[int] = Field(None, description="使用的token数量（可选）")
