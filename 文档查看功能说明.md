# 文档查看功能说明

## 功能概述

已重新实现文档查看功能，现在用户可以：
1. **点击已上传的文档**查看文档内容
2. **在页面右侧显示文档查看面板**（而不是模态框）
3. **查看原始文件的完整内容**（而不仅仅是提取的文本）

## 功能特性

### 🎯 核心功能
- **点击查看**: 点击聊天中的文档附件即可查看内容
- **侧边面板**: 在页面右侧显示文档内容，不遮挡聊天界面
- **原始内容**: 显示文件的原始内容，保持格式
- **多文件支持**: 支持文本、代码、JSON、CSV等多种文件类型

### 🎨 界面设计
- **流畅动画**: 面板打开/关闭有平滑的过渡动画
- **响应式布局**: 聊天区域自动调整宽度适应面板
- **现代化UI**: 简洁美观的界面设计
- **加载状态**: 文档加载时显示加载指示器

### 📁 支持的文件类型
- **文本文件**: .txt, .md, .csv
- **代码文件**: .py, .js, .ts, .html, .css, .json
- **文档文件**: .pdf, .docx（需要相应的Python库）

## 实现细节

### 后端实现

#### 1. API端点优化
```python
@router.get("/files/{attachment_id}/content")
async def get_file_content(attachment_id: int):
    # 返回原始文件内容而不仅仅是提取的文本
    return {
        "original_content": original_content,  # 新增原始内容
        "extracted_text": attachment.extracted_text,
        # ... 其他信息
    }
```

#### 2. 文件内容读取
- **文本文件**: 直接读取UTF-8编码内容
- **PDF文件**: 使用PyPDF2提取文本
- **Word文档**: 使用python-docx提取文本
- **其他类型**: 显示友好的提示信息

### 前端实现

#### 1. 页面布局调整
```tsx
// 主聊天区域根据面板状态调整宽度
<div className={`flex-1 flex flex-col ${isDocumentPanelOpen ? 'mr-96' : ''} transition-all duration-300`}>
```

#### 2. 文档查看面板
```tsx
// 固定在页面右侧的面板
<div className="fixed right-0 top-0 h-full w-96 bg-white border-l border-gray-200 shadow-lg z-40">
```

#### 3. 文档附件交互
- **整个文档区域可点击**
- **悬停效果**增强用户体验
- **加载状态**提供即时反馈

## 使用流程

### 1. 上传文档
1. 在聊天输入框中选择文件
2. 输入消息并发送
3. 文件上传并显示在聊天中

### 2. 查看文档
1. 点击聊天中的文档附件
2. 页面右侧出现文档查看面板
3. 面板中显示文档的完整内容

### 3. 操作文档
1. **滚动查看**: 在面板中滚动查看长文档
2. **复制内容**: 点击复制按钮复制文档内容
3. **关闭面板**: 点击关闭按钮或X图标关闭面板

## 界面截图说明

### 文档上传后的显示
```
┌─────────────────────────────────────────┐
│ 用户消息                                │
│ ┌─────────────────────────────────────┐ │
│ │ 📄 test_document.md                 │ │
│ │ 1.2 KB • 点击查看                   │ │
│ └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 点击后的面板显示
```
┌─────────────────────┬─────────────────────┐
│ 聊天区域            │ 文档查看面板        │
│                     │ ┌─────────────────┐ │
│ 用户消息            │ │ test_document.md│ │
│ AI响应              │ │ 1.2 KB • MD     │ │
│                     │ ├─────────────────┤ │
│                     │ │ # 测试文档      │ │
│                     │ │                 │ │
│                     │ │ 这是一个测试... │ │
│                     │ │                 │ │
│                     │ └─────────────────┘ │
└─────────────────────┴─────────────────────┘
```

## 技术优势

### 1. 用户体验
- **非阻塞式查看**: 不遮挡聊天界面
- **快速访问**: 一键查看文档内容
- **保持上下文**: 可以边查看文档边继续聊天

### 2. 性能优化
- **按需加载**: 只在点击时加载文档内容
- **缓存机制**: 避免重复请求
- **流畅动画**: 使用CSS transition优化体验

### 3. 兼容性
- **向后兼容**: 不影响现有的文件上传功能
- **渐进增强**: 在不支持的文件类型时显示友好提示
- **错误处理**: 完善的错误处理和用户反馈

## 测试验证

### 运行测试
```bash
# 启动后端服务
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 运行测试脚本
python test_document_viewer.py
```

### 测试内容
1. **基础功能测试**: 上传文档并查看内容
2. **多文件类型测试**: 测试不同格式的文件
3. **界面交互测试**: 测试面板的打开/关闭
4. **内容完整性测试**: 验证显示的内容是否完整

## 注意事项

### 1. 文件大小限制
- 建议文档大小不超过10MB
- 超大文档可能影响加载速度

### 2. 文件格式支持
- 主要支持文本类文件
- 二进制文件（如图片、视频）不支持内容预览

### 3. 浏览器兼容性
- 需要现代浏览器支持
- 建议使用Chrome、Firefox、Safari等

## 后续优化计划

### 1. 功能增强
- [ ] 添加文档搜索功能
- [ ] 支持文档高亮显示
- [ ] 添加文档目录导航

### 2. 性能优化
- [ ] 实现虚拟滚动处理大文档
- [ ] 添加文档内容缓存
- [ ] 优化加载速度

### 3. 用户体验
- [ ] 添加键盘快捷键
- [ ] 支持文档打印功能
- [ ] 添加全屏查看模式

## 总结

新的文档查看功能提供了更好的用户体验：
- ✅ **直观的交互**: 点击文档即可查看
- ✅ **非阻塞式设计**: 不影响聊天流程
- ✅ **完整内容显示**: 查看原始文件内容
- ✅ **现代化界面**: 美观流畅的用户界面

这个功能让用户可以更方便地查看和分析上传的文档，提升了整体的使用体验。
