#!/usr/bin/env python3
"""
简单的文件上传测试
"""

import requests
import json
import tempfile
import os

def create_test_file():
    """创建测试文件"""
    content = """这是一个测试文档。

# 测试标题

这个文档用于测试文件上传功能。

## 功能列表
1. 文件上传
2. 文本提取  
3. AI分析

```python
def hello_world():
    print("Hello, World!")
```

测试完成。
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(content)
        return f.name

def test_upload():
    """测试文件上传"""
    
    # 检查后端是否运行
    try:
        response = requests.get('http://localhost:8000/api/chat/health', timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        print("请确保后端服务已启动: cd backend && uvicorn main:app --reload")
        return
    
    # 创建对话
    print("\n🔄 创建对话...")
    try:
        response = requests.post(
            'http://localhost:8000/api/chat/conversations',
            json={'title': '文件上传测试'},
            timeout=10
        )
        
        if response.status_code == 200:
            conversation = response.json()
            conversation_id = conversation['id']
            print(f"✅ 对话创建成功: ID={conversation_id}")
        else:
            print(f"❌ 创建对话失败: {response.status_code}")
            print(response.text)
            return
    except Exception as e:
        print(f"❌ 创建对话异常: {e}")
        return
    
    # 创建测试文件
    test_file_path = create_test_file()
    print(f"📝 创建测试文件: {test_file_path}")
    
    try:
        # 上传文件
        print("\n🔄 上传文件...")
        
        with open(test_file_path, 'rb') as f:
            files = {
                'files': (os.path.basename(test_file_path), f, 'text/plain')
            }
            
            data = {
                'message': '请分析这个文档的内容',
                'conversation_id': conversation_id
            }
            
            response = requests.post(
                'http://localhost:8000/api/chat/stream-with-files',
                files=files,
                data=data,
                stream=True,
                timeout=30
            )
            
            if response.status_code == 200:
                print("✅ 文件上传成功，开始接收响应...")
                
                # 读取流式响应
                response_content = ""
                for line in response.iter_lines():
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            data_str = line[6:]
                            if data_str == '[DONE]':
                                print("\n✅ 响应完成")
                                break
                            
                            try:
                                chunk = json.loads(data_str)
                                if chunk.get('type') == 'content':
                                    content = chunk.get('content', '')
                                    print(content, end='', flush=True)
                                    response_content += content
                                elif chunk.get('type') == 'complete':
                                    print(f"\n✅ AI响应完成")
                                elif chunk.get('type') == 'error':
                                    print(f"\n❌ AI响应错误: {chunk.get('error')}")
                            except json.JSONDecodeError:
                                print(f"\n⚠️ 无法解析数据: {data_str}")
                
                if response_content:
                    print(f"\n📄 AI响应长度: {len(response_content)} 字符")
                
            else:
                print(f"❌ 文件上传失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        # 检查消息
        print("\n🔄 检查对话消息...")
        response = requests.get(
            f'http://localhost:8000/api/chat/conversations/{conversation_id}/messages',
            timeout=10
        )
        
        if response.status_code == 200:
            messages = response.json()
            print(f"✅ 获取到 {len(messages)} 条消息")
            
            for msg in messages:
                print(f"\n📝 消息 {msg['id']} ({msg['role']}):")
                print(f"内容: {msg['content'][:100]}...")
                
                if msg.get('attachments'):
                    print(f"📎 附件: {len(msg['attachments'])} 个")
                    for att in msg['attachments']:
                        print(f"  - {att['filename']} ({att['file_size']} bytes)")
                        
                        # 检查文件是否真的存在
                        file_path = f"backend/{att['file_path']}"
                        if os.path.exists(file_path):
                            print(f"    ✅ 文件存在: {file_path}")
                        else:
                            print(f"    ❌ 文件不存在: {file_path}")
        else:
            print(f"❌ 获取消息失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理测试文件
        try:
            os.unlink(test_file_path)
            print(f"\n🗑️ 已清理测试文件: {test_file_path}")
        except:
            pass

if __name__ == "__main__":
    print("🚀 开始文件上传测试")
    print("="*50)
    test_upload()
    print("\n" + "="*50)
    print("✅ 测试完成")
