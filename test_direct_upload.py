#!/usr/bin/env python3
"""
直接测试文件上传API
"""

import requests
import json

def test_direct_upload():
    """直接测试文件上传"""
    
    # 检查后端服务
    try:
        response = requests.get('http://localhost:8000/api/chat/health', timeout=5)
        if response.status_code != 200:
            print(f"❌ 后端服务异常: {response.status_code}")
            return
        print("✅ 后端服务正常")
    except Exception as e:
        print(f"❌ 无法连接后端: {e}")
        return
    
    # 创建对话
    print("\n🔄 创建对话...")
    try:
        response = requests.post(
            'http://localhost:8000/api/chat/conversations',
            json={'title': '直接上传测试'},
            timeout=10
        )
        
        if response.status_code == 200:
            conversation = response.json()
            conversation_id = conversation['id']
            print(f"✅ 对话创建成功: ID={conversation_id}")
        else:
            print(f"❌ 创建对话失败: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"❌ 创建对话异常: {e}")
        return
    
    # 创建小文件测试
    print("\n🔄 测试小文件上传...")
    small_content = "这是一个小测试文件。\n包含一些中文内容。\n测试完成。"
    
    try:
        files = {
            'files': ('small_test.txt', small_content.encode('utf-8'), 'text/plain')
        }
        
        data = {
            'message': '请分析这个小文件',
            'conversation_id': conversation_id
        }
        
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            stream=True,
            timeout=30
        )
        
        print(f"📡 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 小文件上传成功")
            
            # 读取一些响应
            line_count = 0
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        if data_str == '[DONE]':
                            print("\n✅ 响应完成")
                            break
                        
                        try:
                            chunk = json.loads(data_str)
                            if chunk.get('type') == 'content':
                                print(chunk.get('content', ''), end='', flush=True)
                            elif chunk.get('type') == 'error':
                                print(f"\n❌ 错误: {chunk.get('error')}")
                                break
                        except:
                            pass
                        
                        line_count += 1
                        if line_count > 50:  # 限制输出行数
                            print("\n... (输出截断)")
                            break
        else:
            print(f"❌ 小文件上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 小文件测试异常: {e}")
    
    # 测试中等大小文件
    print("\n\n🔄 测试中等文件上传...")
    medium_content = "这是一个中等大小的测试文件。\n" * 1000  # 约30KB
    
    try:
        files = {
            'files': ('medium_test.txt', medium_content.encode('utf-8'), 'text/plain')
        }
        
        data = {
            'message': '请分析这个中等文件',
            'conversation_id': conversation_id
        }
        
        response = requests.post(
            'http://localhost:8000/api/chat/stream-with-files',
            files=files,
            data=data,
            stream=True,
            timeout=30
        )
        
        print(f"📡 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 中等文件上传成功")
            
            # 只读取前几行响应
            line_count = 0
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        if data_str == '[DONE]':
                            print("\n✅ 响应完成")
                            break
                        
                        try:
                            chunk = json.loads(data_str)
                            if chunk.get('type') == 'content':
                                print(".", end='', flush=True)  # 只显示点表示进度
                            elif chunk.get('type') == 'error':
                                print(f"\n❌ 错误: {chunk.get('error')}")
                                break
                        except:
                            pass
                        
                        line_count += 1
                        if line_count > 20:  # 限制输出
                            print("\n... (响应继续)")
                            break
        else:
            print(f"❌ 中等文件上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 中等文件测试异常: {e}")
    
    # 检查最终结果
    print("\n\n🔄 检查上传结果...")
    try:
        response = requests.get(
            f'http://localhost:8000/api/chat/conversations/{conversation_id}/messages',
            timeout=10
        )
        
        if response.status_code == 200:
            messages = response.json()
            print(f"✅ 获取到 {len(messages)} 条消息")
            
            attachment_count = 0
            for msg in messages:
                if msg.get('attachments'):
                    attachment_count += len(msg['attachments'])
                    for att in msg['attachments']:
                        print(f"📎 附件: {att['filename']} ({att['file_size']} bytes)")
            
            print(f"📊 总附件数: {attachment_count}")
        else:
            print(f"❌ 获取消息失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查结果异常: {e}")

if __name__ == "__main__":
    print("🚀 开始直接文件上传测试")
    print("="*50)
    test_direct_upload()
    print("\n" + "="*50)
    print("✅ 测试完成")
